<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.plan.mapper.TestPlanReportSummaryMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.plan.domain.TestPlanReportSummary">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="functional_case_count" jdbcType="BIGINT" property="functionalCaseCount" />
    <result column="api_case_count" jdbcType="BIGINT" property="apiCaseCount" />
    <result column="api_scenario_count" jdbcType="BIGINT" property="apiScenarioCount" />
    <result column="bug_count" jdbcType="BIGINT" property="bugCount" />
    <result column="test_plan_report_id" jdbcType="VARCHAR" property="testPlanReportId" />
    <result column="plan_count" jdbcType="BIGINT" property="planCount" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="io.metersphere.plan.domain.TestPlanReportSummary">
    <result column="summary" jdbcType="LONGVARCHAR" property="summary" />
    <result column="functional_execute_result" jdbcType="LONGVARBINARY" property="functionalExecuteResult" />
    <result column="api_execute_result" jdbcType="LONGVARBINARY" property="apiExecuteResult" />
    <result column="scenario_execute_result" jdbcType="LONGVARBINARY" property="scenarioExecuteResult" />
    <result column="execute_result" jdbcType="LONGVARBINARY" property="executeResult" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, functional_case_count, api_case_count, api_scenario_count, bug_count, test_plan_report_id, 
    plan_count
  </sql>
  <sql id="Blob_Column_List">
    summary, functional_execute_result, api_execute_result, scenario_execute_result, 
    execute_result
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="io.metersphere.plan.domain.TestPlanReportSummaryExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from test_plan_report_summary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="io.metersphere.plan.domain.TestPlanReportSummaryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from test_plan_report_summary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from test_plan_report_summary
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from test_plan_report_summary
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.plan.domain.TestPlanReportSummaryExample">
    delete from test_plan_report_summary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.plan.domain.TestPlanReportSummary">
    insert into test_plan_report_summary (id, functional_case_count, api_case_count, 
      api_scenario_count, bug_count, test_plan_report_id, 
      plan_count, summary, functional_execute_result, 
      api_execute_result, scenario_execute_result, 
      execute_result)
    values (#{id,jdbcType=VARCHAR}, #{functionalCaseCount,jdbcType=BIGINT}, #{apiCaseCount,jdbcType=BIGINT}, 
      #{apiScenarioCount,jdbcType=BIGINT}, #{bugCount,jdbcType=BIGINT}, #{testPlanReportId,jdbcType=VARCHAR}, 
      #{planCount,jdbcType=BIGINT}, #{summary,jdbcType=LONGVARCHAR}, #{functionalExecuteResult,jdbcType=LONGVARBINARY}, 
      #{apiExecuteResult,jdbcType=LONGVARBINARY}, #{scenarioExecuteResult,jdbcType=LONGVARBINARY}, 
      #{executeResult,jdbcType=LONGVARBINARY})
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.plan.domain.TestPlanReportSummary">
    insert into test_plan_report_summary
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="functionalCaseCount != null">
        functional_case_count,
      </if>
      <if test="apiCaseCount != null">
        api_case_count,
      </if>
      <if test="apiScenarioCount != null">
        api_scenario_count,
      </if>
      <if test="bugCount != null">
        bug_count,
      </if>
      <if test="testPlanReportId != null">
        test_plan_report_id,
      </if>
      <if test="planCount != null">
        plan_count,
      </if>
      <if test="summary != null">
        summary,
      </if>
      <if test="functionalExecuteResult != null">
        functional_execute_result,
      </if>
      <if test="apiExecuteResult != null">
        api_execute_result,
      </if>
      <if test="scenarioExecuteResult != null">
        scenario_execute_result,
      </if>
      <if test="executeResult != null">
        execute_result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="functionalCaseCount != null">
        #{functionalCaseCount,jdbcType=BIGINT},
      </if>
      <if test="apiCaseCount != null">
        #{apiCaseCount,jdbcType=BIGINT},
      </if>
      <if test="apiScenarioCount != null">
        #{apiScenarioCount,jdbcType=BIGINT},
      </if>
      <if test="bugCount != null">
        #{bugCount,jdbcType=BIGINT},
      </if>
      <if test="testPlanReportId != null">
        #{testPlanReportId,jdbcType=VARCHAR},
      </if>
      <if test="planCount != null">
        #{planCount,jdbcType=BIGINT},
      </if>
      <if test="summary != null">
        #{summary,jdbcType=LONGVARCHAR},
      </if>
      <if test="functionalExecuteResult != null">
        #{functionalExecuteResult,jdbcType=LONGVARBINARY},
      </if>
      <if test="apiExecuteResult != null">
        #{apiExecuteResult,jdbcType=LONGVARBINARY},
      </if>
      <if test="scenarioExecuteResult != null">
        #{scenarioExecuteResult,jdbcType=LONGVARBINARY},
      </if>
      <if test="executeResult != null">
        #{executeResult,jdbcType=LONGVARBINARY},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.plan.domain.TestPlanReportSummaryExample" resultType="java.lang.Long">
    select count(*) from test_plan_report_summary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update test_plan_report_summary
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.functionalCaseCount != null">
        functional_case_count = #{record.functionalCaseCount,jdbcType=BIGINT},
      </if>
      <if test="record.apiCaseCount != null">
        api_case_count = #{record.apiCaseCount,jdbcType=BIGINT},
      </if>
      <if test="record.apiScenarioCount != null">
        api_scenario_count = #{record.apiScenarioCount,jdbcType=BIGINT},
      </if>
      <if test="record.bugCount != null">
        bug_count = #{record.bugCount,jdbcType=BIGINT},
      </if>
      <if test="record.testPlanReportId != null">
        test_plan_report_id = #{record.testPlanReportId,jdbcType=VARCHAR},
      </if>
      <if test="record.planCount != null">
        plan_count = #{record.planCount,jdbcType=BIGINT},
      </if>
      <if test="record.summary != null">
        summary = #{record.summary,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.functionalExecuteResult != null">
        functional_execute_result = #{record.functionalExecuteResult,jdbcType=LONGVARBINARY},
      </if>
      <if test="record.apiExecuteResult != null">
        api_execute_result = #{record.apiExecuteResult,jdbcType=LONGVARBINARY},
      </if>
      <if test="record.scenarioExecuteResult != null">
        scenario_execute_result = #{record.scenarioExecuteResult,jdbcType=LONGVARBINARY},
      </if>
      <if test="record.executeResult != null">
        execute_result = #{record.executeResult,jdbcType=LONGVARBINARY},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update test_plan_report_summary
    set id = #{record.id,jdbcType=VARCHAR},
      functional_case_count = #{record.functionalCaseCount,jdbcType=BIGINT},
      api_case_count = #{record.apiCaseCount,jdbcType=BIGINT},
      api_scenario_count = #{record.apiScenarioCount,jdbcType=BIGINT},
      bug_count = #{record.bugCount,jdbcType=BIGINT},
      test_plan_report_id = #{record.testPlanReportId,jdbcType=VARCHAR},
      plan_count = #{record.planCount,jdbcType=BIGINT},
      summary = #{record.summary,jdbcType=LONGVARCHAR},
      functional_execute_result = #{record.functionalExecuteResult,jdbcType=LONGVARBINARY},
      api_execute_result = #{record.apiExecuteResult,jdbcType=LONGVARBINARY},
      scenario_execute_result = #{record.scenarioExecuteResult,jdbcType=LONGVARBINARY},
      execute_result = #{record.executeResult,jdbcType=LONGVARBINARY}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update test_plan_report_summary
    set id = #{record.id,jdbcType=VARCHAR},
      functional_case_count = #{record.functionalCaseCount,jdbcType=BIGINT},
      api_case_count = #{record.apiCaseCount,jdbcType=BIGINT},
      api_scenario_count = #{record.apiScenarioCount,jdbcType=BIGINT},
      bug_count = #{record.bugCount,jdbcType=BIGINT},
      test_plan_report_id = #{record.testPlanReportId,jdbcType=VARCHAR},
      plan_count = #{record.planCount,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.plan.domain.TestPlanReportSummary">
    update test_plan_report_summary
    <set>
      <if test="functionalCaseCount != null">
        functional_case_count = #{functionalCaseCount,jdbcType=BIGINT},
      </if>
      <if test="apiCaseCount != null">
        api_case_count = #{apiCaseCount,jdbcType=BIGINT},
      </if>
      <if test="apiScenarioCount != null">
        api_scenario_count = #{apiScenarioCount,jdbcType=BIGINT},
      </if>
      <if test="bugCount != null">
        bug_count = #{bugCount,jdbcType=BIGINT},
      </if>
      <if test="testPlanReportId != null">
        test_plan_report_id = #{testPlanReportId,jdbcType=VARCHAR},
      </if>
      <if test="planCount != null">
        plan_count = #{planCount,jdbcType=BIGINT},
      </if>
      <if test="summary != null">
        summary = #{summary,jdbcType=LONGVARCHAR},
      </if>
      <if test="functionalExecuteResult != null">
        functional_execute_result = #{functionalExecuteResult,jdbcType=LONGVARBINARY},
      </if>
      <if test="apiExecuteResult != null">
        api_execute_result = #{apiExecuteResult,jdbcType=LONGVARBINARY},
      </if>
      <if test="scenarioExecuteResult != null">
        scenario_execute_result = #{scenarioExecuteResult,jdbcType=LONGVARBINARY},
      </if>
      <if test="executeResult != null">
        execute_result = #{executeResult,jdbcType=LONGVARBINARY},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="io.metersphere.plan.domain.TestPlanReportSummary">
    update test_plan_report_summary
    set functional_case_count = #{functionalCaseCount,jdbcType=BIGINT},
      api_case_count = #{apiCaseCount,jdbcType=BIGINT},
      api_scenario_count = #{apiScenarioCount,jdbcType=BIGINT},
      bug_count = #{bugCount,jdbcType=BIGINT},
      test_plan_report_id = #{testPlanReportId,jdbcType=VARCHAR},
      plan_count = #{planCount,jdbcType=BIGINT},
      summary = #{summary,jdbcType=LONGVARCHAR},
      functional_execute_result = #{functionalExecuteResult,jdbcType=LONGVARBINARY},
      api_execute_result = #{apiExecuteResult,jdbcType=LONGVARBINARY},
      scenario_execute_result = #{scenarioExecuteResult,jdbcType=LONGVARBINARY},
      execute_result = #{executeResult,jdbcType=LONGVARBINARY}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.plan.domain.TestPlanReportSummary">
    update test_plan_report_summary
    set functional_case_count = #{functionalCaseCount,jdbcType=BIGINT},
      api_case_count = #{apiCaseCount,jdbcType=BIGINT},
      api_scenario_count = #{apiScenarioCount,jdbcType=BIGINT},
      bug_count = #{bugCount,jdbcType=BIGINT},
      test_plan_report_id = #{testPlanReportId,jdbcType=VARCHAR},
      plan_count = #{planCount,jdbcType=BIGINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into test_plan_report_summary
    (id, functional_case_count, api_case_count, api_scenario_count, bug_count, test_plan_report_id, 
      plan_count, summary, functional_execute_result, api_execute_result, scenario_execute_result, 
      execute_result)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.functionalCaseCount,jdbcType=BIGINT}, #{item.apiCaseCount,jdbcType=BIGINT}, 
        #{item.apiScenarioCount,jdbcType=BIGINT}, #{item.bugCount,jdbcType=BIGINT}, #{item.testPlanReportId,jdbcType=VARCHAR}, 
        #{item.planCount,jdbcType=BIGINT}, #{item.summary,jdbcType=LONGVARCHAR}, #{item.functionalExecuteResult,jdbcType=LONGVARBINARY}, 
        #{item.apiExecuteResult,jdbcType=LONGVARBINARY}, #{item.scenarioExecuteResult,jdbcType=LONGVARBINARY}, 
        #{item.executeResult,jdbcType=LONGVARBINARY})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into test_plan_report_summary (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'functional_case_count'.toString() == column.value">
          #{item.functionalCaseCount,jdbcType=BIGINT}
        </if>
        <if test="'api_case_count'.toString() == column.value">
          #{item.apiCaseCount,jdbcType=BIGINT}
        </if>
        <if test="'api_scenario_count'.toString() == column.value">
          #{item.apiScenarioCount,jdbcType=BIGINT}
        </if>
        <if test="'bug_count'.toString() == column.value">
          #{item.bugCount,jdbcType=BIGINT}
        </if>
        <if test="'test_plan_report_id'.toString() == column.value">
          #{item.testPlanReportId,jdbcType=VARCHAR}
        </if>
        <if test="'plan_count'.toString() == column.value">
          #{item.planCount,jdbcType=BIGINT}
        </if>
        <if test="'summary'.toString() == column.value">
          #{item.summary,jdbcType=LONGVARCHAR}
        </if>
        <if test="'functional_execute_result'.toString() == column.value">
          #{item.functionalExecuteResult,jdbcType=LONGVARBINARY}
        </if>
        <if test="'api_execute_result'.toString() == column.value">
          #{item.apiExecuteResult,jdbcType=LONGVARBINARY}
        </if>
        <if test="'scenario_execute_result'.toString() == column.value">
          #{item.scenarioExecuteResult,jdbcType=LONGVARBINARY}
        </if>
        <if test="'execute_result'.toString() == column.value">
          #{item.executeResult,jdbcType=LONGVARBINARY}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>