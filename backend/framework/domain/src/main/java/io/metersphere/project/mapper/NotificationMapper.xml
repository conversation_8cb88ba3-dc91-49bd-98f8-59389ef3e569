<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.project.mapper.NotificationMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.project.domain.Notification">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="receiver" jdbcType="VARCHAR" property="receiver" />
    <result column="subject" jdbcType="VARCHAR" property="subject" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="operation" jdbcType="VARCHAR" property="operation" />
    <result column="resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="organization_id" jdbcType="VARCHAR" property="organizationId" />
    <result column="resource_type" jdbcType="VARCHAR" property="resourceType" />
    <result column="resource_name" jdbcType="VARCHAR" property="resourceName" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="io.metersphere.project.domain.Notification">
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, `type`, receiver, subject, `status`, create_time, `operator`, `operation`, resource_id, 
    project_id, organization_id, resource_type, resource_name
  </sql>
  <sql id="Blob_Column_List">
    content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="io.metersphere.project.domain.NotificationExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from notification
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="io.metersphere.project.domain.NotificationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from notification
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from notification
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from notification
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.project.domain.NotificationExample">
    delete from notification
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.project.domain.Notification">
    insert into notification (id, `type`, receiver, 
      subject, `status`, create_time, 
      `operator`, `operation`, resource_id, 
      project_id, organization_id, resource_type, 
      resource_name, content)
    values (#{id,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR}, #{receiver,jdbcType=VARCHAR}, 
      #{subject,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, 
      #{operator,jdbcType=VARCHAR}, #{operation,jdbcType=VARCHAR}, #{resourceId,jdbcType=VARCHAR}, 
      #{projectId,jdbcType=VARCHAR}, #{organizationId,jdbcType=VARCHAR}, #{resourceType,jdbcType=VARCHAR}, 
      #{resourceName,jdbcType=VARCHAR}, #{content,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.project.domain.Notification">
    insert into notification
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="receiver != null">
        receiver,
      </if>
      <if test="subject != null">
        subject,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="operator != null">
        `operator`,
      </if>
      <if test="operation != null">
        `operation`,
      </if>
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="organizationId != null">
        organization_id,
      </if>
      <if test="resourceType != null">
        resource_type,
      </if>
      <if test="resourceName != null">
        resource_name,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="receiver != null">
        #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="subject != null">
        #{subject,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="organizationId != null">
        #{organizationId,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="resourceName != null">
        #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.project.domain.NotificationExample" resultType="java.lang.Long">
    select count(*) from notification
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update notification
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.receiver != null">
        receiver = #{record.receiver,jdbcType=VARCHAR},
      </if>
      <if test="record.subject != null">
        subject = #{record.subject,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.operator != null">
        `operator` = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.operation != null">
        `operation` = #{record.operation,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceId != null">
        resource_id = #{record.resourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=VARCHAR},
      </if>
      <if test="record.organizationId != null">
        organization_id = #{record.organizationId,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceType != null">
        resource_type = #{record.resourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceName != null">
        resource_name = #{record.resourceName,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update notification
    set id = #{record.id,jdbcType=BIGINT},
      `type` = #{record.type,jdbcType=VARCHAR},
      receiver = #{record.receiver,jdbcType=VARCHAR},
      subject = #{record.subject,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      `operator` = #{record.operator,jdbcType=VARCHAR},
      `operation` = #{record.operation,jdbcType=VARCHAR},
      resource_id = #{record.resourceId,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=VARCHAR},
      organization_id = #{record.organizationId,jdbcType=VARCHAR},
      resource_type = #{record.resourceType,jdbcType=VARCHAR},
      resource_name = #{record.resourceName,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update notification
    set id = #{record.id,jdbcType=BIGINT},
      `type` = #{record.type,jdbcType=VARCHAR},
      receiver = #{record.receiver,jdbcType=VARCHAR},
      subject = #{record.subject,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      `operator` = #{record.operator,jdbcType=VARCHAR},
      `operation` = #{record.operation,jdbcType=VARCHAR},
      resource_id = #{record.resourceId,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=VARCHAR},
      organization_id = #{record.organizationId,jdbcType=VARCHAR},
      resource_type = #{record.resourceType,jdbcType=VARCHAR},
      resource_name = #{record.resourceName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.project.domain.Notification">
    update notification
    <set>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="receiver != null">
        receiver = #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="subject != null">
        subject = #{subject,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="operator != null">
        `operator` = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operation != null">
        `operation` = #{operation,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="organizationId != null">
        organization_id = #{organizationId,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        resource_type = #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="resourceName != null">
        resource_name = #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="io.metersphere.project.domain.Notification">
    update notification
    set `type` = #{type,jdbcType=VARCHAR},
      receiver = #{receiver,jdbcType=VARCHAR},
      subject = #{subject,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      `operator` = #{operator,jdbcType=VARCHAR},
      `operation` = #{operation,jdbcType=VARCHAR},
      resource_id = #{resourceId,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=VARCHAR},
      organization_id = #{organizationId,jdbcType=VARCHAR},
      resource_type = #{resourceType,jdbcType=VARCHAR},
      resource_name = #{resourceName,jdbcType=VARCHAR},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.project.domain.Notification">
    update notification
    set `type` = #{type,jdbcType=VARCHAR},
      receiver = #{receiver,jdbcType=VARCHAR},
      subject = #{subject,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      `operator` = #{operator,jdbcType=VARCHAR},
      `operation` = #{operation,jdbcType=VARCHAR},
      resource_id = #{resourceId,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=VARCHAR},
      organization_id = #{organizationId,jdbcType=VARCHAR},
      resource_type = #{resourceType,jdbcType=VARCHAR},
      resource_name = #{resourceName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into notification
    (id, `type`, receiver, subject, `status`, create_time, `operator`, `operation`, resource_id, 
      project_id, organization_id, resource_type, resource_name, content)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.type,jdbcType=VARCHAR}, #{item.receiver,jdbcType=VARCHAR}, 
        #{item.subject,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.operator,jdbcType=VARCHAR}, #{item.operation,jdbcType=VARCHAR}, #{item.resourceId,jdbcType=VARCHAR}, 
        #{item.projectId,jdbcType=VARCHAR}, #{item.organizationId,jdbcType=VARCHAR}, #{item.resourceType,jdbcType=VARCHAR}, 
        #{item.resourceName,jdbcType=VARCHAR}, #{item.content,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into notification (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=VARCHAR}
        </if>
        <if test="'receiver'.toString() == column.value">
          #{item.receiver,jdbcType=VARCHAR}
        </if>
        <if test="'subject'.toString() == column.value">
          #{item.subject,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'operator'.toString() == column.value">
          #{item.operator,jdbcType=VARCHAR}
        </if>
        <if test="'operation'.toString() == column.value">
          #{item.operation,jdbcType=VARCHAR}
        </if>
        <if test="'resource_id'.toString() == column.value">
          #{item.resourceId,jdbcType=VARCHAR}
        </if>
        <if test="'project_id'.toString() == column.value">
          #{item.projectId,jdbcType=VARCHAR}
        </if>
        <if test="'organization_id'.toString() == column.value">
          #{item.organizationId,jdbcType=VARCHAR}
        </if>
        <if test="'resource_type'.toString() == column.value">
          #{item.resourceType,jdbcType=VARCHAR}
        </if>
        <if test="'resource_name'.toString() == column.value">
          #{item.resourceName,jdbcType=VARCHAR}
        </if>
        <if test="'content'.toString() == column.value">
          #{item.content,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>