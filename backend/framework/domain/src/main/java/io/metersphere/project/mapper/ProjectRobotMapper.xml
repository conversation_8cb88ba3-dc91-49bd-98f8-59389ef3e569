<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.project.mapper.ProjectRobotMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.project.domain.ProjectRobot">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="webhook" jdbcType="VARCHAR" property="webhook" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="app_key" jdbcType="VARCHAR" property="appKey" />
    <result column="app_secret" jdbcType="VARCHAR" property="appSecret" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="description" jdbcType="VARCHAR" property="description" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, `name`, platform, webhook, `type`, app_key, app_secret, `enable`, 
    create_user, create_time, update_user, update_time, description
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.project.domain.ProjectRobotExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_robot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_robot
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from project_robot
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.project.domain.ProjectRobotExample">
    delete from project_robot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.project.domain.ProjectRobot">
    insert into project_robot (id, project_id, `name`, 
      platform, webhook, `type`, 
      app_key, app_secret, `enable`, 
      create_user, create_time, update_user, 
      update_time, description)
    values (#{id,jdbcType=VARCHAR}, #{projectId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{platform,jdbcType=VARCHAR}, #{webhook,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{appKey,jdbcType=VARCHAR}, #{appSecret,jdbcType=VARCHAR}, #{enable,jdbcType=BIT}, 
      #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, #{updateUser,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=BIGINT}, #{description,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.project.domain.ProjectRobot">
    insert into project_robot
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="webhook != null">
        webhook,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="appKey != null">
        app_key,
      </if>
      <if test="appSecret != null">
        app_secret,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="description != null">
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="webhook != null">
        #{webhook,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="appKey != null">
        #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="appSecret != null">
        #{appSecret,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.project.domain.ProjectRobotExample" resultType="java.lang.Long">
    select count(*) from project_robot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_robot
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=VARCHAR},
      </if>
      <if test="record.webhook != null">
        webhook = #{record.webhook,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.appKey != null">
        app_key = #{record.appKey,jdbcType=VARCHAR},
      </if>
      <if test="record.appSecret != null">
        app_secret = #{record.appSecret,jdbcType=VARCHAR},
      </if>
      <if test="record.enable != null">
        `enable` = #{record.enable,jdbcType=BIT},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_robot
    set id = #{record.id,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      platform = #{record.platform,jdbcType=VARCHAR},
      webhook = #{record.webhook,jdbcType=VARCHAR},
      `type` = #{record.type,jdbcType=VARCHAR},
      app_key = #{record.appKey,jdbcType=VARCHAR},
      app_secret = #{record.appSecret,jdbcType=VARCHAR},
      `enable` = #{record.enable,jdbcType=BIT},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      description = #{record.description,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.project.domain.ProjectRobot">
    update project_robot
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="webhook != null">
        webhook = #{webhook,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="appKey != null">
        app_key = #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="appSecret != null">
        app_secret = #{appSecret,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.project.domain.ProjectRobot">
    update project_robot
    set project_id = #{projectId,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=VARCHAR},
      webhook = #{webhook,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      app_key = #{appKey,jdbcType=VARCHAR},
      app_secret = #{appSecret,jdbcType=VARCHAR},
      `enable` = #{enable,jdbcType=BIT},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=BIGINT},
      description = #{description,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into project_robot
    (id, project_id, `name`, platform, webhook, `type`, app_key, app_secret, `enable`, 
      create_user, create_time, update_user, update_time, description)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.projectId,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.platform,jdbcType=VARCHAR}, #{item.webhook,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR}, 
        #{item.appKey,jdbcType=VARCHAR}, #{item.appSecret,jdbcType=VARCHAR}, #{item.enable,jdbcType=BIT}, 
        #{item.createUser,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT}, #{item.updateUser,jdbcType=VARCHAR}, 
        #{item.updateTime,jdbcType=BIGINT}, #{item.description,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into project_robot (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'project_id'.toString() == column.value">
          #{item.projectId,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'platform'.toString() == column.value">
          #{item.platform,jdbcType=VARCHAR}
        </if>
        <if test="'webhook'.toString() == column.value">
          #{item.webhook,jdbcType=VARCHAR}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=VARCHAR}
        </if>
        <if test="'app_key'.toString() == column.value">
          #{item.appKey,jdbcType=VARCHAR}
        </if>
        <if test="'app_secret'.toString() == column.value">
          #{item.appSecret,jdbcType=VARCHAR}
        </if>
        <if test="'enable'.toString() == column.value">
          #{item.enable,jdbcType=BIT}
        </if>
        <if test="'create_user'.toString() == column.value">
          #{item.createUser,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'update_user'.toString() == column.value">
          #{item.updateUser,jdbcType=VARCHAR}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=BIGINT}
        </if>
        <if test="'description'.toString() == column.value">
          #{item.description,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>