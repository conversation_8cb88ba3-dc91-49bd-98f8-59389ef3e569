<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.project.mapper.FileModuleRepositoryMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.project.domain.FileModuleRepository">
    <id column="file_module_id" jdbcType="VARCHAR" property="fileModuleId" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    file_module_id, platform, url, token, user_name
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.project.domain.FileModuleRepositoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from file_module_repository
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from file_module_repository
    where file_module_id = #{fileModuleId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from file_module_repository
    where file_module_id = #{fileModuleId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.project.domain.FileModuleRepositoryExample">
    delete from file_module_repository
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.project.domain.FileModuleRepository">
    insert into file_module_repository (file_module_id, platform, url, 
      token, user_name)
    values (#{fileModuleId,jdbcType=VARCHAR}, #{platform,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, 
      #{token,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.project.domain.FileModuleRepository">
    insert into file_module_repository
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fileModuleId != null">
        file_module_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="token != null">
        token,
      </if>
      <if test="userName != null">
        user_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fileModuleId != null">
        #{fileModuleId,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.project.domain.FileModuleRepositoryExample" resultType="java.lang.Long">
    select count(*) from file_module_repository
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update file_module_repository
    <set>
      <if test="record.fileModuleId != null">
        file_module_id = #{record.fileModuleId,jdbcType=VARCHAR},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=VARCHAR},
      </if>
      <if test="record.url != null">
        url = #{record.url,jdbcType=VARCHAR},
      </if>
      <if test="record.token != null">
        token = #{record.token,jdbcType=VARCHAR},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update file_module_repository
    set file_module_id = #{record.fileModuleId,jdbcType=VARCHAR},
      platform = #{record.platform,jdbcType=VARCHAR},
      url = #{record.url,jdbcType=VARCHAR},
      token = #{record.token,jdbcType=VARCHAR},
      user_name = #{record.userName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.project.domain.FileModuleRepository">
    update file_module_repository
    <set>
      <if test="platform != null">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        token = #{token,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
    </set>
    where file_module_id = #{fileModuleId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.project.domain.FileModuleRepository">
    update file_module_repository
    set platform = #{platform,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      token = #{token,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR}
    where file_module_id = #{fileModuleId,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into file_module_repository
    (file_module_id, platform, url, token, user_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.fileModuleId,jdbcType=VARCHAR}, #{item.platform,jdbcType=VARCHAR}, #{item.url,jdbcType=VARCHAR}, 
        #{item.token,jdbcType=VARCHAR}, #{item.userName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into file_module_repository (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'file_module_id'.toString() == column.value">
          #{item.fileModuleId,jdbcType=VARCHAR}
        </if>
        <if test="'platform'.toString() == column.value">
          #{item.platform,jdbcType=VARCHAR}
        </if>
        <if test="'url'.toString() == column.value">
          #{item.url,jdbcType=VARCHAR}
        </if>
        <if test="'token'.toString() == column.value">
          #{item.token,jdbcType=VARCHAR}
        </if>
        <if test="'user_name'.toString() == column.value">
          #{item.userName,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>