<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.bug.mapper.BugRelationCaseMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.bug.domain.BugRelationCase">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="case_id" jdbcType="VARCHAR" property="caseId" />
    <result column="bug_id" jdbcType="VARCHAR" property="bugId" />
    <result column="case_type" jdbcType="VARCHAR" property="caseType" />
    <result column="test_plan_id" jdbcType="VARCHAR" property="testPlanId" />
    <result column="test_plan_case_id" jdbcType="VARCHAR" property="testPlanCaseId" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, case_id, bug_id, case_type, test_plan_id, test_plan_case_id, create_user, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.bug.domain.BugRelationCaseExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bug_relation_case
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bug_relation_case
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from bug_relation_case
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.bug.domain.BugRelationCaseExample">
    delete from bug_relation_case
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.bug.domain.BugRelationCase">
    insert into bug_relation_case (id, case_id, bug_id, 
      case_type, test_plan_id, test_plan_case_id, 
      create_user, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{caseId,jdbcType=VARCHAR}, #{bugId,jdbcType=VARCHAR}, 
      #{caseType,jdbcType=VARCHAR}, #{testPlanId,jdbcType=VARCHAR}, #{testPlanCaseId,jdbcType=VARCHAR}, 
      #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.bug.domain.BugRelationCase">
    insert into bug_relation_case
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="caseId != null">
        case_id,
      </if>
      <if test="bugId != null">
        bug_id,
      </if>
      <if test="caseType != null">
        case_type,
      </if>
      <if test="testPlanId != null">
        test_plan_id,
      </if>
      <if test="testPlanCaseId != null">
        test_plan_case_id,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="caseId != null">
        #{caseId,jdbcType=VARCHAR},
      </if>
      <if test="bugId != null">
        #{bugId,jdbcType=VARCHAR},
      </if>
      <if test="caseType != null">
        #{caseType,jdbcType=VARCHAR},
      </if>
      <if test="testPlanId != null">
        #{testPlanId,jdbcType=VARCHAR},
      </if>
      <if test="testPlanCaseId != null">
        #{testPlanCaseId,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.bug.domain.BugRelationCaseExample" resultType="java.lang.Long">
    select count(*) from bug_relation_case
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bug_relation_case
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.caseId != null">
        case_id = #{record.caseId,jdbcType=VARCHAR},
      </if>
      <if test="record.bugId != null">
        bug_id = #{record.bugId,jdbcType=VARCHAR},
      </if>
      <if test="record.caseType != null">
        case_type = #{record.caseType,jdbcType=VARCHAR},
      </if>
      <if test="record.testPlanId != null">
        test_plan_id = #{record.testPlanId,jdbcType=VARCHAR},
      </if>
      <if test="record.testPlanCaseId != null">
        test_plan_case_id = #{record.testPlanCaseId,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bug_relation_case
    set id = #{record.id,jdbcType=VARCHAR},
      case_id = #{record.caseId,jdbcType=VARCHAR},
      bug_id = #{record.bugId,jdbcType=VARCHAR},
      case_type = #{record.caseType,jdbcType=VARCHAR},
      test_plan_id = #{record.testPlanId,jdbcType=VARCHAR},
      test_plan_case_id = #{record.testPlanCaseId,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.bug.domain.BugRelationCase">
    update bug_relation_case
    <set>
      <if test="caseId != null">
        case_id = #{caseId,jdbcType=VARCHAR},
      </if>
      <if test="bugId != null">
        bug_id = #{bugId,jdbcType=VARCHAR},
      </if>
      <if test="caseType != null">
        case_type = #{caseType,jdbcType=VARCHAR},
      </if>
      <if test="testPlanId != null">
        test_plan_id = #{testPlanId,jdbcType=VARCHAR},
      </if>
      <if test="testPlanCaseId != null">
        test_plan_case_id = #{testPlanCaseId,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.bug.domain.BugRelationCase">
    update bug_relation_case
    set case_id = #{caseId,jdbcType=VARCHAR},
      bug_id = #{bugId,jdbcType=VARCHAR},
      case_type = #{caseType,jdbcType=VARCHAR},
      test_plan_id = #{testPlanId,jdbcType=VARCHAR},
      test_plan_case_id = #{testPlanCaseId,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into bug_relation_case
    (id, case_id, bug_id, case_type, test_plan_id, test_plan_case_id, create_user, create_time, 
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.caseId,jdbcType=VARCHAR}, #{item.bugId,jdbcType=VARCHAR}, 
        #{item.caseType,jdbcType=VARCHAR}, #{item.testPlanId,jdbcType=VARCHAR}, #{item.testPlanCaseId,jdbcType=VARCHAR}, 
        #{item.createUser,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into bug_relation_case (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'case_id'.toString() == column.value">
          #{item.caseId,jdbcType=VARCHAR}
        </if>
        <if test="'bug_id'.toString() == column.value">
          #{item.bugId,jdbcType=VARCHAR}
        </if>
        <if test="'case_type'.toString() == column.value">
          #{item.caseType,jdbcType=VARCHAR}
        </if>
        <if test="'test_plan_id'.toString() == column.value">
          #{item.testPlanId,jdbcType=VARCHAR}
        </if>
        <if test="'test_plan_case_id'.toString() == column.value">
          #{item.testPlanCaseId,jdbcType=VARCHAR}
        </if>
        <if test="'create_user'.toString() == column.value">
          #{item.createUser,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>