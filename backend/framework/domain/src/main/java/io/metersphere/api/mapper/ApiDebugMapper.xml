<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.api.mapper.ApiDebugMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.api.domain.ApiDebug">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="protocol" jdbcType="VARCHAR" property="protocol" />
    <result column="method" jdbcType="VARCHAR" property="method" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="module_id" jdbcType="VARCHAR" property="moduleId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="pos" jdbcType="BIGINT" property="pos" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, `name`, protocol, `method`, `path`, project_id, module_id, create_time, create_user, 
    update_time, update_user, pos
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.api.domain.ApiDebugExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from api_debug
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from api_debug
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from api_debug
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.api.domain.ApiDebugExample">
    delete from api_debug
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.api.domain.ApiDebug">
    insert into api_debug (id, `name`, protocol, 
      `method`, `path`, project_id, 
      module_id, create_time, create_user, 
      update_time, update_user, pos
      )
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{protocol,jdbcType=VARCHAR}, 
      #{method,jdbcType=VARCHAR}, #{path,jdbcType=VARCHAR}, #{projectId,jdbcType=VARCHAR}, 
      #{moduleId,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, #{createUser,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=BIGINT}, #{updateUser,jdbcType=VARCHAR}, #{pos,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.api.domain.ApiDebug">
    insert into api_debug
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="protocol != null">
        protocol,
      </if>
      <if test="method != null">
        `method`,
      </if>
      <if test="path != null">
        `path`,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="moduleId != null">
        module_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="pos != null">
        pos,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="protocol != null">
        #{protocol,jdbcType=VARCHAR},
      </if>
      <if test="method != null">
        #{method,jdbcType=VARCHAR},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="moduleId != null">
        #{moduleId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="pos != null">
        #{pos,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.api.domain.ApiDebugExample" resultType="java.lang.Long">
    select count(*) from api_debug
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update api_debug
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.protocol != null">
        protocol = #{record.protocol,jdbcType=VARCHAR},
      </if>
      <if test="record.method != null">
        `method` = #{record.method,jdbcType=VARCHAR},
      </if>
      <if test="record.path != null">
        `path` = #{record.path,jdbcType=VARCHAR},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=VARCHAR},
      </if>
      <if test="record.moduleId != null">
        module_id = #{record.moduleId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.pos != null">
        pos = #{record.pos,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update api_debug
    set id = #{record.id,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      protocol = #{record.protocol,jdbcType=VARCHAR},
      `method` = #{record.method,jdbcType=VARCHAR},
      `path` = #{record.path,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=VARCHAR},
      module_id = #{record.moduleId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      pos = #{record.pos,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.api.domain.ApiDebug">
    update api_debug
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="protocol != null">
        protocol = #{protocol,jdbcType=VARCHAR},
      </if>
      <if test="method != null">
        `method` = #{method,jdbcType=VARCHAR},
      </if>
      <if test="path != null">
        `path` = #{path,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="moduleId != null">
        module_id = #{moduleId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="pos != null">
        pos = #{pos,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.api.domain.ApiDebug">
    update api_debug
    set `name` = #{name,jdbcType=VARCHAR},
      protocol = #{protocol,jdbcType=VARCHAR},
      `method` = #{method,jdbcType=VARCHAR},
      `path` = #{path,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=VARCHAR},
      module_id = #{moduleId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=BIGINT},
      update_user = #{updateUser,jdbcType=VARCHAR},
      pos = #{pos,jdbcType=BIGINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into api_debug
    (id, `name`, protocol, `method`, `path`, project_id, module_id, create_time, create_user, 
      update_time, update_user, pos)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.protocol,jdbcType=VARCHAR}, 
        #{item.method,jdbcType=VARCHAR}, #{item.path,jdbcType=VARCHAR}, #{item.projectId,jdbcType=VARCHAR}, 
        #{item.moduleId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT}, #{item.createUser,jdbcType=VARCHAR}, 
        #{item.updateTime,jdbcType=BIGINT}, #{item.updateUser,jdbcType=VARCHAR}, #{item.pos,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into api_debug (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'protocol'.toString() == column.value">
          #{item.protocol,jdbcType=VARCHAR}
        </if>
        <if test="'method'.toString() == column.value">
          #{item.method,jdbcType=VARCHAR}
        </if>
        <if test="'path'.toString() == column.value">
          #{item.path,jdbcType=VARCHAR}
        </if>
        <if test="'project_id'.toString() == column.value">
          #{item.projectId,jdbcType=VARCHAR}
        </if>
        <if test="'module_id'.toString() == column.value">
          #{item.moduleId,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'create_user'.toString() == column.value">
          #{item.createUser,jdbcType=VARCHAR}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=BIGINT}
        </if>
        <if test="'update_user'.toString() == column.value">
          #{item.updateUser,jdbcType=VARCHAR}
        </if>
        <if test="'pos'.toString() == column.value">
          #{item.pos,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>