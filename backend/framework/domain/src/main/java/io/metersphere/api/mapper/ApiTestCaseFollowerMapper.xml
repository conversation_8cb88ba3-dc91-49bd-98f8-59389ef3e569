<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.api.mapper.ApiTestCaseFollowerMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.api.domain.ApiTestCaseFollower">
    <id column="case_id" jdbcType="VARCHAR" property="caseId" />
    <id column="user_id" jdbcType="VARCHAR" property="userId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    case_id, user_id
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.api.domain.ApiTestCaseFollowerExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from api_test_case_follower
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from api_test_case_follower
    where case_id = #{caseId,jdbcType=VARCHAR}
      and user_id = #{userId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.api.domain.ApiTestCaseFollowerExample">
    delete from api_test_case_follower
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.api.domain.ApiTestCaseFollower">
    insert into api_test_case_follower (case_id, user_id)
    values (#{caseId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.api.domain.ApiTestCaseFollower">
    insert into api_test_case_follower
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        case_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        #{caseId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.api.domain.ApiTestCaseFollowerExample" resultType="java.lang.Long">
    select count(*) from api_test_case_follower
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update api_test_case_follower
    <set>
      <if test="record.caseId != null">
        case_id = #{record.caseId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update api_test_case_follower
    set case_id = #{record.caseId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into api_test_case_follower
    (case_id, user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.caseId,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into api_test_case_follower (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'case_id'.toString() == column.value">
          #{item.caseId,jdbcType=VARCHAR}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>