<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.project.mapper.ProjectMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.project.domain.Project">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="num" jdbcType="BIGINT" property="num" />
    <result column="organization_id" jdbcType="VARCHAR" property="organizationId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="delete_time" jdbcType="BIGINT" property="deleteTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="delete_user" jdbcType="VARCHAR" property="deleteUser" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="module_setting" jdbcType="VARCHAR" property="moduleSetting" />
    <result column="all_resource_pool" jdbcType="BIT" property="allResourcePool" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, num, organization_id, `name`, description, create_time, update_time, update_user, 
    create_user, delete_time, deleted, delete_user, `enable`, module_setting, all_resource_pool
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.project.domain.ProjectExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from project
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.project.domain.ProjectExample">
    delete from project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.project.domain.Project">
    insert into project (id, num, organization_id, 
      `name`, description, create_time, 
      update_time, update_user, create_user, 
      delete_time, deleted, delete_user, 
      `enable`, module_setting, all_resource_pool
      )
    values (#{id,jdbcType=VARCHAR}, #{num,jdbcType=BIGINT}, #{organizationId,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT}, #{updateUser,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, 
      #{deleteTime,jdbcType=BIGINT}, #{deleted,jdbcType=BIT}, #{deleteUser,jdbcType=VARCHAR}, 
      #{enable,jdbcType=BIT}, #{moduleSetting,jdbcType=VARCHAR}, #{allResourcePool,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.project.domain.Project">
    insert into project
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="organizationId != null">
        organization_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="deleteTime != null">
        delete_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="deleteUser != null">
        delete_user,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="moduleSetting != null">
        module_setting,
      </if>
      <if test="allResourcePool != null">
        all_resource_pool,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=BIGINT},
      </if>
      <if test="organizationId != null">
        #{organizationId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="deleteTime != null">
        #{deleteTime,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="deleteUser != null">
        #{deleteUser,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="moduleSetting != null">
        #{moduleSetting,jdbcType=VARCHAR},
      </if>
      <if test="allResourcePool != null">
        #{allResourcePool,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.project.domain.ProjectExample" resultType="java.lang.Long">
    select count(*) from project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.num != null">
        num = #{record.num,jdbcType=BIGINT},
      </if>
      <if test="record.organizationId != null">
        organization_id = #{record.organizationId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.deleteTime != null">
        delete_time = #{record.deleteTime,jdbcType=BIGINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.deleteUser != null">
        delete_user = #{record.deleteUser,jdbcType=VARCHAR},
      </if>
      <if test="record.enable != null">
        `enable` = #{record.enable,jdbcType=BIT},
      </if>
      <if test="record.moduleSetting != null">
        module_setting = #{record.moduleSetting,jdbcType=VARCHAR},
      </if>
      <if test="record.allResourcePool != null">
        all_resource_pool = #{record.allResourcePool,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project
    set id = #{record.id,jdbcType=VARCHAR},
      num = #{record.num,jdbcType=BIGINT},
      organization_id = #{record.organizationId,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      delete_time = #{record.deleteTime,jdbcType=BIGINT},
      deleted = #{record.deleted,jdbcType=BIT},
      delete_user = #{record.deleteUser,jdbcType=VARCHAR},
      `enable` = #{record.enable,jdbcType=BIT},
      module_setting = #{record.moduleSetting,jdbcType=VARCHAR},
      all_resource_pool = #{record.allResourcePool,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.project.domain.Project">
    update project
    <set>
      <if test="num != null">
        num = #{num,jdbcType=BIGINT},
      </if>
      <if test="organizationId != null">
        organization_id = #{organizationId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="deleteTime != null">
        delete_time = #{deleteTime,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="deleteUser != null">
        delete_user = #{deleteUser,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="moduleSetting != null">
        module_setting = #{moduleSetting,jdbcType=VARCHAR},
      </if>
      <if test="allResourcePool != null">
        all_resource_pool = #{allResourcePool,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.project.domain.Project">
    update project
    set num = #{num,jdbcType=BIGINT},
      organization_id = #{organizationId,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      update_user = #{updateUser,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      delete_time = #{deleteTime,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=BIT},
      delete_user = #{deleteUser,jdbcType=VARCHAR},
      `enable` = #{enable,jdbcType=BIT},
      module_setting = #{moduleSetting,jdbcType=VARCHAR},
      all_resource_pool = #{allResourcePool,jdbcType=BIT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into project
    (id, num, organization_id, `name`, description, create_time, update_time, update_user, 
      create_user, delete_time, deleted, delete_user, `enable`, module_setting, all_resource_pool
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.num,jdbcType=BIGINT}, #{item.organizationId,jdbcType=VARCHAR}, 
        #{item.name,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.updateTime,jdbcType=BIGINT}, #{item.updateUser,jdbcType=VARCHAR}, #{item.createUser,jdbcType=VARCHAR}, 
        #{item.deleteTime,jdbcType=BIGINT}, #{item.deleted,jdbcType=BIT}, #{item.deleteUser,jdbcType=VARCHAR}, 
        #{item.enable,jdbcType=BIT}, #{item.moduleSetting,jdbcType=VARCHAR}, #{item.allResourcePool,jdbcType=BIT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into project (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'num'.toString() == column.value">
          #{item.num,jdbcType=BIGINT}
        </if>
        <if test="'organization_id'.toString() == column.value">
          #{item.organizationId,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'description'.toString() == column.value">
          #{item.description,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=BIGINT}
        </if>
        <if test="'update_user'.toString() == column.value">
          #{item.updateUser,jdbcType=VARCHAR}
        </if>
        <if test="'create_user'.toString() == column.value">
          #{item.createUser,jdbcType=VARCHAR}
        </if>
        <if test="'delete_time'.toString() == column.value">
          #{item.deleteTime,jdbcType=BIGINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=BIT}
        </if>
        <if test="'delete_user'.toString() == column.value">
          #{item.deleteUser,jdbcType=VARCHAR}
        </if>
        <if test="'enable'.toString() == column.value">
          #{item.enable,jdbcType=BIT}
        </if>
        <if test="'module_setting'.toString() == column.value">
          #{item.moduleSetting,jdbcType=VARCHAR}
        </if>
        <if test="'all_resource_pool'.toString() == column.value">
          #{item.allResourcePool,jdbcType=BIT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>