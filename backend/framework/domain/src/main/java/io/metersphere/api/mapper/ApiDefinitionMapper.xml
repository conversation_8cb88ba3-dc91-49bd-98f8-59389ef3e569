<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.api.mapper.ApiDefinitionMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.api.domain.ApiDefinition">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="protocol" jdbcType="VARCHAR" property="protocol" />
    <result column="method" jdbcType="VARCHAR" property="method" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="num" jdbcType="BIGINT" property="num" />
    <result column="tags" jdbcType="VARCHAR" property="tags" typeHandler="io.metersphere.handler.ListTypeHandler" />
    <result column="pos" jdbcType="BIGINT" property="pos" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="module_id" jdbcType="VARCHAR" property="moduleId" />
    <result column="latest" jdbcType="BIT" property="latest" />
    <result column="version_id" jdbcType="VARCHAR" property="versionId" />
    <result column="ref_id" jdbcType="VARCHAR" property="refId" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="delete_user" jdbcType="VARCHAR" property="deleteUser" />
    <result column="delete_time" jdbcType="BIGINT" property="deleteTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.tagsCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=io.metersphere.handler.ListTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=io.metersphere.handler.ListTypeHandler} and #{criterion.secondValue,typeHandler=io.metersphere.handler.ListTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=io.metersphere.handler.ListTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.tagsCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=io.metersphere.handler.ListTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=io.metersphere.handler.ListTypeHandler} and #{criterion.secondValue,typeHandler=io.metersphere.handler.ListTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=io.metersphere.handler.ListTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, `name`, protocol, `method`, `path`, `status`, num, tags, pos, project_id, module_id, 
    latest, version_id, ref_id, description, create_time, create_user, update_time, update_user, 
    delete_user, delete_time, deleted
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.api.domain.ApiDefinitionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from api_definition
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from api_definition
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from api_definition
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.api.domain.ApiDefinitionExample">
    delete from api_definition
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.api.domain.ApiDefinition">
    insert into api_definition (id, `name`, protocol, 
      `method`, `path`, `status`, 
      num, tags, 
      pos, project_id, module_id, 
      latest, version_id, ref_id, 
      description, create_time, create_user, 
      update_time, update_user, delete_user, 
      delete_time, deleted)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{protocol,jdbcType=VARCHAR}, 
      #{method,jdbcType=VARCHAR}, #{path,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{num,jdbcType=BIGINT}, #{tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler}, 
      #{pos,jdbcType=BIGINT}, #{projectId,jdbcType=VARCHAR}, #{moduleId,jdbcType=VARCHAR}, 
      #{latest,jdbcType=BIT}, #{versionId,jdbcType=VARCHAR}, #{refId,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, #{createUser,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=BIGINT}, #{updateUser,jdbcType=VARCHAR}, #{deleteUser,jdbcType=VARCHAR}, 
      #{deleteTime,jdbcType=BIGINT}, #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.api.domain.ApiDefinition">
    insert into api_definition
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="protocol != null">
        protocol,
      </if>
      <if test="method != null">
        `method`,
      </if>
      <if test="path != null">
        `path`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="tags != null">
        tags,
      </if>
      <if test="pos != null">
        pos,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="moduleId != null">
        module_id,
      </if>
      <if test="latest != null">
        latest,
      </if>
      <if test="versionId != null">
        version_id,
      </if>
      <if test="refId != null">
        ref_id,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="deleteUser != null">
        delete_user,
      </if>
      <if test="deleteTime != null">
        delete_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="protocol != null">
        #{protocol,jdbcType=VARCHAR},
      </if>
      <if test="method != null">
        #{method,jdbcType=VARCHAR},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=BIGINT},
      </if>
      <if test="tags != null">
        #{tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler},
      </if>
      <if test="pos != null">
        #{pos,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="moduleId != null">
        #{moduleId,jdbcType=VARCHAR},
      </if>
      <if test="latest != null">
        #{latest,jdbcType=BIT},
      </if>
      <if test="versionId != null">
        #{versionId,jdbcType=VARCHAR},
      </if>
      <if test="refId != null">
        #{refId,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="deleteUser != null">
        #{deleteUser,jdbcType=VARCHAR},
      </if>
      <if test="deleteTime != null">
        #{deleteTime,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.api.domain.ApiDefinitionExample" resultType="java.lang.Long">
    select count(*) from api_definition
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update api_definition
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.protocol != null">
        protocol = #{record.protocol,jdbcType=VARCHAR},
      </if>
      <if test="record.method != null">
        `method` = #{record.method,jdbcType=VARCHAR},
      </if>
      <if test="record.path != null">
        `path` = #{record.path,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.num != null">
        num = #{record.num,jdbcType=BIGINT},
      </if>
      <if test="record.tags != null">
        tags = #{record.tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler},
      </if>
      <if test="record.pos != null">
        pos = #{record.pos,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=VARCHAR},
      </if>
      <if test="record.moduleId != null">
        module_id = #{record.moduleId,jdbcType=VARCHAR},
      </if>
      <if test="record.latest != null">
        latest = #{record.latest,jdbcType=BIT},
      </if>
      <if test="record.versionId != null">
        version_id = #{record.versionId,jdbcType=VARCHAR},
      </if>
      <if test="record.refId != null">
        ref_id = #{record.refId,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.deleteUser != null">
        delete_user = #{record.deleteUser,jdbcType=VARCHAR},
      </if>
      <if test="record.deleteTime != null">
        delete_time = #{record.deleteTime,jdbcType=BIGINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update api_definition
    set id = #{record.id,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      protocol = #{record.protocol,jdbcType=VARCHAR},
      `method` = #{record.method,jdbcType=VARCHAR},
      `path` = #{record.path,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=VARCHAR},
      num = #{record.num,jdbcType=BIGINT},
      tags = #{record.tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler},
      pos = #{record.pos,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=VARCHAR},
      module_id = #{record.moduleId,jdbcType=VARCHAR},
      latest = #{record.latest,jdbcType=BIT},
      version_id = #{record.versionId,jdbcType=VARCHAR},
      ref_id = #{record.refId,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      delete_user = #{record.deleteUser,jdbcType=VARCHAR},
      delete_time = #{record.deleteTime,jdbcType=BIGINT},
      deleted = #{record.deleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.api.domain.ApiDefinition">
    update api_definition
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="protocol != null">
        protocol = #{protocol,jdbcType=VARCHAR},
      </if>
      <if test="method != null">
        `method` = #{method,jdbcType=VARCHAR},
      </if>
      <if test="path != null">
        `path` = #{path,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        num = #{num,jdbcType=BIGINT},
      </if>
      <if test="tags != null">
        tags = #{tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler},
      </if>
      <if test="pos != null">
        pos = #{pos,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="moduleId != null">
        module_id = #{moduleId,jdbcType=VARCHAR},
      </if>
      <if test="latest != null">
        latest = #{latest,jdbcType=BIT},
      </if>
      <if test="versionId != null">
        version_id = #{versionId,jdbcType=VARCHAR},
      </if>
      <if test="refId != null">
        ref_id = #{refId,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="deleteUser != null">
        delete_user = #{deleteUser,jdbcType=VARCHAR},
      </if>
      <if test="deleteTime != null">
        delete_time = #{deleteTime,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.api.domain.ApiDefinition">
    update api_definition
    set `name` = #{name,jdbcType=VARCHAR},
      protocol = #{protocol,jdbcType=VARCHAR},
      `method` = #{method,jdbcType=VARCHAR},
      `path` = #{path,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      num = #{num,jdbcType=BIGINT},
      tags = #{tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler},
      pos = #{pos,jdbcType=BIGINT},
      project_id = #{projectId,jdbcType=VARCHAR},
      module_id = #{moduleId,jdbcType=VARCHAR},
      latest = #{latest,jdbcType=BIT},
      version_id = #{versionId,jdbcType=VARCHAR},
      ref_id = #{refId,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=BIGINT},
      update_user = #{updateUser,jdbcType=VARCHAR},
      delete_user = #{deleteUser,jdbcType=VARCHAR},
      delete_time = #{deleteTime,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into api_definition
    (id, `name`, protocol, `method`, `path`, `status`, num, tags, pos, project_id, module_id, 
      latest, version_id, ref_id, description, create_time, create_user, update_time, 
      update_user, delete_user, delete_time, deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.protocol,jdbcType=VARCHAR}, 
        #{item.method,jdbcType=VARCHAR}, #{item.path,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR}, 
        #{item.num,jdbcType=BIGINT}, #{item.tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler}, 
        #{item.pos,jdbcType=BIGINT}, #{item.projectId,jdbcType=VARCHAR}, #{item.moduleId,jdbcType=VARCHAR}, 
        #{item.latest,jdbcType=BIT}, #{item.versionId,jdbcType=VARCHAR}, #{item.refId,jdbcType=VARCHAR}, 
        #{item.description,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT}, #{item.createUser,jdbcType=VARCHAR}, 
        #{item.updateTime,jdbcType=BIGINT}, #{item.updateUser,jdbcType=VARCHAR}, #{item.deleteUser,jdbcType=VARCHAR}, 
        #{item.deleteTime,jdbcType=BIGINT}, #{item.deleted,jdbcType=BIT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into api_definition (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'protocol'.toString() == column.value">
          #{item.protocol,jdbcType=VARCHAR}
        </if>
        <if test="'method'.toString() == column.value">
          #{item.method,jdbcType=VARCHAR}
        </if>
        <if test="'path'.toString() == column.value">
          #{item.path,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=VARCHAR}
        </if>
        <if test="'num'.toString() == column.value">
          #{item.num,jdbcType=BIGINT}
        </if>
        <if test="'tags'.toString() == column.value">
          #{item.tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler}
        </if>
        <if test="'pos'.toString() == column.value">
          #{item.pos,jdbcType=BIGINT}
        </if>
        <if test="'project_id'.toString() == column.value">
          #{item.projectId,jdbcType=VARCHAR}
        </if>
        <if test="'module_id'.toString() == column.value">
          #{item.moduleId,jdbcType=VARCHAR}
        </if>
        <if test="'latest'.toString() == column.value">
          #{item.latest,jdbcType=BIT}
        </if>
        <if test="'version_id'.toString() == column.value">
          #{item.versionId,jdbcType=VARCHAR}
        </if>
        <if test="'ref_id'.toString() == column.value">
          #{item.refId,jdbcType=VARCHAR}
        </if>
        <if test="'description'.toString() == column.value">
          #{item.description,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'create_user'.toString() == column.value">
          #{item.createUser,jdbcType=VARCHAR}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=BIGINT}
        </if>
        <if test="'update_user'.toString() == column.value">
          #{item.updateUser,jdbcType=VARCHAR}
        </if>
        <if test="'delete_user'.toString() == column.value">
          #{item.deleteUser,jdbcType=VARCHAR}
        </if>
        <if test="'delete_time'.toString() == column.value">
          #{item.deleteTime,jdbcType=BIGINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=BIT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>