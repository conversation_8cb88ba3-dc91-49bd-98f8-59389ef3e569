<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.functional.mapper.FunctionalCaseMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.functional.domain.FunctionalCase">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="num" jdbcType="BIGINT" property="num" />
    <result column="module_id" jdbcType="VARCHAR" property="moduleId" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="template_id" jdbcType="VARCHAR" property="templateId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="review_status" jdbcType="VARCHAR" property="reviewStatus" />
    <result column="tags" jdbcType="VARCHAR" property="tags" typeHandler="io.metersphere.handler.ListTypeHandler" />
    <result column="case_edit_type" jdbcType="VARCHAR" property="caseEditType" />
    <result column="pos" jdbcType="BIGINT" property="pos" />
    <result column="version_id" jdbcType="VARCHAR" property="versionId" />
    <result column="ref_id" jdbcType="VARCHAR" property="refId" />
    <result column="last_execute_result" jdbcType="VARCHAR" property="lastExecuteResult" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="ai_create" jdbcType="BIT" property="aiCreate" />
    <result column="public_case" jdbcType="BIT" property="publicCase" />
    <result column="latest" jdbcType="BIT" property="latest" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="delete_user" jdbcType="VARCHAR" property="deleteUser" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="delete_time" jdbcType="BIGINT" property="deleteTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.tagsCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=io.metersphere.handler.ListTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=io.metersphere.handler.ListTypeHandler} and #{criterion.secondValue,typeHandler=io.metersphere.handler.ListTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=io.metersphere.handler.ListTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.tagsCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=io.metersphere.handler.ListTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=io.metersphere.handler.ListTypeHandler} and #{criterion.secondValue,typeHandler=io.metersphere.handler.ListTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=io.metersphere.handler.ListTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, num, module_id, project_id, template_id, `name`, review_status, tags, case_edit_type, 
    pos, version_id, ref_id, last_execute_result, deleted, ai_create, public_case, latest, 
    create_user, update_user, delete_user, create_time, update_time, delete_time
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.functional.domain.FunctionalCaseExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from functional_case
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from functional_case
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from functional_case
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.functional.domain.FunctionalCaseExample">
    delete from functional_case
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.functional.domain.FunctionalCase">
    insert into functional_case (id, num, module_id, 
      project_id, template_id, `name`, 
      review_status, tags, 
      case_edit_type, pos, version_id, 
      ref_id, last_execute_result, deleted, 
      ai_create, public_case, latest, create_user, 
      update_user, delete_user, create_time, 
      update_time, delete_time)
    values (#{id,jdbcType=VARCHAR}, #{num,jdbcType=BIGINT}, #{moduleId,jdbcType=VARCHAR}, 
      #{projectId,jdbcType=VARCHAR}, #{templateId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{reviewStatus,jdbcType=VARCHAR}, #{tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler}, 
      #{caseEditType,jdbcType=VARCHAR}, #{pos,jdbcType=BIGINT}, #{versionId,jdbcType=VARCHAR}, 
      #{refId,jdbcType=VARCHAR}, #{lastExecuteResult,jdbcType=VARCHAR}, #{deleted,jdbcType=BIT}, 
      #{aiCreate,jdbcType=BIT}, #{publicCase,jdbcType=BIT}, #{latest,jdbcType=BIT}, #{createUser,jdbcType=VARCHAR}, 
      #{updateUser,jdbcType=VARCHAR}, #{deleteUser,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT}, #{deleteTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.functional.domain.FunctionalCase">
    insert into functional_case
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="moduleId != null">
        module_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="reviewStatus != null">
        review_status,
      </if>
      <if test="tags != null">
        tags,
      </if>
      <if test="caseEditType != null">
        case_edit_type,
      </if>
      <if test="pos != null">
        pos,
      </if>
      <if test="versionId != null">
        version_id,
      </if>
      <if test="refId != null">
        ref_id,
      </if>
      <if test="lastExecuteResult != null">
        last_execute_result,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="aiCreate != null">
        ai_create,
      </if>
      <if test="publicCase != null">
        public_case,
      </if>
      <if test="latest != null">
        latest,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="deleteUser != null">
        delete_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleteTime != null">
        delete_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=BIGINT},
      </if>
      <if test="moduleId != null">
        #{moduleId,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="reviewStatus != null">
        #{reviewStatus,jdbcType=VARCHAR},
      </if>
      <if test="tags != null">
        #{tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler},
      </if>
      <if test="caseEditType != null">
        #{caseEditType,jdbcType=VARCHAR},
      </if>
      <if test="pos != null">
        #{pos,jdbcType=BIGINT},
      </if>
      <if test="versionId != null">
        #{versionId,jdbcType=VARCHAR},
      </if>
      <if test="refId != null">
        #{refId,jdbcType=VARCHAR},
      </if>
      <if test="lastExecuteResult != null">
        #{lastExecuteResult,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="aiCreate != null">
        #{aiCreate,jdbcType=BIT},
      </if>
      <if test="publicCase != null">
        #{publicCase,jdbcType=BIT},
      </if>
      <if test="latest != null">
        #{latest,jdbcType=BIT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="deleteUser != null">
        #{deleteUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="deleteTime != null">
        #{deleteTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.functional.domain.FunctionalCaseExample" resultType="java.lang.Long">
    select count(*) from functional_case
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update functional_case
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.num != null">
        num = #{record.num,jdbcType=BIGINT},
      </if>
      <if test="record.moduleId != null">
        module_id = #{record.moduleId,jdbcType=VARCHAR},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=VARCHAR},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewStatus != null">
        review_status = #{record.reviewStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.tags != null">
        tags = #{record.tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler},
      </if>
      <if test="record.caseEditType != null">
        case_edit_type = #{record.caseEditType,jdbcType=VARCHAR},
      </if>
      <if test="record.pos != null">
        pos = #{record.pos,jdbcType=BIGINT},
      </if>
      <if test="record.versionId != null">
        version_id = #{record.versionId,jdbcType=VARCHAR},
      </if>
      <if test="record.refId != null">
        ref_id = #{record.refId,jdbcType=VARCHAR},
      </if>
      <if test="record.lastExecuteResult != null">
        last_execute_result = #{record.lastExecuteResult,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.aiCreate != null">
        ai_create = #{record.aiCreate,jdbcType=BIT},
      </if>
      <if test="record.publicCase != null">
        public_case = #{record.publicCase,jdbcType=BIT},
      </if>
      <if test="record.latest != null">
        latest = #{record.latest,jdbcType=BIT},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.deleteUser != null">
        delete_user = #{record.deleteUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.deleteTime != null">
        delete_time = #{record.deleteTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update functional_case
    set id = #{record.id,jdbcType=VARCHAR},
      num = #{record.num,jdbcType=BIGINT},
      module_id = #{record.moduleId,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=VARCHAR},
      template_id = #{record.templateId,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      review_status = #{record.reviewStatus,jdbcType=VARCHAR},
      tags = #{record.tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler},
      case_edit_type = #{record.caseEditType,jdbcType=VARCHAR},
      pos = #{record.pos,jdbcType=BIGINT},
      version_id = #{record.versionId,jdbcType=VARCHAR},
      ref_id = #{record.refId,jdbcType=VARCHAR},
      last_execute_result = #{record.lastExecuteResult,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=BIT},
      ai_create = #{record.aiCreate,jdbcType=BIT},
      public_case = #{record.publicCase,jdbcType=BIT},
      latest = #{record.latest,jdbcType=BIT},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      delete_user = #{record.deleteUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      delete_time = #{record.deleteTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.functional.domain.FunctionalCase">
    update functional_case
    <set>
      <if test="num != null">
        num = #{num,jdbcType=BIGINT},
      </if>
      <if test="moduleId != null">
        module_id = #{moduleId,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="reviewStatus != null">
        review_status = #{reviewStatus,jdbcType=VARCHAR},
      </if>
      <if test="tags != null">
        tags = #{tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler},
      </if>
      <if test="caseEditType != null">
        case_edit_type = #{caseEditType,jdbcType=VARCHAR},
      </if>
      <if test="pos != null">
        pos = #{pos,jdbcType=BIGINT},
      </if>
      <if test="versionId != null">
        version_id = #{versionId,jdbcType=VARCHAR},
      </if>
      <if test="refId != null">
        ref_id = #{refId,jdbcType=VARCHAR},
      </if>
      <if test="lastExecuteResult != null">
        last_execute_result = #{lastExecuteResult,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="aiCreate != null">
        ai_create = #{aiCreate,jdbcType=BIT},
      </if>
      <if test="publicCase != null">
        public_case = #{publicCase,jdbcType=BIT},
      </if>
      <if test="latest != null">
        latest = #{latest,jdbcType=BIT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="deleteUser != null">
        delete_user = #{deleteUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="deleteTime != null">
        delete_time = #{deleteTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.functional.domain.FunctionalCase">
    update functional_case
    set num = #{num,jdbcType=BIGINT},
      module_id = #{moduleId,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=VARCHAR},
      template_id = #{templateId,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      review_status = #{reviewStatus,jdbcType=VARCHAR},
      tags = #{tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler},
      case_edit_type = #{caseEditType,jdbcType=VARCHAR},
      pos = #{pos,jdbcType=BIGINT},
      version_id = #{versionId,jdbcType=VARCHAR},
      ref_id = #{refId,jdbcType=VARCHAR},
      last_execute_result = #{lastExecuteResult,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIT},
      ai_create = #{aiCreate,jdbcType=BIT},
      public_case = #{publicCase,jdbcType=BIT},
      latest = #{latest,jdbcType=BIT},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      delete_user = #{deleteUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      delete_time = #{deleteTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into functional_case
    (id, num, module_id, project_id, template_id, `name`, review_status, tags, case_edit_type, 
      pos, version_id, ref_id, last_execute_result, deleted, ai_create, public_case, 
      latest, create_user, update_user, delete_user, create_time, update_time, delete_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.num,jdbcType=BIGINT}, #{item.moduleId,jdbcType=VARCHAR}, 
        #{item.projectId,jdbcType=VARCHAR}, #{item.templateId,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.reviewStatus,jdbcType=VARCHAR}, #{item.tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler}, 
        #{item.caseEditType,jdbcType=VARCHAR}, #{item.pos,jdbcType=BIGINT}, #{item.versionId,jdbcType=VARCHAR}, 
        #{item.refId,jdbcType=VARCHAR}, #{item.lastExecuteResult,jdbcType=VARCHAR}, #{item.deleted,jdbcType=BIT}, 
        #{item.aiCreate,jdbcType=BIT}, #{item.publicCase,jdbcType=BIT}, #{item.latest,jdbcType=BIT}, 
        #{item.createUser,jdbcType=VARCHAR}, #{item.updateUser,jdbcType=VARCHAR}, #{item.deleteUser,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}, #{item.deleteTime,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into functional_case (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'num'.toString() == column.value">
          #{item.num,jdbcType=BIGINT}
        </if>
        <if test="'module_id'.toString() == column.value">
          #{item.moduleId,jdbcType=VARCHAR}
        </if>
        <if test="'project_id'.toString() == column.value">
          #{item.projectId,jdbcType=VARCHAR}
        </if>
        <if test="'template_id'.toString() == column.value">
          #{item.templateId,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'review_status'.toString() == column.value">
          #{item.reviewStatus,jdbcType=VARCHAR}
        </if>
        <if test="'tags'.toString() == column.value">
          #{item.tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler}
        </if>
        <if test="'case_edit_type'.toString() == column.value">
          #{item.caseEditType,jdbcType=VARCHAR}
        </if>
        <if test="'pos'.toString() == column.value">
          #{item.pos,jdbcType=BIGINT}
        </if>
        <if test="'version_id'.toString() == column.value">
          #{item.versionId,jdbcType=VARCHAR}
        </if>
        <if test="'ref_id'.toString() == column.value">
          #{item.refId,jdbcType=VARCHAR}
        </if>
        <if test="'last_execute_result'.toString() == column.value">
          #{item.lastExecuteResult,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=BIT}
        </if>
        <if test="'ai_create'.toString() == column.value">
          #{item.aiCreate,jdbcType=BIT}
        </if>
        <if test="'public_case'.toString() == column.value">
          #{item.publicCase,jdbcType=BIT}
        </if>
        <if test="'latest'.toString() == column.value">
          #{item.latest,jdbcType=BIT}
        </if>
        <if test="'create_user'.toString() == column.value">
          #{item.createUser,jdbcType=VARCHAR}
        </if>
        <if test="'update_user'.toString() == column.value">
          #{item.updateUser,jdbcType=VARCHAR}
        </if>
        <if test="'delete_user'.toString() == column.value">
          #{item.deleteUser,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=BIGINT}
        </if>
        <if test="'delete_time'.toString() == column.value">
          #{item.deleteTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>