<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.project.mapper.FakeErrorMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.project.domain.FakeError">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="resp_type" jdbcType="VARCHAR" property="respType" />
    <result column="relation" jdbcType="VARCHAR" property="relation" />
    <result column="expression" jdbcType="VARCHAR" property="expression" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, `name`, create_time, update_time, create_user, update_user, `type`, 
    resp_type, relation, expression, `enable`
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.project.domain.FakeErrorExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from fake_error
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fake_error
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fake_error
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.project.domain.FakeErrorExample">
    delete from fake_error
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.project.domain.FakeError">
    insert into fake_error (id, project_id, `name`, 
      create_time, update_time, create_user, 
      update_user, `type`, resp_type, 
      relation, expression, `enable`
      )
    values (#{id,jdbcType=VARCHAR}, #{projectId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{createUser,jdbcType=VARCHAR}, 
      #{updateUser,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{respType,jdbcType=VARCHAR}, 
      #{relation,jdbcType=VARCHAR}, #{expression,jdbcType=VARCHAR}, #{enable,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.project.domain.FakeError">
    insert into fake_error
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="respType != null">
        resp_type,
      </if>
      <if test="relation != null">
        relation,
      </if>
      <if test="expression != null">
        expression,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="respType != null">
        #{respType,jdbcType=VARCHAR},
      </if>
      <if test="relation != null">
        #{relation,jdbcType=VARCHAR},
      </if>
      <if test="expression != null">
        #{expression,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.project.domain.FakeErrorExample" resultType="java.lang.Long">
    select count(*) from fake_error
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update fake_error
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.respType != null">
        resp_type = #{record.respType,jdbcType=VARCHAR},
      </if>
      <if test="record.relation != null">
        relation = #{record.relation,jdbcType=VARCHAR},
      </if>
      <if test="record.expression != null">
        expression = #{record.expression,jdbcType=VARCHAR},
      </if>
      <if test="record.enable != null">
        `enable` = #{record.enable,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update fake_error
    set id = #{record.id,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      `type` = #{record.type,jdbcType=VARCHAR},
      resp_type = #{record.respType,jdbcType=VARCHAR},
      relation = #{record.relation,jdbcType=VARCHAR},
      expression = #{record.expression,jdbcType=VARCHAR},
      `enable` = #{record.enable,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.project.domain.FakeError">
    update fake_error
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="respType != null">
        resp_type = #{respType,jdbcType=VARCHAR},
      </if>
      <if test="relation != null">
        relation = #{relation,jdbcType=VARCHAR},
      </if>
      <if test="expression != null">
        expression = #{expression,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.project.domain.FakeError">
    update fake_error
    set project_id = #{projectId,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      resp_type = #{respType,jdbcType=VARCHAR},
      relation = #{relation,jdbcType=VARCHAR},
      expression = #{expression,jdbcType=VARCHAR},
      `enable` = #{enable,jdbcType=BIT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into fake_error
    (id, project_id, `name`, create_time, update_time, create_user, update_user, `type`, 
      resp_type, relation, expression, `enable`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.projectId,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}, #{item.createUser,jdbcType=VARCHAR}, 
        #{item.updateUser,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR}, #{item.respType,jdbcType=VARCHAR}, 
        #{item.relation,jdbcType=VARCHAR}, #{item.expression,jdbcType=VARCHAR}, #{item.enable,jdbcType=BIT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into fake_error (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'project_id'.toString() == column.value">
          #{item.projectId,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=BIGINT}
        </if>
        <if test="'create_user'.toString() == column.value">
          #{item.createUser,jdbcType=VARCHAR}
        </if>
        <if test="'update_user'.toString() == column.value">
          #{item.updateUser,jdbcType=VARCHAR}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=VARCHAR}
        </if>
        <if test="'resp_type'.toString() == column.value">
          #{item.respType,jdbcType=VARCHAR}
        </if>
        <if test="'relation'.toString() == column.value">
          #{item.relation,jdbcType=VARCHAR}
        </if>
        <if test="'expression'.toString() == column.value">
          #{item.expression,jdbcType=VARCHAR}
        </if>
        <if test="'enable'.toString() == column.value">
          #{item.enable,jdbcType=BIT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>