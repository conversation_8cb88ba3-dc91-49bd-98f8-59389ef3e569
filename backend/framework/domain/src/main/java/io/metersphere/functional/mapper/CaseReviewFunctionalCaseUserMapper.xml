<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.functional.mapper.CaseReviewFunctionalCaseUserMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.functional.domain.CaseReviewFunctionalCaseUser">
    <result column="case_id" jdbcType="VARCHAR" property="caseId" />
    <result column="review_id" jdbcType="VARCHAR" property="reviewId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    case_id, review_id, user_id
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.functional.domain.CaseReviewFunctionalCaseUserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from case_review_functional_case_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="io.metersphere.functional.domain.CaseReviewFunctionalCaseUserExample">
    delete from case_review_functional_case_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.functional.domain.CaseReviewFunctionalCaseUser">
    insert into case_review_functional_case_user (case_id, review_id, user_id
      )
    values (#{caseId,jdbcType=VARCHAR}, #{reviewId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.functional.domain.CaseReviewFunctionalCaseUser">
    insert into case_review_functional_case_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        case_id,
      </if>
      <if test="reviewId != null">
        review_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        #{caseId,jdbcType=VARCHAR},
      </if>
      <if test="reviewId != null">
        #{reviewId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.functional.domain.CaseReviewFunctionalCaseUserExample" resultType="java.lang.Long">
    select count(*) from case_review_functional_case_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update case_review_functional_case_user
    <set>
      <if test="record.caseId != null">
        case_id = #{record.caseId,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewId != null">
        review_id = #{record.reviewId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update case_review_functional_case_user
    set case_id = #{record.caseId,jdbcType=VARCHAR},
      review_id = #{record.reviewId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into case_review_functional_case_user
    (case_id, review_id, user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.caseId,jdbcType=VARCHAR}, #{item.reviewId,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into case_review_functional_case_user (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'case_id'.toString() == column.value">
          #{item.caseId,jdbcType=VARCHAR}
        </if>
        <if test="'review_id'.toString() == column.value">
          #{item.reviewId,jdbcType=VARCHAR}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>