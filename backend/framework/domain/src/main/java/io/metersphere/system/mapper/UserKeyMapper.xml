<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.system.mapper.UserKeyMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.system.domain.UserKey">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="access_key" jdbcType="VARCHAR" property="accessKey" />
    <result column="secret_key" jdbcType="VARCHAR" property="secretKey" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="forever" jdbcType="BIT" property="forever" />
    <result column="expire_time" jdbcType="BIGINT" property="expireTime" />
    <result column="description" jdbcType="VARCHAR" property="description" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, create_user, access_key, secret_key, create_time, `enable`, forever, expire_time, 
    description
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.system.domain.UserKeyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_key
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_key
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from user_key
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.system.domain.UserKeyExample">
    delete from user_key
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.system.domain.UserKey">
    insert into user_key (id, create_user, access_key, 
      secret_key, create_time, `enable`, 
      forever, expire_time, description
      )
    values (#{id,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, #{accessKey,jdbcType=VARCHAR}, 
      #{secretKey,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, #{enable,jdbcType=BIT}, 
      #{forever,jdbcType=BIT}, #{expireTime,jdbcType=BIGINT}, #{description,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.system.domain.UserKey">
    insert into user_key
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="accessKey != null">
        access_key,
      </if>
      <if test="secretKey != null">
        secret_key,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="forever != null">
        forever,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
      <if test="description != null">
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="accessKey != null">
        #{accessKey,jdbcType=VARCHAR},
      </if>
      <if test="secretKey != null">
        #{secretKey,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="forever != null">
        #{forever,jdbcType=BIT},
      </if>
      <if test="expireTime != null">
        #{expireTime,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.system.domain.UserKeyExample" resultType="java.lang.Long">
    select count(*) from user_key
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update user_key
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.accessKey != null">
        access_key = #{record.accessKey,jdbcType=VARCHAR},
      </if>
      <if test="record.secretKey != null">
        secret_key = #{record.secretKey,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.enable != null">
        `enable` = #{record.enable,jdbcType=BIT},
      </if>
      <if test="record.forever != null">
        forever = #{record.forever,jdbcType=BIT},
      </if>
      <if test="record.expireTime != null">
        expire_time = #{record.expireTime,jdbcType=BIGINT},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update user_key
    set id = #{record.id,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      access_key = #{record.accessKey,jdbcType=VARCHAR},
      secret_key = #{record.secretKey,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      `enable` = #{record.enable,jdbcType=BIT},
      forever = #{record.forever,jdbcType=BIT},
      expire_time = #{record.expireTime,jdbcType=BIGINT},
      description = #{record.description,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.system.domain.UserKey">
    update user_key
    <set>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="accessKey != null">
        access_key = #{accessKey,jdbcType=VARCHAR},
      </if>
      <if test="secretKey != null">
        secret_key = #{secretKey,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="forever != null">
        forever = #{forever,jdbcType=BIT},
      </if>
      <if test="expireTime != null">
        expire_time = #{expireTime,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.system.domain.UserKey">
    update user_key
    set create_user = #{createUser,jdbcType=VARCHAR},
      access_key = #{accessKey,jdbcType=VARCHAR},
      secret_key = #{secretKey,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      `enable` = #{enable,jdbcType=BIT},
      forever = #{forever,jdbcType=BIT},
      expire_time = #{expireTime,jdbcType=BIGINT},
      description = #{description,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into user_key
    (id, create_user, access_key, secret_key, create_time, `enable`, forever, expire_time, 
      description)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.createUser,jdbcType=VARCHAR}, #{item.accessKey,jdbcType=VARCHAR}, 
        #{item.secretKey,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT}, #{item.enable,jdbcType=BIT}, 
        #{item.forever,jdbcType=BIT}, #{item.expireTime,jdbcType=BIGINT}, #{item.description,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into user_key (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'create_user'.toString() == column.value">
          #{item.createUser,jdbcType=VARCHAR}
        </if>
        <if test="'access_key'.toString() == column.value">
          #{item.accessKey,jdbcType=VARCHAR}
        </if>
        <if test="'secret_key'.toString() == column.value">
          #{item.secretKey,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'enable'.toString() == column.value">
          #{item.enable,jdbcType=BIT}
        </if>
        <if test="'forever'.toString() == column.value">
          #{item.forever,jdbcType=BIT}
        </if>
        <if test="'expire_time'.toString() == column.value">
          #{item.expireTime,jdbcType=BIGINT}
        </if>
        <if test="'description'.toString() == column.value">
          #{item.description,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>