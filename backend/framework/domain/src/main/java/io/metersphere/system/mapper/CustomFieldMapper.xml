<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.system.mapper.CustomFieldMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.system.domain.CustomField">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="scene" jdbcType="VARCHAR" property="scene" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="internal" jdbcType="BIT" property="internal" />
    <result column="scope_type" jdbcType="VARCHAR" property="scopeType" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="ref_id" jdbcType="VARCHAR" property="refId" />
    <result column="enable_option_key" jdbcType="BIT" property="enableOptionKey" />
    <result column="scope_id" jdbcType="VARCHAR" property="scopeId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, `name`, scene, `type`, remark, internal, scope_type, create_time, update_time, 
    create_user, ref_id, enable_option_key, scope_id
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.system.domain.CustomFieldExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from custom_field
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from custom_field
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from custom_field
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.system.domain.CustomFieldExample">
    delete from custom_field
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.system.domain.CustomField">
    insert into custom_field (id, `name`, scene, 
      `type`, remark, internal, 
      scope_type, create_time, update_time, 
      create_user, ref_id, enable_option_key, 
      scope_id)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{scene,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{internal,jdbcType=BIT}, 
      #{scopeType,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, 
      #{createUser,jdbcType=VARCHAR}, #{refId,jdbcType=VARCHAR}, #{enableOptionKey,jdbcType=BIT}, 
      #{scopeId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.system.domain.CustomField">
    insert into custom_field
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="scene != null">
        scene,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="internal != null">
        internal,
      </if>
      <if test="scopeType != null">
        scope_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="refId != null">
        ref_id,
      </if>
      <if test="enableOptionKey != null">
        enable_option_key,
      </if>
      <if test="scopeId != null">
        scope_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="scene != null">
        #{scene,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="internal != null">
        #{internal,jdbcType=BIT},
      </if>
      <if test="scopeType != null">
        #{scopeType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="refId != null">
        #{refId,jdbcType=VARCHAR},
      </if>
      <if test="enableOptionKey != null">
        #{enableOptionKey,jdbcType=BIT},
      </if>
      <if test="scopeId != null">
        #{scopeId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.system.domain.CustomFieldExample" resultType="java.lang.Long">
    select count(*) from custom_field
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update custom_field
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.scene != null">
        scene = #{record.scene,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.internal != null">
        internal = #{record.internal,jdbcType=BIT},
      </if>
      <if test="record.scopeType != null">
        scope_type = #{record.scopeType,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.refId != null">
        ref_id = #{record.refId,jdbcType=VARCHAR},
      </if>
      <if test="record.enableOptionKey != null">
        enable_option_key = #{record.enableOptionKey,jdbcType=BIT},
      </if>
      <if test="record.scopeId != null">
        scope_id = #{record.scopeId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update custom_field
    set id = #{record.id,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      scene = #{record.scene,jdbcType=VARCHAR},
      `type` = #{record.type,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      internal = #{record.internal,jdbcType=BIT},
      scope_type = #{record.scopeType,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      ref_id = #{record.refId,jdbcType=VARCHAR},
      enable_option_key = #{record.enableOptionKey,jdbcType=BIT},
      scope_id = #{record.scopeId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.system.domain.CustomField">
    update custom_field
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="scene != null">
        scene = #{scene,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="internal != null">
        internal = #{internal,jdbcType=BIT},
      </if>
      <if test="scopeType != null">
        scope_type = #{scopeType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="refId != null">
        ref_id = #{refId,jdbcType=VARCHAR},
      </if>
      <if test="enableOptionKey != null">
        enable_option_key = #{enableOptionKey,jdbcType=BIT},
      </if>
      <if test="scopeId != null">
        scope_id = #{scopeId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.system.domain.CustomField">
    update custom_field
    set `name` = #{name,jdbcType=VARCHAR},
      scene = #{scene,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      internal = #{internal,jdbcType=BIT},
      scope_type = #{scopeType,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      ref_id = #{refId,jdbcType=VARCHAR},
      enable_option_key = #{enableOptionKey,jdbcType=BIT},
      scope_id = #{scopeId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into custom_field
    (id, `name`, scene, `type`, remark, internal, scope_type, create_time, update_time, 
      create_user, ref_id, enable_option_key, scope_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.scene,jdbcType=VARCHAR}, 
        #{item.type,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.internal,jdbcType=BIT}, 
        #{item.scopeType,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}, 
        #{item.createUser,jdbcType=VARCHAR}, #{item.refId,jdbcType=VARCHAR}, #{item.enableOptionKey,jdbcType=BIT}, 
        #{item.scopeId,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into custom_field (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'scene'.toString() == column.value">
          #{item.scene,jdbcType=VARCHAR}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'internal'.toString() == column.value">
          #{item.internal,jdbcType=BIT}
        </if>
        <if test="'scope_type'.toString() == column.value">
          #{item.scopeType,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=BIGINT}
        </if>
        <if test="'create_user'.toString() == column.value">
          #{item.createUser,jdbcType=VARCHAR}
        </if>
        <if test="'ref_id'.toString() == column.value">
          #{item.refId,jdbcType=VARCHAR}
        </if>
        <if test="'enable_option_key'.toString() == column.value">
          #{item.enableOptionKey,jdbcType=BIT}
        </if>
        <if test="'scope_id'.toString() == column.value">
          #{item.scopeId,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>