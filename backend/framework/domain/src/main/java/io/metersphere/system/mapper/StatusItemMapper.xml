<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.system.mapper.StatusItemMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.system.domain.StatusItem">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="scene" jdbcType="VARCHAR" property="scene" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="internal" jdbcType="BIT" property="internal" />
    <result column="scope_type" jdbcType="VARCHAR" property="scopeType" />
    <result column="ref_id" jdbcType="VARCHAR" property="refId" />
    <result column="scope_id" jdbcType="VARCHAR" property="scopeId" />
    <result column="pos" jdbcType="INTEGER" property="pos" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, `name`, scene, remark, internal, scope_type, ref_id, scope_id, pos
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.system.domain.StatusItemExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from status_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from status_item
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from status_item
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.system.domain.StatusItemExample">
    delete from status_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.system.domain.StatusItem">
    insert into status_item (id, `name`, scene, 
      remark, internal, scope_type, 
      ref_id, scope_id, pos
      )
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{scene,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{internal,jdbcType=BIT}, #{scopeType,jdbcType=VARCHAR}, 
      #{refId,jdbcType=VARCHAR}, #{scopeId,jdbcType=VARCHAR}, #{pos,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.system.domain.StatusItem">
    insert into status_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="scene != null">
        scene,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="internal != null">
        internal,
      </if>
      <if test="scopeType != null">
        scope_type,
      </if>
      <if test="refId != null">
        ref_id,
      </if>
      <if test="scopeId != null">
        scope_id,
      </if>
      <if test="pos != null">
        pos,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="scene != null">
        #{scene,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="internal != null">
        #{internal,jdbcType=BIT},
      </if>
      <if test="scopeType != null">
        #{scopeType,jdbcType=VARCHAR},
      </if>
      <if test="refId != null">
        #{refId,jdbcType=VARCHAR},
      </if>
      <if test="scopeId != null">
        #{scopeId,jdbcType=VARCHAR},
      </if>
      <if test="pos != null">
        #{pos,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.system.domain.StatusItemExample" resultType="java.lang.Long">
    select count(*) from status_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update status_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.scene != null">
        scene = #{record.scene,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.internal != null">
        internal = #{record.internal,jdbcType=BIT},
      </if>
      <if test="record.scopeType != null">
        scope_type = #{record.scopeType,jdbcType=VARCHAR},
      </if>
      <if test="record.refId != null">
        ref_id = #{record.refId,jdbcType=VARCHAR},
      </if>
      <if test="record.scopeId != null">
        scope_id = #{record.scopeId,jdbcType=VARCHAR},
      </if>
      <if test="record.pos != null">
        pos = #{record.pos,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update status_item
    set id = #{record.id,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      scene = #{record.scene,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      internal = #{record.internal,jdbcType=BIT},
      scope_type = #{record.scopeType,jdbcType=VARCHAR},
      ref_id = #{record.refId,jdbcType=VARCHAR},
      scope_id = #{record.scopeId,jdbcType=VARCHAR},
      pos = #{record.pos,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.system.domain.StatusItem">
    update status_item
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="scene != null">
        scene = #{scene,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="internal != null">
        internal = #{internal,jdbcType=BIT},
      </if>
      <if test="scopeType != null">
        scope_type = #{scopeType,jdbcType=VARCHAR},
      </if>
      <if test="refId != null">
        ref_id = #{refId,jdbcType=VARCHAR},
      </if>
      <if test="scopeId != null">
        scope_id = #{scopeId,jdbcType=VARCHAR},
      </if>
      <if test="pos != null">
        pos = #{pos,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.system.domain.StatusItem">
    update status_item
    set `name` = #{name,jdbcType=VARCHAR},
      scene = #{scene,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      internal = #{internal,jdbcType=BIT},
      scope_type = #{scopeType,jdbcType=VARCHAR},
      ref_id = #{refId,jdbcType=VARCHAR},
      scope_id = #{scopeId,jdbcType=VARCHAR},
      pos = #{pos,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into status_item
    (id, `name`, scene, remark, internal, scope_type, ref_id, scope_id, pos)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.scene,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.internal,jdbcType=BIT}, #{item.scopeType,jdbcType=VARCHAR}, 
        #{item.refId,jdbcType=VARCHAR}, #{item.scopeId,jdbcType=VARCHAR}, #{item.pos,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into status_item (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'scene'.toString() == column.value">
          #{item.scene,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'internal'.toString() == column.value">
          #{item.internal,jdbcType=BIT}
        </if>
        <if test="'scope_type'.toString() == column.value">
          #{item.scopeType,jdbcType=VARCHAR}
        </if>
        <if test="'ref_id'.toString() == column.value">
          #{item.refId,jdbcType=VARCHAR}
        </if>
        <if test="'scope_id'.toString() == column.value">
          #{item.scopeId,jdbcType=VARCHAR}
        </if>
        <if test="'pos'.toString() == column.value">
          #{item.pos,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>