<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.plan.mapper.TestPlanReportAttachmentMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.plan.domain.TestPlanReportAttachment">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="test_plan_report_id" jdbcType="VARCHAR" property="testPlanReportId" />
    <result column="file_id" jdbcType="VARCHAR" property="fileId" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="size" jdbcType="BIGINT" property="size" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, test_plan_report_id, file_id, file_name, `size`, `source`, create_user, create_time
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.plan.domain.TestPlanReportAttachmentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from test_plan_report_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from test_plan_report_attachment
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from test_plan_report_attachment
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.plan.domain.TestPlanReportAttachmentExample">
    delete from test_plan_report_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.plan.domain.TestPlanReportAttachment">
    insert into test_plan_report_attachment (id, test_plan_report_id, file_id, 
      file_name, `size`, `source`, 
      create_user, create_time)
    values (#{id,jdbcType=VARCHAR}, #{testPlanReportId,jdbcType=VARCHAR}, #{fileId,jdbcType=VARCHAR}, 
      #{fileName,jdbcType=VARCHAR}, #{size,jdbcType=BIGINT}, #{source,jdbcType=VARCHAR}, 
      #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.plan.domain.TestPlanReportAttachment">
    insert into test_plan_report_attachment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="testPlanReportId != null">
        test_plan_report_id,
      </if>
      <if test="fileId != null">
        file_id,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="size != null">
        `size`,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="testPlanReportId != null">
        #{testPlanReportId,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null">
        #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        #{size,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.plan.domain.TestPlanReportAttachmentExample" resultType="java.lang.Long">
    select count(*) from test_plan_report_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update test_plan_report_attachment
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.testPlanReportId != null">
        test_plan_report_id = #{record.testPlanReportId,jdbcType=VARCHAR},
      </if>
      <if test="record.fileId != null">
        file_id = #{record.fileId,jdbcType=VARCHAR},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.size != null">
        `size` = #{record.size,jdbcType=BIGINT},
      </if>
      <if test="record.source != null">
        `source` = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update test_plan_report_attachment
    set id = #{record.id,jdbcType=VARCHAR},
      test_plan_report_id = #{record.testPlanReportId,jdbcType=VARCHAR},
      file_id = #{record.fileId,jdbcType=VARCHAR},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      `size` = #{record.size,jdbcType=BIGINT},
      `source` = #{record.source,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.plan.domain.TestPlanReportAttachment">
    update test_plan_report_attachment
    <set>
      <if test="testPlanReportId != null">
        test_plan_report_id = #{testPlanReportId,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null">
        file_id = #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        `size` = #{size,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.plan.domain.TestPlanReportAttachment">
    update test_plan_report_attachment
    set test_plan_report_id = #{testPlanReportId,jdbcType=VARCHAR},
      file_id = #{fileId,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      `size` = #{size,jdbcType=BIGINT},
      `source` = #{source,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into test_plan_report_attachment
    (id, test_plan_report_id, file_id, file_name, `size`, `source`, create_user, create_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.testPlanReportId,jdbcType=VARCHAR}, #{item.fileId,jdbcType=VARCHAR}, 
        #{item.fileName,jdbcType=VARCHAR}, #{item.size,jdbcType=BIGINT}, #{item.source,jdbcType=VARCHAR}, 
        #{item.createUser,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into test_plan_report_attachment (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'test_plan_report_id'.toString() == column.value">
          #{item.testPlanReportId,jdbcType=VARCHAR}
        </if>
        <if test="'file_id'.toString() == column.value">
          #{item.fileId,jdbcType=VARCHAR}
        </if>
        <if test="'file_name'.toString() == column.value">
          #{item.fileName,jdbcType=VARCHAR}
        </if>
        <if test="'size'.toString() == column.value">
          #{item.size,jdbcType=BIGINT}
        </if>
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=VARCHAR}
        </if>
        <if test="'create_user'.toString() == column.value">
          #{item.createUser,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>