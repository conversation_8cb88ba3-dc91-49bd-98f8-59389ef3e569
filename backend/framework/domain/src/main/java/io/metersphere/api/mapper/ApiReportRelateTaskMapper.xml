<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.api.mapper.ApiReportRelateTaskMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.api.domain.ApiReportRelateTask">
    <id column="task_resource_id" jdbcType="VARCHAR" property="taskResourceId" />
    <id column="report_id" jdbcType="VARCHAR" property="reportId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    task_resource_id, report_id
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.api.domain.ApiReportRelateTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from api_report_relate_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from api_report_relate_task
    where task_resource_id = #{taskResourceId,jdbcType=VARCHAR}
      and report_id = #{reportId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.api.domain.ApiReportRelateTaskExample">
    delete from api_report_relate_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.api.domain.ApiReportRelateTask">
    insert into api_report_relate_task (task_resource_id, report_id)
    values (#{taskResourceId,jdbcType=VARCHAR}, #{reportId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.api.domain.ApiReportRelateTask">
    insert into api_report_relate_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskResourceId != null">
        task_resource_id,
      </if>
      <if test="reportId != null">
        report_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskResourceId != null">
        #{taskResourceId,jdbcType=VARCHAR},
      </if>
      <if test="reportId != null">
        #{reportId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.api.domain.ApiReportRelateTaskExample" resultType="java.lang.Long">
    select count(*) from api_report_relate_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update api_report_relate_task
    <set>
      <if test="record.taskResourceId != null">
        task_resource_id = #{record.taskResourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.reportId != null">
        report_id = #{record.reportId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update api_report_relate_task
    set task_resource_id = #{record.taskResourceId,jdbcType=VARCHAR},
      report_id = #{record.reportId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into api_report_relate_task
    (task_resource_id, report_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.taskResourceId,jdbcType=VARCHAR}, #{item.reportId,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into api_report_relate_task (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'task_resource_id'.toString() == column.value">
          #{item.taskResourceId,jdbcType=VARCHAR}
        </if>
        <if test="'report_id'.toString() == column.value">
          #{item.reportId,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>