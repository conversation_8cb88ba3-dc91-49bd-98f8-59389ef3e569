<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.api.mapper.ApiReportStepMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.api.domain.ApiReportStep">
    <id column="step_id" jdbcType="VARCHAR" property="stepId" />
    <id column="report_id" jdbcType="VARCHAR" property="reportId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sort" jdbcType="BIGINT" property="sort" />
    <result column="step_type" jdbcType="VARCHAR" property="stepType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    step_id, report_id, `name`, sort, step_type
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.api.domain.ApiReportStepExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from api_report_step
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from api_report_step
    where step_id = #{stepId,jdbcType=VARCHAR}
      and report_id = #{reportId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from api_report_step
    where step_id = #{stepId,jdbcType=VARCHAR}
      and report_id = #{reportId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.api.domain.ApiReportStepExample">
    delete from api_report_step
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.api.domain.ApiReportStep">
    insert into api_report_step (step_id, report_id, `name`, 
      sort, step_type)
    values (#{stepId,jdbcType=VARCHAR}, #{reportId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{sort,jdbcType=BIGINT}, #{stepType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.api.domain.ApiReportStep">
    insert into api_report_step
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="stepId != null">
        step_id,
      </if>
      <if test="reportId != null">
        report_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="stepType != null">
        step_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="stepId != null">
        #{stepId,jdbcType=VARCHAR},
      </if>
      <if test="reportId != null">
        #{reportId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=BIGINT},
      </if>
      <if test="stepType != null">
        #{stepType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.api.domain.ApiReportStepExample" resultType="java.lang.Long">
    select count(*) from api_report_step
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update api_report_step
    <set>
      <if test="record.stepId != null">
        step_id = #{record.stepId,jdbcType=VARCHAR},
      </if>
      <if test="record.reportId != null">
        report_id = #{record.reportId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=BIGINT},
      </if>
      <if test="record.stepType != null">
        step_type = #{record.stepType,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update api_report_step
    set step_id = #{record.stepId,jdbcType=VARCHAR},
      report_id = #{record.reportId,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=BIGINT},
      step_type = #{record.stepType,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.api.domain.ApiReportStep">
    update api_report_step
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=BIGINT},
      </if>
      <if test="stepType != null">
        step_type = #{stepType,jdbcType=VARCHAR},
      </if>
    </set>
    where step_id = #{stepId,jdbcType=VARCHAR}
      and report_id = #{reportId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.api.domain.ApiReportStep">
    update api_report_step
    set `name` = #{name,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=BIGINT},
      step_type = #{stepType,jdbcType=VARCHAR}
    where step_id = #{stepId,jdbcType=VARCHAR}
      and report_id = #{reportId,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into api_report_step
    (step_id, report_id, `name`, sort, step_type)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.stepId,jdbcType=VARCHAR}, #{item.reportId,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.sort,jdbcType=BIGINT}, #{item.stepType,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into api_report_step (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'step_id'.toString() == column.value">
          #{item.stepId,jdbcType=VARCHAR}
        </if>
        <if test="'report_id'.toString() == column.value">
          #{item.reportId,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'sort'.toString() == column.value">
          #{item.sort,jdbcType=BIGINT}
        </if>
        <if test="'step_type'.toString() == column.value">
          #{item.stepType,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>