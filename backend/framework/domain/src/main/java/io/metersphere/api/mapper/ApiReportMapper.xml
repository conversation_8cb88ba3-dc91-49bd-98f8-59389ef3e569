<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.api.mapper.ApiReportMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.api.domain.ApiReport">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="test_plan_case_id" jdbcType="VARCHAR" property="testPlanCaseId" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="delete_time" jdbcType="BIGINT" property="deleteTime" />
    <result column="delete_user" jdbcType="VARCHAR" property="deleteUser" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="start_time" jdbcType="BIGINT" property="startTime" />
    <result column="end_time" jdbcType="BIGINT" property="endTime" />
    <result column="request_duration" jdbcType="BIGINT" property="requestDuration" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="trigger_mode" jdbcType="VARCHAR" property="triggerMode" />
    <result column="run_mode" jdbcType="VARCHAR" property="runMode" />
    <result column="pool_id" jdbcType="VARCHAR" property="poolId" />
    <result column="integrated" jdbcType="BIT" property="integrated" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="environment_id" jdbcType="VARCHAR" property="environmentId" />
    <result column="error_count" jdbcType="BIGINT" property="errorCount" />
    <result column="fake_error_count" jdbcType="BIGINT" property="fakeErrorCount" />
    <result column="pending_count" jdbcType="BIGINT" property="pendingCount" />
    <result column="success_count" jdbcType="BIGINT" property="successCount" />
    <result column="assertion_count" jdbcType="BIGINT" property="assertionCount" />
    <result column="assertion_success_count" jdbcType="BIGINT" property="assertionSuccessCount" />
    <result column="request_error_rate" jdbcType="VARCHAR" property="requestErrorRate" />
    <result column="request_pending_rate" jdbcType="VARCHAR" property="requestPendingRate" />
    <result column="request_fake_error_rate" jdbcType="VARCHAR" property="requestFakeErrorRate" />
    <result column="request_pass_rate" jdbcType="VARCHAR" property="requestPassRate" />
    <result column="assertion_pass_rate" jdbcType="VARCHAR" property="assertionPassRate" />
    <result column="script_identifier" jdbcType="VARCHAR" property="scriptIdentifier" />
    <result column="exec_status" jdbcType="VARCHAR" property="execStatus" />
    <result column="plan" jdbcType="BIT" property="plan" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, `name`, test_plan_case_id, create_user, delete_time, delete_user, deleted, update_user, 
    update_time, start_time, end_time, request_duration, `status`, trigger_mode, run_mode, 
    pool_id, integrated, project_id, environment_id, error_count, fake_error_count, pending_count, 
    success_count, assertion_count, assertion_success_count, request_error_rate, request_pending_rate, 
    request_fake_error_rate, request_pass_rate, assertion_pass_rate, script_identifier, 
    exec_status, `plan`
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.api.domain.ApiReportExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from api_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from api_report
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from api_report
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.api.domain.ApiReportExample">
    delete from api_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.api.domain.ApiReport">
    insert into api_report (id, `name`, test_plan_case_id, 
      create_user, delete_time, delete_user, 
      deleted, update_user, update_time, 
      start_time, end_time, request_duration, 
      `status`, trigger_mode, run_mode, 
      pool_id, integrated, project_id, 
      environment_id, error_count, fake_error_count, 
      pending_count, success_count, assertion_count, 
      assertion_success_count, request_error_rate, 
      request_pending_rate, request_fake_error_rate, 
      request_pass_rate, assertion_pass_rate, script_identifier, 
      exec_status, `plan`)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{testPlanCaseId,jdbcType=VARCHAR}, 
      #{createUser,jdbcType=VARCHAR}, #{deleteTime,jdbcType=BIGINT}, #{deleteUser,jdbcType=VARCHAR}, 
      #{deleted,jdbcType=BIT}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=BIGINT}, 
      #{startTime,jdbcType=BIGINT}, #{endTime,jdbcType=BIGINT}, #{requestDuration,jdbcType=BIGINT}, 
      #{status,jdbcType=VARCHAR}, #{triggerMode,jdbcType=VARCHAR}, #{runMode,jdbcType=VARCHAR}, 
      #{poolId,jdbcType=VARCHAR}, #{integrated,jdbcType=BIT}, #{projectId,jdbcType=VARCHAR}, 
      #{environmentId,jdbcType=VARCHAR}, #{errorCount,jdbcType=BIGINT}, #{fakeErrorCount,jdbcType=BIGINT}, 
      #{pendingCount,jdbcType=BIGINT}, #{successCount,jdbcType=BIGINT}, #{assertionCount,jdbcType=BIGINT}, 
      #{assertionSuccessCount,jdbcType=BIGINT}, #{requestErrorRate,jdbcType=VARCHAR}, 
      #{requestPendingRate,jdbcType=VARCHAR}, #{requestFakeErrorRate,jdbcType=VARCHAR}, 
      #{requestPassRate,jdbcType=VARCHAR}, #{assertionPassRate,jdbcType=VARCHAR}, #{scriptIdentifier,jdbcType=VARCHAR}, 
      #{execStatus,jdbcType=VARCHAR}, #{plan,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.api.domain.ApiReport">
    insert into api_report
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="testPlanCaseId != null">
        test_plan_case_id,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="deleteTime != null">
        delete_time,
      </if>
      <if test="deleteUser != null">
        delete_user,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="requestDuration != null">
        request_duration,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="triggerMode != null">
        trigger_mode,
      </if>
      <if test="runMode != null">
        run_mode,
      </if>
      <if test="poolId != null">
        pool_id,
      </if>
      <if test="integrated != null">
        integrated,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="environmentId != null">
        environment_id,
      </if>
      <if test="errorCount != null">
        error_count,
      </if>
      <if test="fakeErrorCount != null">
        fake_error_count,
      </if>
      <if test="pendingCount != null">
        pending_count,
      </if>
      <if test="successCount != null">
        success_count,
      </if>
      <if test="assertionCount != null">
        assertion_count,
      </if>
      <if test="assertionSuccessCount != null">
        assertion_success_count,
      </if>
      <if test="requestErrorRate != null">
        request_error_rate,
      </if>
      <if test="requestPendingRate != null">
        request_pending_rate,
      </if>
      <if test="requestFakeErrorRate != null">
        request_fake_error_rate,
      </if>
      <if test="requestPassRate != null">
        request_pass_rate,
      </if>
      <if test="assertionPassRate != null">
        assertion_pass_rate,
      </if>
      <if test="scriptIdentifier != null">
        script_identifier,
      </if>
      <if test="execStatus != null">
        exec_status,
      </if>
      <if test="plan != null">
        `plan`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="testPlanCaseId != null">
        #{testPlanCaseId,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="deleteTime != null">
        #{deleteTime,jdbcType=BIGINT},
      </if>
      <if test="deleteUser != null">
        #{deleteUser,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=BIGINT},
      </if>
      <if test="requestDuration != null">
        #{requestDuration,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="triggerMode != null">
        #{triggerMode,jdbcType=VARCHAR},
      </if>
      <if test="runMode != null">
        #{runMode,jdbcType=VARCHAR},
      </if>
      <if test="poolId != null">
        #{poolId,jdbcType=VARCHAR},
      </if>
      <if test="integrated != null">
        #{integrated,jdbcType=BIT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="environmentId != null">
        #{environmentId,jdbcType=VARCHAR},
      </if>
      <if test="errorCount != null">
        #{errorCount,jdbcType=BIGINT},
      </if>
      <if test="fakeErrorCount != null">
        #{fakeErrorCount,jdbcType=BIGINT},
      </if>
      <if test="pendingCount != null">
        #{pendingCount,jdbcType=BIGINT},
      </if>
      <if test="successCount != null">
        #{successCount,jdbcType=BIGINT},
      </if>
      <if test="assertionCount != null">
        #{assertionCount,jdbcType=BIGINT},
      </if>
      <if test="assertionSuccessCount != null">
        #{assertionSuccessCount,jdbcType=BIGINT},
      </if>
      <if test="requestErrorRate != null">
        #{requestErrorRate,jdbcType=VARCHAR},
      </if>
      <if test="requestPendingRate != null">
        #{requestPendingRate,jdbcType=VARCHAR},
      </if>
      <if test="requestFakeErrorRate != null">
        #{requestFakeErrorRate,jdbcType=VARCHAR},
      </if>
      <if test="requestPassRate != null">
        #{requestPassRate,jdbcType=VARCHAR},
      </if>
      <if test="assertionPassRate != null">
        #{assertionPassRate,jdbcType=VARCHAR},
      </if>
      <if test="scriptIdentifier != null">
        #{scriptIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="execStatus != null">
        #{execStatus,jdbcType=VARCHAR},
      </if>
      <if test="plan != null">
        #{plan,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.api.domain.ApiReportExample" resultType="java.lang.Long">
    select count(*) from api_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update api_report
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.testPlanCaseId != null">
        test_plan_case_id = #{record.testPlanCaseId,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.deleteTime != null">
        delete_time = #{record.deleteTime,jdbcType=BIGINT},
      </if>
      <if test="record.deleteUser != null">
        delete_user = #{record.deleteUser,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=BIGINT},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=BIGINT},
      </if>
      <if test="record.requestDuration != null">
        request_duration = #{record.requestDuration,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.triggerMode != null">
        trigger_mode = #{record.triggerMode,jdbcType=VARCHAR},
      </if>
      <if test="record.runMode != null">
        run_mode = #{record.runMode,jdbcType=VARCHAR},
      </if>
      <if test="record.poolId != null">
        pool_id = #{record.poolId,jdbcType=VARCHAR},
      </if>
      <if test="record.integrated != null">
        integrated = #{record.integrated,jdbcType=BIT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=VARCHAR},
      </if>
      <if test="record.environmentId != null">
        environment_id = #{record.environmentId,jdbcType=VARCHAR},
      </if>
      <if test="record.errorCount != null">
        error_count = #{record.errorCount,jdbcType=BIGINT},
      </if>
      <if test="record.fakeErrorCount != null">
        fake_error_count = #{record.fakeErrorCount,jdbcType=BIGINT},
      </if>
      <if test="record.pendingCount != null">
        pending_count = #{record.pendingCount,jdbcType=BIGINT},
      </if>
      <if test="record.successCount != null">
        success_count = #{record.successCount,jdbcType=BIGINT},
      </if>
      <if test="record.assertionCount != null">
        assertion_count = #{record.assertionCount,jdbcType=BIGINT},
      </if>
      <if test="record.assertionSuccessCount != null">
        assertion_success_count = #{record.assertionSuccessCount,jdbcType=BIGINT},
      </if>
      <if test="record.requestErrorRate != null">
        request_error_rate = #{record.requestErrorRate,jdbcType=VARCHAR},
      </if>
      <if test="record.requestPendingRate != null">
        request_pending_rate = #{record.requestPendingRate,jdbcType=VARCHAR},
      </if>
      <if test="record.requestFakeErrorRate != null">
        request_fake_error_rate = #{record.requestFakeErrorRate,jdbcType=VARCHAR},
      </if>
      <if test="record.requestPassRate != null">
        request_pass_rate = #{record.requestPassRate,jdbcType=VARCHAR},
      </if>
      <if test="record.assertionPassRate != null">
        assertion_pass_rate = #{record.assertionPassRate,jdbcType=VARCHAR},
      </if>
      <if test="record.scriptIdentifier != null">
        script_identifier = #{record.scriptIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="record.execStatus != null">
        exec_status = #{record.execStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.plan != null">
        `plan` = #{record.plan,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update api_report
    set id = #{record.id,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      test_plan_case_id = #{record.testPlanCaseId,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      delete_time = #{record.deleteTime,jdbcType=BIGINT},
      delete_user = #{record.deleteUser,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=BIT},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      start_time = #{record.startTime,jdbcType=BIGINT},
      end_time = #{record.endTime,jdbcType=BIGINT},
      request_duration = #{record.requestDuration,jdbcType=BIGINT},
      `status` = #{record.status,jdbcType=VARCHAR},
      trigger_mode = #{record.triggerMode,jdbcType=VARCHAR},
      run_mode = #{record.runMode,jdbcType=VARCHAR},
      pool_id = #{record.poolId,jdbcType=VARCHAR},
      integrated = #{record.integrated,jdbcType=BIT},
      project_id = #{record.projectId,jdbcType=VARCHAR},
      environment_id = #{record.environmentId,jdbcType=VARCHAR},
      error_count = #{record.errorCount,jdbcType=BIGINT},
      fake_error_count = #{record.fakeErrorCount,jdbcType=BIGINT},
      pending_count = #{record.pendingCount,jdbcType=BIGINT},
      success_count = #{record.successCount,jdbcType=BIGINT},
      assertion_count = #{record.assertionCount,jdbcType=BIGINT},
      assertion_success_count = #{record.assertionSuccessCount,jdbcType=BIGINT},
      request_error_rate = #{record.requestErrorRate,jdbcType=VARCHAR},
      request_pending_rate = #{record.requestPendingRate,jdbcType=VARCHAR},
      request_fake_error_rate = #{record.requestFakeErrorRate,jdbcType=VARCHAR},
      request_pass_rate = #{record.requestPassRate,jdbcType=VARCHAR},
      assertion_pass_rate = #{record.assertionPassRate,jdbcType=VARCHAR},
      script_identifier = #{record.scriptIdentifier,jdbcType=VARCHAR},
      exec_status = #{record.execStatus,jdbcType=VARCHAR},
      `plan` = #{record.plan,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.api.domain.ApiReport">
    update api_report
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="testPlanCaseId != null">
        test_plan_case_id = #{testPlanCaseId,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="deleteTime != null">
        delete_time = #{deleteTime,jdbcType=BIGINT},
      </if>
      <if test="deleteUser != null">
        delete_user = #{deleteUser,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=BIGINT},
      </if>
      <if test="requestDuration != null">
        request_duration = #{requestDuration,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="triggerMode != null">
        trigger_mode = #{triggerMode,jdbcType=VARCHAR},
      </if>
      <if test="runMode != null">
        run_mode = #{runMode,jdbcType=VARCHAR},
      </if>
      <if test="poolId != null">
        pool_id = #{poolId,jdbcType=VARCHAR},
      </if>
      <if test="integrated != null">
        integrated = #{integrated,jdbcType=BIT},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="environmentId != null">
        environment_id = #{environmentId,jdbcType=VARCHAR},
      </if>
      <if test="errorCount != null">
        error_count = #{errorCount,jdbcType=BIGINT},
      </if>
      <if test="fakeErrorCount != null">
        fake_error_count = #{fakeErrorCount,jdbcType=BIGINT},
      </if>
      <if test="pendingCount != null">
        pending_count = #{pendingCount,jdbcType=BIGINT},
      </if>
      <if test="successCount != null">
        success_count = #{successCount,jdbcType=BIGINT},
      </if>
      <if test="assertionCount != null">
        assertion_count = #{assertionCount,jdbcType=BIGINT},
      </if>
      <if test="assertionSuccessCount != null">
        assertion_success_count = #{assertionSuccessCount,jdbcType=BIGINT},
      </if>
      <if test="requestErrorRate != null">
        request_error_rate = #{requestErrorRate,jdbcType=VARCHAR},
      </if>
      <if test="requestPendingRate != null">
        request_pending_rate = #{requestPendingRate,jdbcType=VARCHAR},
      </if>
      <if test="requestFakeErrorRate != null">
        request_fake_error_rate = #{requestFakeErrorRate,jdbcType=VARCHAR},
      </if>
      <if test="requestPassRate != null">
        request_pass_rate = #{requestPassRate,jdbcType=VARCHAR},
      </if>
      <if test="assertionPassRate != null">
        assertion_pass_rate = #{assertionPassRate,jdbcType=VARCHAR},
      </if>
      <if test="scriptIdentifier != null">
        script_identifier = #{scriptIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="execStatus != null">
        exec_status = #{execStatus,jdbcType=VARCHAR},
      </if>
      <if test="plan != null">
        `plan` = #{plan,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.api.domain.ApiReport">
    update api_report
    set `name` = #{name,jdbcType=VARCHAR},
      test_plan_case_id = #{testPlanCaseId,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      delete_time = #{deleteTime,jdbcType=BIGINT},
      delete_user = #{deleteUser,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIT},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=BIGINT},
      end_time = #{endTime,jdbcType=BIGINT},
      request_duration = #{requestDuration,jdbcType=BIGINT},
      `status` = #{status,jdbcType=VARCHAR},
      trigger_mode = #{triggerMode,jdbcType=VARCHAR},
      run_mode = #{runMode,jdbcType=VARCHAR},
      pool_id = #{poolId,jdbcType=VARCHAR},
      integrated = #{integrated,jdbcType=BIT},
      project_id = #{projectId,jdbcType=VARCHAR},
      environment_id = #{environmentId,jdbcType=VARCHAR},
      error_count = #{errorCount,jdbcType=BIGINT},
      fake_error_count = #{fakeErrorCount,jdbcType=BIGINT},
      pending_count = #{pendingCount,jdbcType=BIGINT},
      success_count = #{successCount,jdbcType=BIGINT},
      assertion_count = #{assertionCount,jdbcType=BIGINT},
      assertion_success_count = #{assertionSuccessCount,jdbcType=BIGINT},
      request_error_rate = #{requestErrorRate,jdbcType=VARCHAR},
      request_pending_rate = #{requestPendingRate,jdbcType=VARCHAR},
      request_fake_error_rate = #{requestFakeErrorRate,jdbcType=VARCHAR},
      request_pass_rate = #{requestPassRate,jdbcType=VARCHAR},
      assertion_pass_rate = #{assertionPassRate,jdbcType=VARCHAR},
      script_identifier = #{scriptIdentifier,jdbcType=VARCHAR},
      exec_status = #{execStatus,jdbcType=VARCHAR},
      `plan` = #{plan,jdbcType=BIT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into api_report
    (id, `name`, test_plan_case_id, create_user, delete_time, delete_user, deleted, update_user, 
      update_time, start_time, end_time, request_duration, `status`, trigger_mode, run_mode, 
      pool_id, integrated, project_id, environment_id, error_count, fake_error_count, 
      pending_count, success_count, assertion_count, assertion_success_count, request_error_rate, 
      request_pending_rate, request_fake_error_rate, request_pass_rate, assertion_pass_rate, 
      script_identifier, exec_status, `plan`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.testPlanCaseId,jdbcType=VARCHAR}, 
        #{item.createUser,jdbcType=VARCHAR}, #{item.deleteTime,jdbcType=BIGINT}, #{item.deleteUser,jdbcType=VARCHAR}, 
        #{item.deleted,jdbcType=BIT}, #{item.updateUser,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=BIGINT}, 
        #{item.startTime,jdbcType=BIGINT}, #{item.endTime,jdbcType=BIGINT}, #{item.requestDuration,jdbcType=BIGINT}, 
        #{item.status,jdbcType=VARCHAR}, #{item.triggerMode,jdbcType=VARCHAR}, #{item.runMode,jdbcType=VARCHAR}, 
        #{item.poolId,jdbcType=VARCHAR}, #{item.integrated,jdbcType=BIT}, #{item.projectId,jdbcType=VARCHAR}, 
        #{item.environmentId,jdbcType=VARCHAR}, #{item.errorCount,jdbcType=BIGINT}, #{item.fakeErrorCount,jdbcType=BIGINT}, 
        #{item.pendingCount,jdbcType=BIGINT}, #{item.successCount,jdbcType=BIGINT}, #{item.assertionCount,jdbcType=BIGINT}, 
        #{item.assertionSuccessCount,jdbcType=BIGINT}, #{item.requestErrorRate,jdbcType=VARCHAR}, 
        #{item.requestPendingRate,jdbcType=VARCHAR}, #{item.requestFakeErrorRate,jdbcType=VARCHAR}, 
        #{item.requestPassRate,jdbcType=VARCHAR}, #{item.assertionPassRate,jdbcType=VARCHAR}, 
        #{item.scriptIdentifier,jdbcType=VARCHAR}, #{item.execStatus,jdbcType=VARCHAR}, 
        #{item.plan,jdbcType=BIT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into api_report (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'test_plan_case_id'.toString() == column.value">
          #{item.testPlanCaseId,jdbcType=VARCHAR}
        </if>
        <if test="'create_user'.toString() == column.value">
          #{item.createUser,jdbcType=VARCHAR}
        </if>
        <if test="'delete_time'.toString() == column.value">
          #{item.deleteTime,jdbcType=BIGINT}
        </if>
        <if test="'delete_user'.toString() == column.value">
          #{item.deleteUser,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=BIT}
        </if>
        <if test="'update_user'.toString() == column.value">
          #{item.updateUser,jdbcType=VARCHAR}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=BIGINT}
        </if>
        <if test="'start_time'.toString() == column.value">
          #{item.startTime,jdbcType=BIGINT}
        </if>
        <if test="'end_time'.toString() == column.value">
          #{item.endTime,jdbcType=BIGINT}
        </if>
        <if test="'request_duration'.toString() == column.value">
          #{item.requestDuration,jdbcType=BIGINT}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=VARCHAR}
        </if>
        <if test="'trigger_mode'.toString() == column.value">
          #{item.triggerMode,jdbcType=VARCHAR}
        </if>
        <if test="'run_mode'.toString() == column.value">
          #{item.runMode,jdbcType=VARCHAR}
        </if>
        <if test="'pool_id'.toString() == column.value">
          #{item.poolId,jdbcType=VARCHAR}
        </if>
        <if test="'integrated'.toString() == column.value">
          #{item.integrated,jdbcType=BIT}
        </if>
        <if test="'project_id'.toString() == column.value">
          #{item.projectId,jdbcType=VARCHAR}
        </if>
        <if test="'environment_id'.toString() == column.value">
          #{item.environmentId,jdbcType=VARCHAR}
        </if>
        <if test="'error_count'.toString() == column.value">
          #{item.errorCount,jdbcType=BIGINT}
        </if>
        <if test="'fake_error_count'.toString() == column.value">
          #{item.fakeErrorCount,jdbcType=BIGINT}
        </if>
        <if test="'pending_count'.toString() == column.value">
          #{item.pendingCount,jdbcType=BIGINT}
        </if>
        <if test="'success_count'.toString() == column.value">
          #{item.successCount,jdbcType=BIGINT}
        </if>
        <if test="'assertion_count'.toString() == column.value">
          #{item.assertionCount,jdbcType=BIGINT}
        </if>
        <if test="'assertion_success_count'.toString() == column.value">
          #{item.assertionSuccessCount,jdbcType=BIGINT}
        </if>
        <if test="'request_error_rate'.toString() == column.value">
          #{item.requestErrorRate,jdbcType=VARCHAR}
        </if>
        <if test="'request_pending_rate'.toString() == column.value">
          #{item.requestPendingRate,jdbcType=VARCHAR}
        </if>
        <if test="'request_fake_error_rate'.toString() == column.value">
          #{item.requestFakeErrorRate,jdbcType=VARCHAR}
        </if>
        <if test="'request_pass_rate'.toString() == column.value">
          #{item.requestPassRate,jdbcType=VARCHAR}
        </if>
        <if test="'assertion_pass_rate'.toString() == column.value">
          #{item.assertionPassRate,jdbcType=VARCHAR}
        </if>
        <if test="'script_identifier'.toString() == column.value">
          #{item.scriptIdentifier,jdbcType=VARCHAR}
        </if>
        <if test="'exec_status'.toString() == column.value">
          #{item.execStatus,jdbcType=VARCHAR}
        </if>
        <if test="'plan'.toString() == column.value">
          #{item.plan,jdbcType=BIT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>