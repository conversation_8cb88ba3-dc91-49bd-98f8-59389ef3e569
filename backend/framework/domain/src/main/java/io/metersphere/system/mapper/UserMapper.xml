<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.system.mapper.UserMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.system.domain.User">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="language" jdbcType="VARCHAR" property="language" />
    <result column="last_organization_id" jdbcType="VARCHAR" property="lastOrganizationId" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="last_project_id" jdbcType="VARCHAR" property="lastProjectId" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="cft_token" jdbcType="VARCHAR" property="cftToken" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, `name`, email, `password`, `enable`, create_time, update_time, `language`, last_organization_id, 
    phone, `source`, last_project_id, create_user, update_user, deleted, cft_token
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.system.domain.UserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from user
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.system.domain.UserExample">
    delete from user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.system.domain.User">
    insert into user (id, `name`, email, 
      `password`, `enable`, create_time, 
      update_time, `language`, last_organization_id, 
      phone, `source`, last_project_id, 
      create_user, update_user, deleted, 
      cft_token)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, 
      #{password,jdbcType=VARCHAR}, #{enable,jdbcType=BIT}, #{createTime,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT}, #{language,jdbcType=VARCHAR}, #{lastOrganizationId,jdbcType=VARCHAR}, 
      #{phone,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, #{lastProjectId,jdbcType=VARCHAR}, 
      #{createUser,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR}, #{deleted,jdbcType=BIT}, 
      #{cftToken,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.system.domain.User">
    insert into user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="password != null">
        `password`,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="language != null">
        `language`,
      </if>
      <if test="lastOrganizationId != null">
        last_organization_id,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="lastProjectId != null">
        last_project_id,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="cftToken != null">
        cft_token,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="language != null">
        #{language,jdbcType=VARCHAR},
      </if>
      <if test="lastOrganizationId != null">
        #{lastOrganizationId,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="lastProjectId != null">
        #{lastProjectId,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="cftToken != null">
        #{cftToken,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.system.domain.UserExample" resultType="java.lang.Long">
    select count(*) from user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update user
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.password != null">
        `password` = #{record.password,jdbcType=VARCHAR},
      </if>
      <if test="record.enable != null">
        `enable` = #{record.enable,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.language != null">
        `language` = #{record.language,jdbcType=VARCHAR},
      </if>
      <if test="record.lastOrganizationId != null">
        last_organization_id = #{record.lastOrganizationId,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        `source` = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.lastProjectId != null">
        last_project_id = #{record.lastProjectId,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.cftToken != null">
        cft_token = #{record.cftToken,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update user
    set id = #{record.id,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      email = #{record.email,jdbcType=VARCHAR},
      `password` = #{record.password,jdbcType=VARCHAR},
      `enable` = #{record.enable,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      `language` = #{record.language,jdbcType=VARCHAR},
      last_organization_id = #{record.lastOrganizationId,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      `source` = #{record.source,jdbcType=VARCHAR},
      last_project_id = #{record.lastProjectId,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=BIT},
      cft_token = #{record.cftToken,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.system.domain.User">
    update user
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        `password` = #{password,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="language != null">
        `language` = #{language,jdbcType=VARCHAR},
      </if>
      <if test="lastOrganizationId != null">
        last_organization_id = #{lastOrganizationId,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="lastProjectId != null">
        last_project_id = #{lastProjectId,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="cftToken != null">
        cft_token = #{cftToken,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.system.domain.User">
    update user
    set `name` = #{name,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      `password` = #{password,jdbcType=VARCHAR},
      `enable` = #{enable,jdbcType=BIT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      `language` = #{language,jdbcType=VARCHAR},
      last_organization_id = #{lastOrganizationId,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      `source` = #{source,jdbcType=VARCHAR},
      last_project_id = #{lastProjectId,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIT},
      cft_token = #{cftToken,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into user
    (id, `name`, email, `password`, `enable`, create_time, update_time, `language`, last_organization_id, 
      phone, `source`, last_project_id, create_user, update_user, deleted, cft_token)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.email,jdbcType=VARCHAR}, 
        #{item.password,jdbcType=VARCHAR}, #{item.enable,jdbcType=BIT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.updateTime,jdbcType=BIGINT}, #{item.language,jdbcType=VARCHAR}, #{item.lastOrganizationId,jdbcType=VARCHAR}, 
        #{item.phone,jdbcType=VARCHAR}, #{item.source,jdbcType=VARCHAR}, #{item.lastProjectId,jdbcType=VARCHAR}, 
        #{item.createUser,jdbcType=VARCHAR}, #{item.updateUser,jdbcType=VARCHAR}, #{item.deleted,jdbcType=BIT}, 
        #{item.cftToken,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into user (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'email'.toString() == column.value">
          #{item.email,jdbcType=VARCHAR}
        </if>
        <if test="'password'.toString() == column.value">
          #{item.password,jdbcType=VARCHAR}
        </if>
        <if test="'enable'.toString() == column.value">
          #{item.enable,jdbcType=BIT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=BIGINT}
        </if>
        <if test="'language'.toString() == column.value">
          #{item.language,jdbcType=VARCHAR}
        </if>
        <if test="'last_organization_id'.toString() == column.value">
          #{item.lastOrganizationId,jdbcType=VARCHAR}
        </if>
        <if test="'phone'.toString() == column.value">
          #{item.phone,jdbcType=VARCHAR}
        </if>
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=VARCHAR}
        </if>
        <if test="'last_project_id'.toString() == column.value">
          #{item.lastProjectId,jdbcType=VARCHAR}
        </if>
        <if test="'create_user'.toString() == column.value">
          #{item.createUser,jdbcType=VARCHAR}
        </if>
        <if test="'update_user'.toString() == column.value">
          #{item.updateUser,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=BIT}
        </if>
        <if test="'cft_token'.toString() == column.value">
          #{item.cftToken,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>