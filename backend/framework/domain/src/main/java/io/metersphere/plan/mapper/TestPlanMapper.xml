<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.plan.mapper.TestPlanMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.plan.domain.TestPlan">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="num" jdbcType="BIGINT" property="num" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="module_id" jdbcType="VARCHAR" property="moduleId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="tags" jdbcType="VARCHAR" property="tags" typeHandler="io.metersphere.handler.ListTypeHandler" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="planned_start_time" jdbcType="BIGINT" property="plannedStartTime" />
    <result column="planned_end_time" jdbcType="BIGINT" property="plannedEndTime" />
    <result column="actual_start_time" jdbcType="BIGINT" property="actualStartTime" />
    <result column="actual_end_time" jdbcType="BIGINT" property="actualEndTime" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="pos" jdbcType="BIGINT" property="pos" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.tagsCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=io.metersphere.handler.ListTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=io.metersphere.handler.ListTypeHandler} and #{criterion.secondValue,typeHandler=io.metersphere.handler.ListTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=io.metersphere.handler.ListTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.tagsCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=io.metersphere.handler.ListTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=io.metersphere.handler.ListTypeHandler} and #{criterion.secondValue,typeHandler=io.metersphere.handler.ListTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=io.metersphere.handler.ListTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, num, project_id, group_id, module_id, `name`, `status`, `type`, tags, create_time, 
    create_user, update_time, update_user, planned_start_time, planned_end_time, actual_start_time, 
    actual_end_time, description, pos
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.plan.domain.TestPlanExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from test_plan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from test_plan
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from test_plan
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.plan.domain.TestPlanExample">
    delete from test_plan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.plan.domain.TestPlan">
    insert into test_plan (id, num, project_id, 
      group_id, module_id, `name`, 
      `status`, `type`, tags, 
      create_time, create_user, update_time, 
      update_user, planned_start_time, planned_end_time, 
      actual_start_time, actual_end_time, description, 
      pos)
    values (#{id,jdbcType=VARCHAR}, #{num,jdbcType=BIGINT}, #{projectId,jdbcType=VARCHAR}, 
      #{groupId,jdbcType=VARCHAR}, #{moduleId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler}, 
      #{createTime,jdbcType=BIGINT}, #{createUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=BIGINT}, 
      #{updateUser,jdbcType=VARCHAR}, #{plannedStartTime,jdbcType=BIGINT}, #{plannedEndTime,jdbcType=BIGINT}, 
      #{actualStartTime,jdbcType=BIGINT}, #{actualEndTime,jdbcType=BIGINT}, #{description,jdbcType=VARCHAR}, 
      #{pos,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.plan.domain.TestPlan">
    insert into test_plan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="moduleId != null">
        module_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="tags != null">
        tags,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="plannedStartTime != null">
        planned_start_time,
      </if>
      <if test="plannedEndTime != null">
        planned_end_time,
      </if>
      <if test="actualStartTime != null">
        actual_start_time,
      </if>
      <if test="actualEndTime != null">
        actual_end_time,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="pos != null">
        pos,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="moduleId != null">
        #{moduleId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="tags != null">
        #{tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="plannedStartTime != null">
        #{plannedStartTime,jdbcType=BIGINT},
      </if>
      <if test="plannedEndTime != null">
        #{plannedEndTime,jdbcType=BIGINT},
      </if>
      <if test="actualStartTime != null">
        #{actualStartTime,jdbcType=BIGINT},
      </if>
      <if test="actualEndTime != null">
        #{actualEndTime,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="pos != null">
        #{pos,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.plan.domain.TestPlanExample" resultType="java.lang.Long">
    select count(*) from test_plan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update test_plan
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.num != null">
        num = #{record.num,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=VARCHAR},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=VARCHAR},
      </if>
      <if test="record.moduleId != null">
        module_id = #{record.moduleId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.tags != null">
        tags = #{record.tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.plannedStartTime != null">
        planned_start_time = #{record.plannedStartTime,jdbcType=BIGINT},
      </if>
      <if test="record.plannedEndTime != null">
        planned_end_time = #{record.plannedEndTime,jdbcType=BIGINT},
      </if>
      <if test="record.actualStartTime != null">
        actual_start_time = #{record.actualStartTime,jdbcType=BIGINT},
      </if>
      <if test="record.actualEndTime != null">
        actual_end_time = #{record.actualEndTime,jdbcType=BIGINT},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.pos != null">
        pos = #{record.pos,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update test_plan
    set id = #{record.id,jdbcType=VARCHAR},
      num = #{record.num,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=VARCHAR},
      group_id = #{record.groupId,jdbcType=VARCHAR},
      module_id = #{record.moduleId,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=VARCHAR},
      `type` = #{record.type,jdbcType=VARCHAR},
      tags = #{record.tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler},
      create_time = #{record.createTime,jdbcType=BIGINT},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      planned_start_time = #{record.plannedStartTime,jdbcType=BIGINT},
      planned_end_time = #{record.plannedEndTime,jdbcType=BIGINT},
      actual_start_time = #{record.actualStartTime,jdbcType=BIGINT},
      actual_end_time = #{record.actualEndTime,jdbcType=BIGINT},
      description = #{record.description,jdbcType=VARCHAR},
      pos = #{record.pos,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.plan.domain.TestPlan">
    update test_plan
    <set>
      <if test="num != null">
        num = #{num,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="moduleId != null">
        module_id = #{moduleId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="tags != null">
        tags = #{tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="plannedStartTime != null">
        planned_start_time = #{plannedStartTime,jdbcType=BIGINT},
      </if>
      <if test="plannedEndTime != null">
        planned_end_time = #{plannedEndTime,jdbcType=BIGINT},
      </if>
      <if test="actualStartTime != null">
        actual_start_time = #{actualStartTime,jdbcType=BIGINT},
      </if>
      <if test="actualEndTime != null">
        actual_end_time = #{actualEndTime,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="pos != null">
        pos = #{pos,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.plan.domain.TestPlan">
    update test_plan
    set num = #{num,jdbcType=BIGINT},
      project_id = #{projectId,jdbcType=VARCHAR},
      group_id = #{groupId,jdbcType=VARCHAR},
      module_id = #{moduleId,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      tags = #{tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler},
      create_time = #{createTime,jdbcType=BIGINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=BIGINT},
      update_user = #{updateUser,jdbcType=VARCHAR},
      planned_start_time = #{plannedStartTime,jdbcType=BIGINT},
      planned_end_time = #{plannedEndTime,jdbcType=BIGINT},
      actual_start_time = #{actualStartTime,jdbcType=BIGINT},
      actual_end_time = #{actualEndTime,jdbcType=BIGINT},
      description = #{description,jdbcType=VARCHAR},
      pos = #{pos,jdbcType=BIGINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into test_plan
    (id, num, project_id, group_id, module_id, `name`, `status`, `type`, tags, create_time, 
      create_user, update_time, update_user, planned_start_time, planned_end_time, actual_start_time, 
      actual_end_time, description, pos)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.num,jdbcType=BIGINT}, #{item.projectId,jdbcType=VARCHAR}, 
        #{item.groupId,jdbcType=VARCHAR}, #{item.moduleId,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR}, #{item.tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.createUser,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=BIGINT}, 
        #{item.updateUser,jdbcType=VARCHAR}, #{item.plannedStartTime,jdbcType=BIGINT}, 
        #{item.plannedEndTime,jdbcType=BIGINT}, #{item.actualStartTime,jdbcType=BIGINT}, 
        #{item.actualEndTime,jdbcType=BIGINT}, #{item.description,jdbcType=VARCHAR}, #{item.pos,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into test_plan (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'num'.toString() == column.value">
          #{item.num,jdbcType=BIGINT}
        </if>
        <if test="'project_id'.toString() == column.value">
          #{item.projectId,jdbcType=VARCHAR}
        </if>
        <if test="'group_id'.toString() == column.value">
          #{item.groupId,jdbcType=VARCHAR}
        </if>
        <if test="'module_id'.toString() == column.value">
          #{item.moduleId,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=VARCHAR}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=VARCHAR}
        </if>
        <if test="'tags'.toString() == column.value">
          #{item.tags,jdbcType=VARCHAR,typeHandler=io.metersphere.handler.ListTypeHandler}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'create_user'.toString() == column.value">
          #{item.createUser,jdbcType=VARCHAR}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=BIGINT}
        </if>
        <if test="'update_user'.toString() == column.value">
          #{item.updateUser,jdbcType=VARCHAR}
        </if>
        <if test="'planned_start_time'.toString() == column.value">
          #{item.plannedStartTime,jdbcType=BIGINT}
        </if>
        <if test="'planned_end_time'.toString() == column.value">
          #{item.plannedEndTime,jdbcType=BIGINT}
        </if>
        <if test="'actual_start_time'.toString() == column.value">
          #{item.actualStartTime,jdbcType=BIGINT}
        </if>
        <if test="'actual_end_time'.toString() == column.value">
          #{item.actualEndTime,jdbcType=BIGINT}
        </if>
        <if test="'description'.toString() == column.value">
          #{item.description,jdbcType=VARCHAR}
        </if>
        <if test="'pos'.toString() == column.value">
          #{item.pos,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>