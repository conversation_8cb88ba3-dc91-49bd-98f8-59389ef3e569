<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.project.mapper.FileAssociationMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.project.domain.FileAssociation">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="source_type" jdbcType="VARCHAR" property="sourceType" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="file_id" jdbcType="VARCHAR" property="fileId" />
    <result column="file_ref_id" jdbcType="VARCHAR" property="fileRefId" />
    <result column="file_version" jdbcType="VARCHAR" property="fileVersion" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="deleted_file_name" jdbcType="VARCHAR" property="deletedFileName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, source_type, source_id, file_id, file_ref_id, file_version, create_time, update_user, 
    update_time, create_user, deleted, deleted_file_name
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.project.domain.FileAssociationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from file_association
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from file_association
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from file_association
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.project.domain.FileAssociationExample">
    delete from file_association
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.project.domain.FileAssociation">
    insert into file_association (id, source_type, source_id, 
      file_id, file_ref_id, file_version, 
      create_time, update_user, update_time, 
      create_user, deleted, deleted_file_name
      )
    values (#{id,jdbcType=VARCHAR}, #{sourceType,jdbcType=VARCHAR}, #{sourceId,jdbcType=VARCHAR}, 
      #{fileId,jdbcType=VARCHAR}, #{fileRefId,jdbcType=VARCHAR}, #{fileVersion,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=BIGINT}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=BIGINT}, 
      #{createUser,jdbcType=VARCHAR}, #{deleted,jdbcType=BIT}, #{deletedFileName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.project.domain.FileAssociation">
    insert into file_association
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sourceType != null">
        source_type,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="fileId != null">
        file_id,
      </if>
      <if test="fileRefId != null">
        file_ref_id,
      </if>
      <if test="fileVersion != null">
        file_version,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="deletedFileName != null">
        deleted_file_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null">
        #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="fileRefId != null">
        #{fileRefId,jdbcType=VARCHAR},
      </if>
      <if test="fileVersion != null">
        #{fileVersion,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="deletedFileName != null">
        #{deletedFileName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.project.domain.FileAssociationExample" resultType="java.lang.Long">
    select count(*) from file_association
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update file_association
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceType != null">
        source_type = #{record.sourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceId != null">
        source_id = #{record.sourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.fileId != null">
        file_id = #{record.fileId,jdbcType=VARCHAR},
      </if>
      <if test="record.fileRefId != null">
        file_ref_id = #{record.fileRefId,jdbcType=VARCHAR},
      </if>
      <if test="record.fileVersion != null">
        file_version = #{record.fileVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.deletedFileName != null">
        deleted_file_name = #{record.deletedFileName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update file_association
    set id = #{record.id,jdbcType=VARCHAR},
      source_type = #{record.sourceType,jdbcType=VARCHAR},
      source_id = #{record.sourceId,jdbcType=VARCHAR},
      file_id = #{record.fileId,jdbcType=VARCHAR},
      file_ref_id = #{record.fileRefId,jdbcType=VARCHAR},
      file_version = #{record.fileVersion,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=BIT},
      deleted_file_name = #{record.deletedFileName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.project.domain.FileAssociation">
    update file_association
    <set>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null">
        file_id = #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="fileRefId != null">
        file_ref_id = #{fileRefId,jdbcType=VARCHAR},
      </if>
      <if test="fileVersion != null">
        file_version = #{fileVersion,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="deletedFileName != null">
        deleted_file_name = #{deletedFileName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.project.domain.FileAssociation">
    update file_association
    set source_type = #{sourceType,jdbcType=VARCHAR},
      source_id = #{sourceId,jdbcType=VARCHAR},
      file_id = #{fileId,jdbcType=VARCHAR},
      file_ref_id = #{fileRefId,jdbcType=VARCHAR},
      file_version = #{fileVersion,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=BIGINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIT},
      deleted_file_name = #{deletedFileName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into file_association
    (id, source_type, source_id, file_id, file_ref_id, file_version, create_time, update_user, 
      update_time, create_user, deleted, deleted_file_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.sourceType,jdbcType=VARCHAR}, #{item.sourceId,jdbcType=VARCHAR}, 
        #{item.fileId,jdbcType=VARCHAR}, #{item.fileRefId,jdbcType=VARCHAR}, #{item.fileVersion,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.updateUser,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=BIGINT}, 
        #{item.createUser,jdbcType=VARCHAR}, #{item.deleted,jdbcType=BIT}, #{item.deletedFileName,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into file_association (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'source_type'.toString() == column.value">
          #{item.sourceType,jdbcType=VARCHAR}
        </if>
        <if test="'source_id'.toString() == column.value">
          #{item.sourceId,jdbcType=VARCHAR}
        </if>
        <if test="'file_id'.toString() == column.value">
          #{item.fileId,jdbcType=VARCHAR}
        </if>
        <if test="'file_ref_id'.toString() == column.value">
          #{item.fileRefId,jdbcType=VARCHAR}
        </if>
        <if test="'file_version'.toString() == column.value">
          #{item.fileVersion,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'update_user'.toString() == column.value">
          #{item.updateUser,jdbcType=VARCHAR}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=BIGINT}
        </if>
        <if test="'create_user'.toString() == column.value">
          #{item.createUser,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=BIT}
        </if>
        <if test="'deleted_file_name'.toString() == column.value">
          #{item.deletedFileName,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>