<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.api.mapper.ApiDefinitionSwaggerMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.api.domain.ApiDefinitionSwagger">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="num" jdbcType="BIGINT" property="num" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="swagger_url" jdbcType="VARCHAR" property="swaggerUrl" />
    <result column="module_id" jdbcType="VARCHAR" property="moduleId" />
    <result column="config" jdbcType="VARCHAR" property="config" />
    <result column="cover_data" jdbcType="BIT" property="coverData" />
    <result column="cover_module" jdbcType="BIT" property="coverModule" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="version_id" jdbcType="VARCHAR" property="versionId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, num, `name`, swagger_url, module_id, config, cover_data, cover_module, project_id, 
    version_id
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.api.domain.ApiDefinitionSwaggerExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from api_definition_swagger
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from api_definition_swagger
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from api_definition_swagger
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.api.domain.ApiDefinitionSwaggerExample">
    delete from api_definition_swagger
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.api.domain.ApiDefinitionSwagger">
    insert into api_definition_swagger (id, num, `name`, 
      swagger_url, module_id, config, 
      cover_data, cover_module, project_id, 
      version_id)
    values (#{id,jdbcType=VARCHAR}, #{num,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{swaggerUrl,jdbcType=VARCHAR}, #{moduleId,jdbcType=VARCHAR}, #{config,jdbcType=VARCHAR}, 
      #{coverData,jdbcType=BIT}, #{coverModule,jdbcType=BIT}, #{projectId,jdbcType=VARCHAR}, 
      #{versionId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.api.domain.ApiDefinitionSwagger">
    insert into api_definition_swagger
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="swaggerUrl != null">
        swagger_url,
      </if>
      <if test="moduleId != null">
        module_id,
      </if>
      <if test="config != null">
        config,
      </if>
      <if test="coverData != null">
        cover_data,
      </if>
      <if test="coverModule != null">
        cover_module,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="versionId != null">
        version_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="swaggerUrl != null">
        #{swaggerUrl,jdbcType=VARCHAR},
      </if>
      <if test="moduleId != null">
        #{moduleId,jdbcType=VARCHAR},
      </if>
      <if test="config != null">
        #{config,jdbcType=VARCHAR},
      </if>
      <if test="coverData != null">
        #{coverData,jdbcType=BIT},
      </if>
      <if test="coverModule != null">
        #{coverModule,jdbcType=BIT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="versionId != null">
        #{versionId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.api.domain.ApiDefinitionSwaggerExample" resultType="java.lang.Long">
    select count(*) from api_definition_swagger
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update api_definition_swagger
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.num != null">
        num = #{record.num,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.swaggerUrl != null">
        swagger_url = #{record.swaggerUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.moduleId != null">
        module_id = #{record.moduleId,jdbcType=VARCHAR},
      </if>
      <if test="record.config != null">
        config = #{record.config,jdbcType=VARCHAR},
      </if>
      <if test="record.coverData != null">
        cover_data = #{record.coverData,jdbcType=BIT},
      </if>
      <if test="record.coverModule != null">
        cover_module = #{record.coverModule,jdbcType=BIT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=VARCHAR},
      </if>
      <if test="record.versionId != null">
        version_id = #{record.versionId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update api_definition_swagger
    set id = #{record.id,jdbcType=VARCHAR},
      num = #{record.num,jdbcType=BIGINT},
      `name` = #{record.name,jdbcType=VARCHAR},
      swagger_url = #{record.swaggerUrl,jdbcType=VARCHAR},
      module_id = #{record.moduleId,jdbcType=VARCHAR},
      config = #{record.config,jdbcType=VARCHAR},
      cover_data = #{record.coverData,jdbcType=BIT},
      cover_module = #{record.coverModule,jdbcType=BIT},
      project_id = #{record.projectId,jdbcType=VARCHAR},
      version_id = #{record.versionId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.api.domain.ApiDefinitionSwagger">
    update api_definition_swagger
    <set>
      <if test="num != null">
        num = #{num,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="swaggerUrl != null">
        swagger_url = #{swaggerUrl,jdbcType=VARCHAR},
      </if>
      <if test="moduleId != null">
        module_id = #{moduleId,jdbcType=VARCHAR},
      </if>
      <if test="config != null">
        config = #{config,jdbcType=VARCHAR},
      </if>
      <if test="coverData != null">
        cover_data = #{coverData,jdbcType=BIT},
      </if>
      <if test="coverModule != null">
        cover_module = #{coverModule,jdbcType=BIT},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="versionId != null">
        version_id = #{versionId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.api.domain.ApiDefinitionSwagger">
    update api_definition_swagger
    set num = #{num,jdbcType=BIGINT},
      `name` = #{name,jdbcType=VARCHAR},
      swagger_url = #{swaggerUrl,jdbcType=VARCHAR},
      module_id = #{moduleId,jdbcType=VARCHAR},
      config = #{config,jdbcType=VARCHAR},
      cover_data = #{coverData,jdbcType=BIT},
      cover_module = #{coverModule,jdbcType=BIT},
      project_id = #{projectId,jdbcType=VARCHAR},
      version_id = #{versionId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into api_definition_swagger
    (id, num, `name`, swagger_url, module_id, config, cover_data, cover_module, project_id, 
      version_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.num,jdbcType=BIGINT}, #{item.name,jdbcType=VARCHAR}, 
        #{item.swaggerUrl,jdbcType=VARCHAR}, #{item.moduleId,jdbcType=VARCHAR}, #{item.config,jdbcType=VARCHAR}, 
        #{item.coverData,jdbcType=BIT}, #{item.coverModule,jdbcType=BIT}, #{item.projectId,jdbcType=VARCHAR}, 
        #{item.versionId,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into api_definition_swagger (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'num'.toString() == column.value">
          #{item.num,jdbcType=BIGINT}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'swagger_url'.toString() == column.value">
          #{item.swaggerUrl,jdbcType=VARCHAR}
        </if>
        <if test="'module_id'.toString() == column.value">
          #{item.moduleId,jdbcType=VARCHAR}
        </if>
        <if test="'config'.toString() == column.value">
          #{item.config,jdbcType=VARCHAR}
        </if>
        <if test="'cover_data'.toString() == column.value">
          #{item.coverData,jdbcType=BIT}
        </if>
        <if test="'cover_module'.toString() == column.value">
          #{item.coverModule,jdbcType=BIT}
        </if>
        <if test="'project_id'.toString() == column.value">
          #{item.projectId,jdbcType=VARCHAR}
        </if>
        <if test="'version_id'.toString() == column.value">
          #{item.versionId,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>