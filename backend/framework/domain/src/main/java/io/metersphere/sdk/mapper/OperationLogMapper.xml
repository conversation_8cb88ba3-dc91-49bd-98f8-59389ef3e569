<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.sdk.mapper.OperationLogMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.sdk.domain.OperationLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="organization_id" jdbcType="VARCHAR" property="organizationId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="method" jdbcType="VARCHAR" property="method" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="module" jdbcType="VARCHAR" property="module" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="path" jdbcType="VARCHAR" property="path" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, organization_id, create_time, create_user, source_id, `method`, `type`, 
    `module`, content, `path`
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.sdk.domain.OperationLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from operation_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from operation_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.sdk.domain.OperationLogExample">
    delete from operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.sdk.domain.OperationLog" useGeneratedKeys="true" keyProperty="id">
    insert into operation_log (project_id, organization_id,
      create_time, create_user, source_id, 
      `method`, `type`, `module`, 
      content, `path`)
    values (#{projectId,jdbcType=VARCHAR}, #{organizationId,jdbcType=VARCHAR},
      #{createTime,jdbcType=BIGINT}, #{createUser,jdbcType=VARCHAR}, #{sourceId,jdbcType=VARCHAR}, 
      #{method,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{module,jdbcType=VARCHAR}, 
      #{content,jdbcType=VARCHAR}, #{path,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.sdk.domain.OperationLog" useGeneratedKeys="true" keyProperty="id">
    insert into operation_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="organizationId != null">
        organization_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="method != null">
        `method`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="module != null">
        `module`,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="path != null">
        `path`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="organizationId != null">
        #{organizationId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="method != null">
        #{method,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="module != null">
        #{module,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.sdk.domain.OperationLogExample" resultType="java.lang.Long">
    select count(*) from operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update operation_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=VARCHAR},
      </if>
      <if test="record.organizationId != null">
        organization_id = #{record.organizationId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceId != null">
        source_id = #{record.sourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.method != null">
        `method` = #{record.method,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.module != null">
        `module` = #{record.module,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.path != null">
        `path` = #{record.path,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update operation_log
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=VARCHAR},
      organization_id = #{record.organizationId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      source_id = #{record.sourceId,jdbcType=VARCHAR},
      `method` = #{record.method,jdbcType=VARCHAR},
      `type` = #{record.type,jdbcType=VARCHAR},
      `module` = #{record.module,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      `path` = #{record.path,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.sdk.domain.OperationLog">
    update operation_log
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="organizationId != null">
        organization_id = #{organizationId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="method != null">
        `method` = #{method,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="module != null">
        `module` = #{module,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="path != null">
        `path` = #{path,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.sdk.domain.OperationLog">
    update operation_log
    set project_id = #{projectId,jdbcType=VARCHAR},
      organization_id = #{organizationId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      source_id = #{sourceId,jdbcType=VARCHAR},
      `method` = #{method,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      `module` = #{module,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      `path` = #{path,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map" useGeneratedKeys="true" keyProperty="id">
    insert into operation_log
    (project_id, organization_id, create_time, create_user, source_id, `method`,
      `type`, `module`, content, `path`)
    values
    <foreach collection="list" item="item" separator=",">
      ( #{item.projectId,jdbcType=VARCHAR}, #{item.organizationId,jdbcType=VARCHAR},
        #{item.createTime,jdbcType=BIGINT}, #{item.createUser,jdbcType=VARCHAR}, #{item.sourceId,jdbcType=VARCHAR}, 
        #{item.method,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR}, #{item.module,jdbcType=VARCHAR}, 
        #{item.content,jdbcType=VARCHAR}, #{item.path,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map" useGeneratedKeys="true" keyProperty="id">
    insert into operation_log (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'project_id'.toString() == column.value">
          #{item.projectId,jdbcType=VARCHAR}
        </if>
        <if test="'organization_id'.toString() == column.value">
          #{item.organizationId,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'create_user'.toString() == column.value">
          #{item.createUser,jdbcType=VARCHAR}
        </if>
        <if test="'source_id'.toString() == column.value">
          #{item.sourceId,jdbcType=VARCHAR}
        </if>
        <if test="'method'.toString() == column.value">
          #{item.method,jdbcType=VARCHAR}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=VARCHAR}
        </if>
        <if test="'module'.toString() == column.value">
          #{item.module,jdbcType=VARCHAR}
        </if>
        <if test="'content'.toString() == column.value">
          #{item.content,jdbcType=VARCHAR}
        </if>
        <if test="'path'.toString() == column.value">
          #{item.path,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>