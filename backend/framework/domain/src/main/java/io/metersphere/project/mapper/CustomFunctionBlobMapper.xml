<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.project.mapper.CustomFunctionBlobMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.project.domain.CustomFunctionBlob">
    <id column="id" jdbcType="VARCHAR" property="id" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="io.metersphere.project.domain.CustomFunctionBlob">
    <result column="params" jdbcType="LONGVARBINARY" property="params" />
    <result column="script" jdbcType="LONGVARBINARY" property="script" />
    <result column="result" jdbcType="LONGVARBINARY" property="result" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id
  </sql>
  <sql id="Blob_Column_List">
    params, script, `result`
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="io.metersphere.project.domain.CustomFunctionBlobExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from custom_function_blob
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="io.metersphere.project.domain.CustomFunctionBlobExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from custom_function_blob
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from custom_function_blob
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from custom_function_blob
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.project.domain.CustomFunctionBlobExample">
    delete from custom_function_blob
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.project.domain.CustomFunctionBlob">
    insert into custom_function_blob (id, params, script, 
      `result`)
    values (#{id,jdbcType=VARCHAR}, #{params,jdbcType=LONGVARBINARY}, #{script,jdbcType=LONGVARBINARY}, 
      #{result,jdbcType=LONGVARBINARY})
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.project.domain.CustomFunctionBlob">
    insert into custom_function_blob
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="params != null">
        params,
      </if>
      <if test="script != null">
        script,
      </if>
      <if test="result != null">
        `result`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="params != null">
        #{params,jdbcType=LONGVARBINARY},
      </if>
      <if test="script != null">
        #{script,jdbcType=LONGVARBINARY},
      </if>
      <if test="result != null">
        #{result,jdbcType=LONGVARBINARY},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.project.domain.CustomFunctionBlobExample" resultType="java.lang.Long">
    select count(*) from custom_function_blob
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update custom_function_blob
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.params != null">
        params = #{record.params,jdbcType=LONGVARBINARY},
      </if>
      <if test="record.script != null">
        script = #{record.script,jdbcType=LONGVARBINARY},
      </if>
      <if test="record.result != null">
        `result` = #{record.result,jdbcType=LONGVARBINARY},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update custom_function_blob
    set id = #{record.id,jdbcType=VARCHAR},
      params = #{record.params,jdbcType=LONGVARBINARY},
      script = #{record.script,jdbcType=LONGVARBINARY},
      `result` = #{record.result,jdbcType=LONGVARBINARY}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update custom_function_blob
    set id = #{record.id,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.project.domain.CustomFunctionBlob">
    update custom_function_blob
    <set>
      <if test="params != null">
        params = #{params,jdbcType=LONGVARBINARY},
      </if>
      <if test="script != null">
        script = #{script,jdbcType=LONGVARBINARY},
      </if>
      <if test="result != null">
        `result` = #{result,jdbcType=LONGVARBINARY},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="io.metersphere.project.domain.CustomFunctionBlob">
    update custom_function_blob
    set params = #{params,jdbcType=LONGVARBINARY},
      script = #{script,jdbcType=LONGVARBINARY},
      `result` = #{result,jdbcType=LONGVARBINARY}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into custom_function_blob
    (id, params, script, `result`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.params,jdbcType=LONGVARBINARY}, #{item.script,jdbcType=LONGVARBINARY}, 
        #{item.result,jdbcType=LONGVARBINARY})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into custom_function_blob (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'params'.toString() == column.value">
          #{item.params,jdbcType=LONGVARBINARY}
        </if>
        <if test="'script'.toString() == column.value">
          #{item.script,jdbcType=LONGVARBINARY}
        </if>
        <if test="'result'.toString() == column.value">
          #{item.result,jdbcType=LONGVARBINARY}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>