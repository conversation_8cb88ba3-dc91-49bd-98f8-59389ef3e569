<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.system.mapper.UserRoleMapper">
  <resultMap id="BaseResultMap" type="io.metersphere.system.domain.UserRole">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="internal" jdbcType="BIT" property="internal" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="scope_id" jdbcType="VARCHAR" property="scopeId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, `name`, description, internal, `type`, create_time, update_time, create_user, 
    scope_id
  </sql>
  <select id="selectByExample" parameterType="io.metersphere.system.domain.UserRoleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_role
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from user_role
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="io.metersphere.system.domain.UserRoleExample">
    delete from user_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="io.metersphere.system.domain.UserRole">
    insert into user_role (id, `name`, description, 
      internal, `type`, create_time, 
      update_time, create_user, scope_id
      )
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{internal,jdbcType=BIT}, #{type,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT}, #{createUser,jdbcType=VARCHAR}, #{scopeId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="io.metersphere.system.domain.UserRole">
    insert into user_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="internal != null">
        internal,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="scopeId != null">
        scope_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="internal != null">
        #{internal,jdbcType=BIT},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="scopeId != null">
        #{scopeId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="io.metersphere.system.domain.UserRoleExample" resultType="java.lang.Long">
    select count(*) from user_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update user_role
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.internal != null">
        internal = #{record.internal,jdbcType=BIT},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.scopeId != null">
        scope_id = #{record.scopeId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update user_role
    set id = #{record.id,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      internal = #{record.internal,jdbcType=BIT},
      `type` = #{record.type,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      scope_id = #{record.scopeId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="io.metersphere.system.domain.UserRole">
    update user_role
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="internal != null">
        internal = #{internal,jdbcType=BIT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="scopeId != null">
        scope_id = #{scopeId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="io.metersphere.system.domain.UserRole">
    update user_role
    set `name` = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      internal = #{internal,jdbcType=BIT},
      `type` = #{type,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      scope_id = #{scopeId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into user_role
    (id, `name`, description, internal, `type`, create_time, update_time, create_user, 
      scope_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, 
        #{item.internal,jdbcType=BIT}, #{item.type,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.updateTime,jdbcType=BIGINT}, #{item.createUser,jdbcType=VARCHAR}, #{item.scopeId,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into user_role (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'description'.toString() == column.value">
          #{item.description,jdbcType=VARCHAR}
        </if>
        <if test="'internal'.toString() == column.value">
          #{item.internal,jdbcType=BIT}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=BIGINT}
        </if>
        <if test="'create_user'.toString() == column.value">
          #{item.createUser,jdbcType=VARCHAR}
        </if>
        <if test="'scope_id'.toString() == column.value">
          #{item.scopeId,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>