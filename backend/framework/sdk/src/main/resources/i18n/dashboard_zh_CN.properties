# dashboard
functional_case.reviewRate=评审率
functional_case.hasReview=已评审
functional_case.unReview=未评审
functional_case.passRate=通过率
functional_case.hasPass=已通过
functional_case.unPass=未通过
functional_case.associateRate=关联率
functional_case.hasAssociate=已关联
functional_case.unAssociate=未关联
api_definition.completionRate=完成率
api_management.execTime=执行次数
api_management.execCount=已执行
api_management.unExecCount=未执行
api_management.apiCaseCount=接口用例数
api_management.apiScenarioCount=场景用例数
api_management.fakeErrorCount=误报数
api_management.apiCaseExecRate=用例执行率
api_management.apiCasePassRate=用例通过率
api_management.scenarioExecRate=场景执行率
api_management.scenarioPassRate=场景通过率
api_management.passCount=已通过
api_management.unPassCount=未通过


bug_management.retentionRate=遗留率
bug_management.totalCount=缺陷总数
bug_management.retentionCount=遗留缺陷数
get_platform_end_status_error=获取平台结束状态失败

plan_executor=未分配