#commons
error_lang_invalid=语言参数错误
file_cannot_be_null=文件不能为空！
length.too.large=长度过长
cannot_be_null=不能为空
number=第
row=行
error=出错
delete_fail=删除失败
start_engine_fail=启动失败
upload_fail=文件上传失败
invalid_parameter=非法的参数
name_already_exists=该名称已经存在
resource_not_exist=资源不存在或已删除
upload_file_fail_get_file_path_fail=文件上传失败，获取文件上传路径为空
#user related
user_email_already_exists=用户邮箱已存在
user_id_is_null=用户ID不能为空
user_name_is_null=用户名不能为空
user_name_length_too_long=用户名称长度不能超过256个字符
password_length_too_long=密码长度不能超过256个字符
user_email_is_null=用户邮箱不能为空
password_is_null=密码不能为空
user_id_already_exists=用户id已存在
password_modification_failed=旧密码输入错误，请重新输入
cannot_delete_current_user=无法删除当前登录用户
connection_failed=连接失败
upload_config_save_param_error=文件限制参数错误
connection_timeout=连接超时
user_already_exists=该用户已存在于当前成员列表中
cannot_remove_current=无法移除当前登录用户
login_fail=登录失败
password_is_incorrect=邮箱或密码不正确
user_not_exist=用户不存在
user_has_been_disabled=用户已被禁用
excessive_attempts=操作频繁
user_locked=用户被锁定
user_expires=用户过期
not_authorized=未经授权
user_apikey_limit=最多能有5个Api key
please_logout_current_user=请先登出当前用户
resource.name=资源
keep_at_least_one_administrator=至少保留一个管理员
project.member_count.not_blank=成员数量不能为空

#load test
edit_load_test_not_found=无法编辑测试，未找到测试：
run_load_test_not_found=无法运行测试，未找到测试：
run_load_test_file_not_found=无法运行测试，无法获取测试文件元信息，测试ID：
run_load_test_file_content_not_found=无法运行测试，无法获取测试文件内容，测试ID：
run_load_test_file_init_error=无法运行测试，请前往 [系统设置-系统-系统参数设置] 检查当前站点配置，详情见 https://metersphere.io/docs/v2.x/faq/load_test/#url
load_test_is_running=测试正在运行, 请等待
load_test_kafka_invalid=Kafka 不可用，请检查配置
cannot_edit_load_test_running=不能修改正在运行的测试
test_not_found=测试不存在:
test_not_running=测试未运行
load_test_already_exists=测试名称不能重复
load_test_name_length=测试名称长度超过限制
no_nodes_message=没有节点信息
duplicate_node_ip=节点 IP 重复
duplicate_node_port=节点 Port 重复
duplicate_node_ip_port=节点 IP、Port 重复
max_thread_insufficient=并发用户数超额
related_case_del_fail_prefix=已关联到
related_case_del_fail_suffix=测试用例，请先解除关联
jmx_content_valid=JMX 内容无效，请检查
container_delete_fail=容器由于网络原因停止失败，请重试
load_test_report_file_not_exist=当前报告没有JTL文件，请等待或重新执行以便获取
startTime_must_be_less_than_endTime=开始日期必须小于结束日期
start_time_is_null=开始日期不能为空
end_time_is_null=结束日期不能为空

all_organization=全部组织
organization_not_exists=组织不存在
#test resource pool
test_resource_pool_id_is_null=资源池ID不能为空
test_resource_pool_name_is_null=资源池名称不能为空
test_resource_pool_name_already_exists=资源池名称已存在
resource_pool_application_organization_is_empty= 资源池应用组织为空
test_resource_pool_type_is_null=资源池类型不能为空
test_resource_pool_used_content_is_null = 资源池用途相关配置为空
load_test=性能测试
test_resource_pool_is_use=正在使用此资源池，无法删除
test_resource_pool_is_valid_fail = 校验不通过，请管理员检查资源池是否配置正常
only_one_k8s=只能添加一个 K8S
test_resource_pool_not_exists=测试资源池不存在
default_test_resource_pool_is_empty=默认资源池无法删除
test_resource_pool_invalid=当前测试使用的资源池处于禁用状态
selenium_grid_is_null=selenium_grid不能为空
ip_is_null=ip 地址/域名不能为空
port_is_null = 节点端口不能为空
concurrent_number_is_null = 节点最大线程数不能为空
monitor_is_null = node节点监控器不能为空
token_is_null = Token 不能为空
namespace_is_null=命名空间不能为空
deploy_name_is_null=Deploy Name 不能为空
api_test_image_is_null=API 镜像不能为空


#project
project_name_is_null=项目名称不能为空
project_name_already_exists=项目名称已存在
project_file_already_exists=项目下该文件已经存在
project_file_in_use=占用文件，无法删除。
#organization
organization_name_is_null=组织名不能为空
organization_name_already_exists=组织名已存在
organization_does_not_belong_to_user=当前组织不属于当前用户
organization_id_is_null=组织 ID 不能为空
organization_default_not_exists=默认组织不存在
#api
api_load_script_error=读取脚本失败
illegal_xml_format=不合法的 XML 格式
api_report_is_null=测试报告是未生成，无法更新
api_test_environment_already_exists=环境名称已存在
api_test=接口测试
#test case
test_case_node_level=层
test_case_node_level_tip=模块树最大深度为
test_case_module_not_null=所属模块不能为空
test_case_create_module_fail=创建模块失败
test_case_import_template_name=测试用例模版
test_case_import_template_sheet=模版
module_not_null=所属模块不能为空格
module_starts_with=所属模块必须以'/'开始
user_not_exists=该项目下无该用户
test_case_already_exists=该项目下已存在该测试用例
parse_data_error=解析数据出错
parse_empty_data=未解析到数据
missing_header_information=缺少头部信息
test_case_exist=该项目下已存在用例：
node_deep_limit=节点深度不超过8层！
before_delete_plan=该计划下存在关联测试用例，请先取消关联！
incorrect_format=格式错误
test_case_step_model_validate=必须为TEXT、STEP
test_case_priority_validate=必须为P0、P1、P2、P3
test_case_method_validate=必须为manual、auto
test_case_name=用例名称
test_case_type=用例类型
test_case_maintainer=维护人
test_case_priority=用例等级
test_case_method=测试方式
test_case_prerequisite=前置条件
test_case_remark=备注
test_case_step_desc=步骤描述
test_case_step_result=预期结果
test_case_module=所属模块
test_case=功能用例
user=用户
user_import_template_name=用户导入模板
user_import_template_sheet=模版
user_import_format_wrong=格式错误
user_import_id_is_repeat=ID重复
user_import_email_is_repeat=E-mail重复
user_import_password_format_wrong=密码格式错误
user_import_phone_format_wrong=手机号码格式错误
user_import_email_format_wrong=电子邮箱格式错误
user_import_organization_not_fond=组织未找到
org_admin=组织管理员
org_member=组织成员
test_manager=测试经理
tester=测试成员
read_only_user=只读用户
module=模块
num_needed_modify_testcase=修改用例时ID必填
num_needless_create_testcase=创建用例时无需ID
tag_tip_pattern=标签之间以分号或者逗号隔开
preconditions_optional=前置条件选填
remark_optional=备注选填
do_not_modify_header_order=请勿修改表头顺序
module_created_automatically=若无该模块将自动创建
options=选项
options_yes=是
options_no=否
required=必填
password_format_is_incorrect=有效密码：8-30位，英文大小写字母+数字+特殊字符（可选）
please_input_project_member=请填写该项目下的相关人员邮箱
test_case_report_template_repeat=同一工作空间下不能存在同名模版
plan_name_already_exists=测试计划名称已存在
test_case_already_exists_excel=文件中存在多条相同用例
test_case_module_already_exists=同层级下已存在该模块名称
api_test_name_already_exists=测试名称已经存在
functional_method_tip=功能测试不支持自动方式
custom_num_is_exist=用例自定义ID已存在
custom_num_is_not_exist=用例自定义ID不存在
id_required=ID必填
id_repeat_in_table=表格内ID重复
step_model_tip=步骤描述填写 STEP,文本描述请填写 TEXT (非必填)
case_status_not_exist=用例状态必须为未开始(Prepare)、进行中(Underway)、已完成(Completed)
issue_project_not_exist=ID不存在或其它错误
tapd_project_not_exist=关联的TAPD项目ID不存在
zentao_get_project_builds_fail=获取影响版本错误
zentao_project_id_not_exist=关联的禅道ID不存在或其它错误
#ldap
ldap_url_is_null=LDAP地址为空
ldap_dn_is_null=LDAP绑定DN为空
ldap_ou_is_null=LDAP参数OU为空
ldap_password_is_null=LDAP密码为空
ldap_connect_fail=连接LDAP失败
ldap_connect_fail_user=连接LDAP失败，绑定的DN或密码错误
ldap_user_filter_is_null=LDAP用户过滤器为空
ldap_user_mapping_is_null=LDAP用户属性映射为空
authentication_failed=用户认证失败,用户名或密码错误
user_not_found_or_not_unique=用户不存在或者不唯一
find_more_user=查找到多个用户
ldap_authentication_not_enabled=LDAP认证未启用
login_fail_email_null=登录失败，用户邮箱为空
login_fail_ou_error=登录失败，请检查用户OU
login_fail_filter_error=登录失败，请检查用户过滤器
check_ldap_mapping=检查LDAP属性映射
ldap_mapping_value_null=LDAP用户属性映射字段为空值
oauth_mapping_config_error=OAuth2属性映射配置错误
oauth_mapping_value_null=OAuth2用户属性映射字段为空值
#quota
quota_project_excess_ws_api=项目的接口测试数量总和不能超过工作空间的配额
quota_project_excess_ws_performance=项目的性能测试数量总和不能超过工作空间的配额
quota_project_excess_ws_max_threads=项目的最大并发数不能超过工作空间的配额
quota_project_excess_ws_max_duration=项目的压测时长不能超过工作空间的配额
quota_project_excess_ws_resource_pool=项目的资源池不能超过工作空间的资源池范围
quota_project_excess_ws_vum_total=项目的总vum数量总和不能超过工作空间配额
quota_vum_used_gt_vum_total=总vum数量不能小于已消耗的vum数量
quota_api_excess_organization=接口测试数量超过工作空间限额
quota_api_excess_project=接口测试数量超过项目限额
quota_performance_excess_organization=性能测试数量超过工作空间限额
quota_performance_excess_project=性能测试数量超过项目限额
quota_max_threads_excess_organization=最大并发数超过工作空间限额
quota_max_threads_excess_project=最大并发数超过项目限额
quota_duration_excess_organization=压测时长超过工作空间限额
quota_duration_excess_project=压测时长超过项目限额
quota_member_excess_organization=成员数超过工作空间配额
quota_member_excess_project=成员数超过项目配额
quota_project_excess_project=项目数超过工作空间配额
quota_vum_used_excess_organization=消耗的vum数量超过工作空间配额
quota_vum_used_excess_project=消耗的vum数量超过项目配额
import_xmind_count_error=思维导图导入用例数量不能超过 800 条
license_valid_license_error=授权认证失败
import_xmind_not_found=未找到测试用例
test_review_task_notice=测试评审任务通知
swagger_url_scheduled_import_notification=swagger_url定时导入通知
test_track.length_less_than=标题过长，字数必须小于
# check owner
check_owner_project=当前用户没有操作此项目的权限
check_owner_test=当前用户没有操作此测试的权限
check_owner_case=当前用户没有操作此资源的权限或资源不存在
check_owner_plan=当前用户没有操作此计划的权限
check_owner_review=当前用户没有操作此评审的权限
check_owner_comment=当前用户没有操作此评论的权限
check_owner_organization=当前用户没有操作此工作空间的权限
upload_content_is_null=导入内容为空
test_plan_notification=测试计划通知
task_defect_notification=缺陷任务通知
task_notification_=定时任务结果通知
api_definition_url_not_repeating=接口请求地址已经存在
api_definition_name_not_repeating=相同的名称-url组合已存在
task_notification_jenkins=jenkins任务通知
task_notification=任务通知
message_task_already_exists=任务接收人已经存在
#automation
automation_name_already_exists=同一个项目的同一模块下，场景名称不能重复
automation_exec_info=没有测试步骤，无法执行
delete_check_reference_by=被场景引用
not_execute=未执行
execute_not_pass=未通过
execute_pass=通过
import_fail_custom_num_exists=导入失败，自定义ID已存在
#authsource
authsource_name_already_exists=认证源名称已经存在
authsource_name_is_null=认证源名称不能为空
authsource_configuration_is_null=认证源配置不能为空
authsource_type_is_null=认证源类型不能为空
authsource_is_delete=认证源已被删除
custom_field_already=工作空间下已存在该字段：
template_already=工作空间下已存在该模板：
expect_name_exists=预期名称已存在
ssl_password_error=认证密码错误，请重新输入密码
ssl_file_error=认证文件加载失败，请检查认证文件
#log
api_definition=接口定义
api_definition_case=接口定义用例
api_automation=接口自动化
api_automation_schedule=接口自动化-定时任务
api_automation_report=测试报告
track_test_case=测试用例
track_test_case_review=用例评审
track_test_plan=测试计划
track_test_plan_schedule=测试计划-定时任务
track_bug=缺陷管理
track_report=报告
performance_test=性能测试
performance_test_report=性能测试报告
system_user=系统-用户
system_organization=系统-组织
system_test_resource=系统-测试资源池
system_parameter_setting=系统-系统参数设置
system_quota_management=系统-配额管理
system_authorization_management=系统-授权管理
organization_member=组织-成员
organization_organization=组织-工作空间
organization_service_integration=工作空间-服务集成
organization_message_settings=工作空间-消息设置
organization_template_settings_field=工作空间-模版设置-自定义字段
organization_template_settings_case=工作空间-模版设置-用例模版
organization_template_settings_bug=工作空间-模版设置-缺陷模版
project_project_manager=项目-项目管理
project_project_member=项目-成员
project_project_jar=項目-JAR包管理
project_environment_setting=项目-环境设置
project_file_management=项目-文件管理
personal_information_personal_settings=个人信息-个人设置
personal_information_apikeys=个人信息-API Keys
auth_title=系统认证
group_permission=用户组与权限
test_case_status_prepare=未开始
test_case_status_again=重新提审
test_case_status_running=进行中
test_case_status_finished=已完成
connection_expired=链接已失效，请重新获取
# track home
api_case=接口用例
performance_case=性能用例
scenario_case=场景用例
ui_case=UI用例
scenario_name_is_null=场景名称不能为空
test_case_status_error=失败
test_case_status_success=成功
test_case_status_trash=废弃
test_case_status_saved=已保存
create_user=创建人
test_case_status=用例状态
id_not_rightful=ID 不合法
tags_count=标签数量不能超过10个
tag_length=标签长度不能超过64个字符
step_length=用例步骤不能超过1000个字符
result_length=预期结果不能超过1000个字符
project_reference_multiple_plateform=项目指向多个第三方平台
# mock
mock_warning=未找到匹配的Mock期望
zentao_test_type_error=无效的 Zentao 请求
#项目报告
enterprise_test_report=项目报告
count=统计
cannot_find_project=未找到测试项目
project_repeatable_is_false=项目未配置URL可重复
#环境组
null_environment_group_name=环境组名称不存在
environment_group_name=环境组名称
environment_group_exist=已存在
environment_group_has_duplicate_project=每个项目只能选择一个环境！
#误报库
error_report_library=误报库
bug_jira_info_error=请检查服务集成信息或Jira项目ID
error_code_is_unique=错误代码不可重复
no_version_exists=不存在版本！请先创建项目的版本
jira_auth_error=账号名或密码(Token)错误
jira_auth_url_error=测试连接失败，请检查Jira地址是否正确
#ui 指令校验
param_error=参数校验失败！请检查
is_null=不能为空
url_is_null=URL 参数不能为空
frame_index_is_null=网页索引号不能为空
element_is_null=元素对象已经被删除
locator_is_null=元素定位参数不能有空
coord=坐标
input_content=输入内容
subitem_type=子选项类型
subitem=子选项值
varname=变量名
varname_or_value=变量名或变量值
attributeName=属性名
webtitle_varname=网页标题变量名
webhandle_varname=网页窗口 handle变量名
cant_be_negative=不能为负数
expression=表达式
times=循环次数
command=步骤
extract_type=提取信息类型
cmdValidation=断言
cmdValidateValue=断言值
cmdValidateText=弹窗文本
cmdValidateDropdown=下拉框
cmdValidateElement=元素断言
cmdValidateTitle=网页标题
cmdOpen=打开网页
cmdSelectWindow=切换窗口
cmdSetWindowSize=设置窗口大小
cmdSelectFrame=选择内嵌网页
cmdDialog=弹窗操作
cmdDropdownBox=下拉框操作
submit=提交表单
cmdSetItem=设置选项
cmdWaitElement=等待元素
cmdInput=输入操作
cmdMouseClick=鼠标点击
cmdMouseMove=鼠标移动
cmdMouseDrag=鼠标拖拽
cmdTimes=次数循环
cmdForEach=ForEach 循环
cmdWhile=While 循环
cmdIf=If
cmdElse=Else
cmdElseIf=ElseIf
close=关闭网页
cmdExtraction=数据提取
cmdExtractWindow=提取窗口信息
cmdExtractElement=提取元素信息
tcp_mock_not_unique=该TCP端口号已被使用
no_tcp_mock_port=无可用的TCP端口号，请联系管理员
name_already_exists_in_module=同层级下已经存在
# bug template copy
target_bug_template_not_checked=无法复制，未选中目标项目
source_bug_template_is_empty=复制错误，源项目为空
#模块相关
module.id.not_blank=ID不能为空
module.project_id.not_blank=模块项目ID不能为空
module.name.not_blank=名称不能为空
module.not.exist=模块不存在
module.parent.not.exist=文件模块父节点不存在
module.root=根目录

#plugin
get_plugin_instance_error=获取插件接口实现类错误！
# userRole
user_role_relation_exist_error=用户已在当前用户组！
internal_user_role_permission_error=内置用户组无法编辑与删除！
user_role_relation_remove_admin_user_permission_error=无法将 admin 用户将系统管理员用户组删除！
internal_admin_user_role_permission_error=内置管理员用户组无法修改权限!
# customField
internal_custom_field_permission_error=系统字段或模板无法删除！
internal_template_permission_error=系统模板无法删除！
default_template_permission_error=默认模板无法删除！
field_validate_error={0}字段参数值不合法
simple_field_validate_error_tips=字段参数值不合法, 请检查必填项或默认值!
status_definition_required_error=该状态无法取消

#result message
http_result_success=操作成功
http_result_unknown_exception=系统未知异常
http_result_validate=参数校验失败
http_result_unauthorized=用户认证失败
http_result_forbidden=权限认证失败
http_result_not_found=%s不存在

enum_value_valid_message=枚举值不合法，必须为

#system organization
organization_member_log=组织成员
#system project
project_admin=项目管理员
project_member=项目成员
project=项目
add=添加
delete=删除
update=更新
recover=恢复
copy=复制
move=移动
archive=归档
run=执行
project_is_not_exist=项目不存在

#permission
permission.system.name=系统
permission.org.name=组织
permission.project.name=项目
permission.read=查询
permission.add=创建
permission.edit=编辑
permission.delete=删除
permission.import=导入
permission.recover=恢复
permission.export=导出
permission.execute=执行
permission.debug=调试
permission.association=关联
permission.association_and_disassociation=关联/取消关联
file_name_illegal_error=文件名不合法
plugin_enable_error=插件未启用
plugin_permission_error=没有该插件的访问权限

scheduled_tasks=定时任务
template_scene_illegal_error=使用场景不合法
# 内置的模板或字段
custom_field.functional_priority=用例等级
custom_field.bug_degree=严重程度
template.default=默认模板

set_default_template=设置默认模板
project_template_enable=开启项目模板

global_parameters_already_exist=全局参数已存在
global_parameters_is_not_exist=全局参数不存在
parent.node.not_blank=父节点不能为空
node.not_blank=节点不能为空
node.name.repeat=该层级已有此模块名称
project.cannot.match.parent=和父节点的项目无法匹配
api_test_environment_not_exist=环境不存在
mock_environment_not_delete=Mock环境不允许删除

# 状态流
status=状态
status_flow.name=状态流
status_item.bug_new=新建
status_item.bug_in_process=处理中
status_item.bug_closed=已关闭
status_item.bug_resolved=已解决
status_item.bug_rejected=已拒绝

permission.api_test.name=接口测试
permission.api_debug.name=调试
permission.api_definition.name=定义
permission.api_case.name=用例
permission.api_mock.name=Mock
permission.api_doc.name=文档
permission.api_doc.share=分享
permission.api_definition.import=导入
permission.api_definition.export=导出
permission.api_definition.execute=执行
permission.api_report=报告

#接口管理
api_definition_not_exist=接口不存在
file_upload_fail=文件上传失败
api_test_case_name_exist=接口用例名称已存在
api_test_case_not_exist=接口用例不存在
please_recover_the_interface_data_first=请先恢复接口数据
batch_edit_type_error=批量编辑类型错误
environment_id_is_null=环境ID不能为空
environment_group_id_is_null=环境组ID不能为空
environment_group_is_not_exist=环境组不存在
environment_is_not_exist=环境不存在
status_is_null=状态不能为空
api_scenario_is_not_exist=场景不存在
priority_is_null=用例等级不能为空
apikey_has_expired=ApiKey 已过期
user_key.id.not_blank=ApiKey ID不能为空
expire_time_not_null=过期时间不能为空
permission.organization.name=组织
swagger_parse_error_with_auth=Swagger 解析失败，请确认认证信息是否正确或文件格式是否正确！
swagger_parse_error=Swagger 解析失败，请确认文件格式是否正确！
#测试计划
permission.test_plan.name=测试计划
permission.test_plan=计划
permission.test_plan_report=报告

excel.template.id=非必填，ID为系统自动生成；导入用例时，可选择择是否覆盖相同ID的用例；当ID为空或不存在时默认为新增用例；
excel.template.case_edit_type=非必填，步骤描述填写STEP，文本描述填写TEXT，未填写默认为TEXT
excel.template.tag=非必填，标签之间以分号或者逗号隔开
excel.template.text_description=非必填，编辑模式为STEP时，步骤描述会根据标识[1] [2] [3]...来判断是否将单元格拆分为多个步骤，没有则为一个步骤
excel.template.member=请填写该项目下的相关人员的邮箱
excel.template.not_required=非必填
case_import_table_header_missing=表头信息缺失！
functional_case.module.length_less_than=标题过长，字数必须小于
custom_field_required_tip=[%s]为必填项
custom_field_array_tip=[%s]必须是数组
custom_field_datetime_tip=[%s]必须为时间日期格式[%s]
custom_field_date_tip=[%s]必须为日期格式[%s]
custom_field_member_tip=[%s]必须当前项目成员
custom_field_select_tip=[%s]必须为%s
custom_field_int_tip=[%s]必须为整型
custom_field_float_tip=[%s]必须为数字
check_import_excel_error=校验失败，请检查文件是否正确
custom_field_multip_input_tip=[%s]个数不能超过15个
custom_field_multip_input_length_tip=[%s]长度不能超过64个字符
custom_field_input_length_tip=[%s]长度不能超过255个字符
custom_field_textarea_length_tip=[%s]长度不能超过1000个字符
#关联
relate_source_id_not_blank=关联来源ID不能为空
relate_source_id_length_range=关联来源ID必须在{min}和{max}之间
relate_source_type_not_blank=关联资源类型不能为空

api_import_schedule=接口定义-定时导入任务

project.description.length_range=项目描述长度必须在{min}和{max}之间
api_test_environment_datasource_connect_failed=数据源连接失败

permission.api_definition.delete_and_recover=删除/恢复
permission.service_integration.reset=重置
ms_url_not_available=资源池无法访问当前站点
api_key_not_exist=ApiKey不存在
current_user_can_not_operation_api_key=当前用户无操作该ApiKey的权限

#全局请求头
global_request_header=全局请求头

url_format_error=请检查Swagger URL是否输入正确！
swagger_version_error=Swagger 版本不支持，请检查是否为3.0版本!
test_plan=测试计划 ID

#user_view
internal_user_view_permission_error=系统视图无法删除！
user_view.all_data=全部数据
user_view.my_follow=我关注的
user_view.my_create=我创建的
user_view.my_review=我评审的
user_view.archived=已归档
user_view_exist=视图已存在

#task_error_message
task_error_message.invalid_resource_pool=没有可用的资源池
task_error_message.case_not_exist=用例不存在