excel.parse.error=Excelè§£æå¤±è´¥
id.not_blank=IDä¸è½ä¸ºç©º
permission.system_user.invite=éè¯·ç¨æ·
role.not.global.system=è§è²ä¸æ¯å¨å±ç³»ç»è§è²
role.not.contains.member=è§è²ä¸åå«ç³»ç»æåè§è²
schedule.cron.error=Cronè¡¨è¾¾å¼éè¯¯
user.not.login=æªè·åå°ç»å½ç¨æ·
user.not.empty=ç¨æ·ä¸è½ä¸ºç©º
user.not.exist=ç¨æ·ä¸å­å¨
personal.no.permission=æ ææä½éæ¬äººè´¦æ·
personal.change.password=ä¿®æ¹äºå¯ç 
personal.change.info=ä¿®æ¹äºä¿¡æ¯
personal.user.name=ç¨æ·åç§°
personal.user.phone=ææºå·
personal.user.email=ç¨æ·é®ç®±
default.module=é»è®¤æ¨¡å
file_format_does_not_meet_requirements=æä»¶æ ¼å¼ä¸ç¬¦å
auth_source.id.not_blank=è®¤è¯æºIDä¸è½ä¸ºç©º
auth_source.status.length_range=è®¤è¯æºç¶æé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
auth_source.status.not_blank=è®¤è¯æºç¶æä¸è½ä¸ºç©º
license.id.not_blank=License IDä¸è½ä¸ºç©º
message_task.id.not_blank=æ¶æ¯éç¥ä»»å¡IDä¸è½ä¸ºç©º
message_task.type.not_blank=æ¶æ¯éç¥ä»»å¡ç±»åä¸è½ä¸ºç©º
message_task.type.length_range=æ¶æ¯éç¥ä»»å¡ç±»åé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
message_task.event.not_blank=æ¶æ¯éç¥ä»»å¡äºä»¶ä¸è½ä¸ºç©º
message_task.event.length_range=æ¶æ¯éç¥ä»»å¡äºä»¶é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
message_task.receiver.not_blank=æ¶æ¯éç¥ä»»å¡æ¥æ¶èä¸è½ä¸ºç©º
message_task.receiver.length_range=æ¶æ¯éç¥ä»»å¡æ¥æ¶èé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
message_task.task_type.not_blank=æ¶æ¯éç¥ä»»å¡ä»»å¡ç±»åä¸è½ä¸ºç©º
message_task.task_type.length_range=æ¶æ¯éç¥ä»»å¡ä»»å¡ç±»åé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
message_task.test_id.not_blank=æ¶æ¯éç¥ä»»å¡æµè¯IDä¸è½ä¸ºç©º
message_task.test_id.length_range=æ¶æ¯éç¥ä»»å¡æµè¯IDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
message_task.project_id.not_blank=æ¶æ¯éç¥ä»»å¡é¡¹ç®IDä¸è½ä¸ºç©º
message_task.project_id.length_range=æ¶æ¯éç¥ä»»å¡é¡¹ç®IDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
message_task_blob.id.not_blank=æ¶æ¯éç¥ä»»å¡IDä¸è½ä¸ºç©º
notification.id.not_blank=æ¶æ¯éç¥IDä¸è½ä¸ºç©º
notification.type.not_blank=æ¶æ¯éç¥ç±»åä¸è½ä¸ºç©º
notification.type.length_range=æ¶æ¯éç¥ç±»åé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
notification.receiver.not_blank=æ¶æ¯éç¥æ¥æ¶èä¸è½ä¸ºç©º
notification.receiver.length_range=æ¶æ¯éç¥æ¥æ¶èé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
notification.title.not_blank=æ¶æ¯éç¥æ é¢ä¸è½ä¸ºç©º
notification.title.length_range=æ¶æ¯éç¥æ é¢é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
notification.status.not_blank=æ¶æ¯éç¥ç¶æä¸è½ä¸ºç©º
notification.status.length_range=æ¶æ¯éç¥ç¶æé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
notification.operator.not_blank=æ¶æ¯éç¥æä½èä¸è½ä¸ºç©º
notification.operator.length_range=æ¶æ¯éç¥æä½èé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
notification.operation.not_blank=æ¶æ¯éç¥æä½ä¸è½ä¸ºç©º
notification.operation.length_range=æ¶æ¯éç¥æä½é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
notification.resource_id.not_blank=æ¶æ¯éç¥èµæºIDä¸è½ä¸ºç©º
notification.resource_id.length_range=æ¶æ¯éç¥èµæºIDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
notification.resource_type.not_blank=æ¶æ¯éç¥èµæºç±»åä¸è½ä¸ºç©º
notification.resource_type.length_range=æ¶æ¯éç¥èµæºç±»åé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
notification.resource_name.not_blank=æ¶æ¯éç¥èµæºåç§°ä¸è½ä¸ºç©º
notification.resource_name.length_range=æ¶æ¯éç¥èµæºåç§°é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
novice_statistics.id.not_blank=æ°ææIDä¸è½ä¸ºç©º
novice_statistics.guide_step.not_blank=æ°æææ­¥éª¤ä¸è½ä¸ºç©º
novice_statistics.guide_step.length_range=æ°æææ­¥éª¤é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
operation_log.id.not_blank=æä½æ¥å¿IDä¸è½ä¸ºç©º
operation_log.project_id.not_blank=æä½æ¥å¿é¡¹ç®IDä¸è½ä¸ºç©º
operation_log.project_id.length_range=æä½æ¥å¿é¡¹ç®IDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
operation_log.organization_id.not_blank=æä½æ¥å¿ç»ç»IDä¸è½ä¸ºç©º
operation_log.organization_id.length_range=æä½æ¥å¿ç»ç»IDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
operation_log.batch_id.not_blank=æä½æ¥å¿æ¹æ¬¡IDä¸è½ä¸ºç©º
operation_log_resource.id.not_blank=æä½æ¥å¿èµæºIDä¸è½ä¸ºç©º
operation_log_resource.operating_log_id.not_blank=æä½æ¥å¿èµæºæä½æ¥å¿IDä¸è½ä¸ºç©º
operation_log_resource.operating_log_id.length_range=æä½æ¥å¿èµæºæä½æ¥å¿IDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
operation_log_resource.source_id.not_blank=æä½æ¥å¿èµæºæ¥æºIDä¸è½ä¸ºç©º
operation_log_resource.source_id.length_range=æä½æ¥å¿èµæºæ¥æºIDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
plugin_blob.id.not_blank=æä»¶IDä¸è½ä¸ºç©º
quota.id.not_blank=éé¢IDä¸è½ä¸ºç©º
schedule.id.not_blank=å®æ¶ä»»å¡IDä¸è½ä¸ºç©º
schedule.type.not_blank=å®æ¶ä»»å¡ç±»åä¸è½ä¸ºç©º
schedule.type.length_range=å®æ¶ä»»å¡ç±»åé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
schedule.value.not_blank=å®æ¶ä»»å¡å¼ä¸è½ä¸ºç©º
schedule.value.length_range=å®æ¶ä»»å¡å¼é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
schedule.job.not_blank=å®æ¶ä»»å¡ä¸è½ä¸ºç©º
schedule.job.length_range=å®æ¶ä»»å¡é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
schedule.create_user.not_blank=å®æ¶ä»»å¡åå»ºäººä¸è½ä¸ºç©º
schedule.create_user.length_range=å®æ¶ä»»å¡åå»ºäººé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´

#system
system_parameter.param_key.not_blank=ç³»ç»åæ°Keyä¸è½ä¸ºç©º
system_parameter.type.not_blank=ç³»ç»åæ°ç±»åä¸è½ä¸ºç©º
system_parameter.type.length_range=ç³»ç»åæ°ç±»åé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
system_model_not_exist=æ¨¡åä¿¡æ¯ä¸å­å¨
system_model_not_enable=æ¨¡åæªå¯ç¨
system_model_test_link_error=æ¨¡åé¾æ¥å¤±è´¥ï¼è¯·æ£æ¥éç½®
system_model_test_chat_error=æ¨¡åè°ç¨éè¯¯ï¼éè¯¯ç ï¼
system_model_name_exist=æ¨¡ååç§°
system_model_name_exist_label=å·²è¢«ç³»ç»ä¸­å¶ä»ç¨æ·å ç¨ï¼è¯·æ´æ¢å¯ä¸åç§°åéè¯


# èµæºæ± 
test_resource.id.not_blank=èµæºæ± èç¹IDä¸è½ä¸ºç©º
test_resource.test_resource_pool_id.not_blank=èµæºæ± IDä¸è½ä¸ºç©º
test_resource.test_resource_pool_id.length_range=èµæºæ± IDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
test_resource.status.not_blank=èµæºæ± èç¹ç¶æä¸è½ä¸ºç©º
test_resource.status.length_range=èµæºæ± èç¹ç¶æé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
test_resource_pool.node_must_be_one=èµæºæ± èç¹æå¤ä¸º1ä¸ª
test_resource_pool.node_must_have_one=èµæºæ± èç¹è³å°ä¿çä¸ä¸ª
test_resource_pool.id.not_blank=èµæºæ± IDä¸è½ä¸ºç©º
test_resource_pool.name.not_blank=èµæºæ± åç§°ä¸è½ä¸ºç©º
test_resource_pool.name.length_range=èµæºæ± åç§°é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
test_resource_pool.type.not_blank=èµæºæ± ç±»åä¸è½ä¸ºç©º
test_resource_pool.type.length_range=èµæºæ± ç±»åé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
test_resource_pool.status.not_blank=èµæºæ± ç¶æä¸è½ä¸ºç©º
test_resource_pool.status.length_range=èµæºæ± ç¶æé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´

user.not.delete=ç¨æ·ä¸è½å é¤
user.not.disable=ç¨æ·ä¸è½ç¦ç¨
user.id.not_blank=ç¨æ·IDä¸è½ä¸ºç©º
user.name.not_blank=ç¨æ·åç§°ä¸è½ä¸ºç©º
user.name.length_range=ç¨æ·åç§°é¿åº¦å¿é¡»å¨ {min} å {max} ä¹é´
user.phone.not_blank=ç¨æ·ææºå·ä¸è½ä¸ºç©º
user.phone.error=ææºå·è¾å¥éè¯¯
user.password.error=éªè¯ç¨æ·å¯ç å¤±è´¥
user.password.not.blank=ç¨æ·å¯ç ä¸è½ä¸ºç©º
user.email.not_blank=ç¨æ·é®ç®±ä¸è½ä¸ºç©º
user.email.length_range=ç¨æ·é®ç®±é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user.email.hi=ä½ å¥½
user.email.invite_ms=éè¯·ä½ å å¥MeterSphere
user.email.invite_click=ç¹å»å å¥
user.email.invite_tips=å¦ææé®æ æ³ç¹å»ï¼è¯·ç´æ¥è®¿é®ä»¥ä¸é¾æ¥ï¼
user.email.invite_limited_time=æ­¤é¾æ¥èªåéä¹æ¶èµ·24å°æ¶åè¿æ
user.email.repeat=é®ç®±éå¤
user.email.import.in_system=ç³»ç»å·²å­å¨
user.reset.password=éç½®å¯ç 
user.delete=å é¤ç¨æ·
user.enable=å¯ç¨ç¨æ·
user.disable=ç¦ç¨ç¨æ·
user.add.project=æ·»å é¡¹ç®
user.add.org=æ·»å ç»ç»
user.add.group=æ·»å ç¨æ·ç»
user.invite.email=é®ç®±éè¯·
register.by.invite=éè¿é®ç®±éè¯·æ³¨åãéè¯·äººï¼
user.not.invite.or.expired=è¯¥ç¨æ·æ²¡æè¢«éè¯·æéè¯·å·²è¿æ
user.email.invalid=ç¨æ·é®ç®±æ ¼å¼ä¸æ­£ç¡®
user.status.not_blank=ç¨æ·ç¶æä¸è½ä¸ºç©º
user.status.length_range=ç¨æ·ç¶æé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user.source.not_blank=ç¨æ·æ¥æºä¸è½ä¸ºç©º
user.source.length_range=ç¨æ·æ¥æºé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user.create_user.not_blank=ç¨æ·åå»ºäººä¸è½ä¸ºç©º
user.create_user.length_range=ç¨æ·åå»ºäººé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user_extend.id.not_blank=ç¨æ·IDä¸è½ä¸ºç©º
user_key.id.not_blank=ç¨æ·ApiKey IDä¸è½ä¸ºç©º
user_key.create_user.not_blank=ç¨æ·ApiKeyåå»ºäººä¸è½ä¸ºç©º
user_key.create_user.length_range=ç¨æ·ApiKeyåå»ºäººé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user_key.access_key.not_blank=ç¨æ·ApiKey access keyä¸è½ä¸ºç©º
user_key.access_key.length_range=ç¨æ·ApiKey access keyé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user_key.secret_key.not_blank=ç¨æ·ApiKey secret keyä¸è½ä¸ºç©º
user_key.secret_key.length_range=ç¨æ·ApiKey secret keyé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user.info.not_empty=ç¨æ·ä¿¡æ¯ä¸è½ä¸ºç©º
user.organizationId.not_blank=ç¨æ·ç»ç»ä¸è½ä¸ºç©º
user.projectId.not_blank=ç¨æ·é¡¹ç®ä¸è½ä¸ºç©º
user_role.id.not_blank=ç¨æ·ç»IDä¸è½ä¸ºç©º
user_role.name.not_blank=ç¨æ·ç»åç§°ä¸è½ä¸ºç©º
user_role.name.length_range=ç¨æ·ç»åç§°é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user_role.system.not_blank=æ¯å¦æ¯ç³»ç»ç¨æ·ç»ä¸è½ä¸ºç©º
user_role.system.length_range=æ¯å¦æ¯ç³»ç»ç¨æ·ç»é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user_role.type.not_blank=ç¨æ·ç»ç±»åä¸è½ä¸ºç©º
user_role.type.length_range=ç¨æ·ç»ç±»åé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user_role.create_user.not_blank=ç¨æ·ç»åå»ºäººä¸è½ä¸ºç©º
user_role.create_user.length_range=ç¨æ·ç»åå»ºäººé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user_role.scope_id.not_blank=ç¨æ·ç»åºç¨èå´ä¸è½ä¸ºç©º
user_role.scope_id.length_range=ç¨æ·ç»åºç¨èå´é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user_role_permission.id.not_blank=ç¨æ·ç»æéIDä¸è½ä¸ºç©º
user_role_permission.role_id.not_blank=ç¨æ·ç»æéç¨æ·ç»IDä¸è½ä¸ºç©º
user_role_permission.role_id.length_range=ç¨æ·ç»æéç¨æ·ç»IDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user_role_permission.permission_id.not_blank=ç¨æ·ç»æéæéIDä¸è½ä¸ºç©º
user_role_permission.permission_id.length_range=ç¨æ·ç»æéæéIDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user_role_permission.module_id.not_blank=ç¨æ·ç»æéæ¨¡åIDä¸è½ä¸ºç©º
user_role_permission.module_id.length_range=ç¨æ·ç»æéæ¨¡åIDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user_role_relation.id.not_blank=ç¨æ·ç»å³ç³»IDä¸è½ä¸ºç©º
user_role_relation.user_id.not_blank=ç¨æ·ç»å³ç³»ç¨æ·IDä¸è½ä¸ºç©º
user_role_relation.user_id.length_range=ç¨æ·ç»å³ç³»ç¨æ·IDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user_role_relation.role_id.not_blank=ç¨æ·ç»å³ç³»ç¨æ·ç»IDä¸è½ä¸ºç©º
user_role_relation.role_id.length_range=ç¨æ·ç»å³ç³»ç¨æ·ç»IDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user_role_relation.source_id.not_blank=ç¨æ·ç»å³ç³»æ¥æºIDä¸è½ä¸ºç©º
user_role_relation.source_id.length_range=ç¨æ·ç»å³ç³»æ¥æºIDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
organization.id.not_blank=ç»ç»IDä¸è½ä¸ºç©º
organization.name.not_blank=ç»ç»åç§°ä¸è½ä¸ºç©º
organization.name.length_range=ç»ç»åç§°é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
organization.create_user.not_blank=ç»ç»åå»ºäººä¸è½ä¸ºç©º
organization.create_user.length_range=ç»ç»åå»ºäººé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
and_add_organization_admin=å¹¶æ·»å ç»ç»ç®¡çå
organization_add_member_ids_empty=ç»ç»æ·»å æåä¸è½ä¸ºç©º
organization_not_exist=ç»ç»ä¸å­å¨
organization_member_not_exist=ç»ç»æåä¸å­å¨
global_user_role_permission_error=æ²¡ææéæä½éå¨å±ç¨æ·ç»
global_user_role_exist_error=å¨å±ç¨æ·ç»å·²å­å¨
global_user_role_relation_system_permission_error=æ²¡ææéæä½éç³»ç»çº§å«ç¨æ·ç»
global_user_role_limit_error=è³å°éè¦æä¸ä¸ªç¨æ·ç»
organization_user_role_permission_error=æ²¡ææéæä½éç»ç»ç¨æ·ç»
project_user_role_permission_error=æ²¡ææéæä½éé¡¹ç®ç¨æ·ç»
no_global_user_role_permission_error=æ²¡ææéæä½å¨å±ç¨æ·ç»
user_role_exist=ç¨æ·ç»å·²å­å¨
user_role_not_exist=ç¨æ·ç»ä¸å­å¨
user_role_not_edit=ç¨æ·ç»æ æ³ç¼è¾
at_least_one_user_role_require=è³å°éè¦ä¸ä¸ªç¨æ·ç»
org_at_least_one_user_role_require=ç»ç»æåè³å°æä¸ä¸ªç¨æ·ç»ï¼å¦éä»ç»ç»ç§»é¤æåè¯·å¨æååè¡¨æä½ï¼
project_at_least_one_user_role_require=é¡¹ç®æåè³å°æä¸ä¸ªç¨æ·ç»ï¼å¦éä»é¡¹ç®ç§»é¤æåè¯·å¨æååè¡¨æä½ï¼
default_organization_not_allow_delete=é»è®¤ç»ç»æ æ³å é¤
organization_template_permission_error=æªå¼å¯ç»ç»æ¨¡æ¿
# plugin
plugin.id.not_blank=IDä¸è½ä¸ºç©º
plugin.id.length_range=IDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
plugin.name.not_blank=æä»¶åç§°ä¸è½ä¸ºç©º
plugin.name.length_range=æä»¶åç§°é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
plugin.plugin_id.not_blank=æä»¶IDï¼åç§°å çæ¬å·ï¼ä¸è½ä¸ºç©º
plugin.plugin_id.length_range=æä»¶IDï¼åç§°å çæ¬å·ï¼é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
plugin.file_name.not_blank=æä»¶åä¸è½ä¸ºç©º
plugin.file_name.length_range=æä»¶åé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
plugin.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
plugin.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
plugin.scenario.not_blank=æä»¶ä½¿ç¨åºæ¯API/PLATFORMä¸è½ä¸ºç©º
plugin.scenario.length_range=æä»¶ä½¿ç¨åºæ¯API/PLATFORMé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
plugin.exist=æä»¶åç§°å·²å­å¨
plugin.type.exist=æä»¶ç±»åå·²å­å¨
plugin.script.exist=èæ¬idéå¤
plugin.script.format=èæ¬æ ¼å¼éè¯¯
plugin.parse.error=æä»¶è§£æå¤±è´¥, è¯·éæ°ä¸ä¼ 
# serviceIntegration
service_integration.id.not_blank=IDä¸è½ä¸ºç©º
service_integration.id.length_range=IDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
service_integration.plugin_id.not_blank=æä»¶çIDä¸è½ä¸ºç©º
service_integration.plugin_id.length_range=æä»¶çIDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
service_integration.organization_id.not_blank=ç»ç»IDä¸è½ä¸ºç©º
service_integration.organization_id.length_range=ç»ç»IDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
service_integration_exist_error=æå¡éæéç½®å·²å­å¨
# customField
service_integration.configuration.not_blank=æå¡éæéç½®ä¸è½çºç©º
permission.system_custom_field.name=èªå®ä¹å­æ®µ
custom_field.exist=èªå®ä¹å­æ®µå·²å­å¨
template.exist=æ¨¡æ¿å·²å­å¨
status_item.not.exist=ç¶æé¡¹ä¸å­å¨
status_item.exist=ç¶æé¡¹å·²å­å¨
custom_field_option.field_id.not_blank=èªå®ä¹å­æ®µIDä¸è½ä¸ºç©º
custom_field_option.field_id.length_range=èªå®ä¹å­æ®µIDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
custom_field_option.value.not_blank=éé¡¹å¼ä¸è½ä¸ºç©º
custom_field_option.value.length_range=éé¡¹å¼é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
custom_field_option.text.not_blank=éé¡¹å¼åç§°ä¸è½ä¸ºç©º
custom_field_option.text.length_range=éé¡¹å¼åç§°é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
custom_field_option.internal.not_blank=éé¡¹å¼æ¯å¦åç½®ä¸è½ä¸ºç©º
custom_field_option.pos.not_blank=éé¡¹å¼é¡ºåºä¸è½ä¸ºç©º

# permission
permission.system_plugin.name=æä»¶
permission.system_organization_project.name=ç»ç»ä¸é¡¹ç®
permission.system_user.name=ç¨æ·
permission.system_user_role.name=ç¨æ·ç»
permission.system_test_resource_pool.name=èµæºæ± 
permission.system_parameter_setting.name=åæ°è®¾ç½®
permission.system_parameter_setting_base.read=åºç¡è®¾ç½®-æ¥è¯¢
permission.system_parameter_setting_base.update=åºç¡è®¾ç½®-ç¼è¾
permission.system_parameter_setting_display.read=çé¢è®¾ç½®-æ¥è¯¢
permission.system_parameter_setting_display.update=çé¢è®¾ç½®-ç¼è¾
permission.system_parameter_setting_auth.read=è®¤è¯è®¾ç½®-æ¥è¯¢
permission.system_parameter_setting_auth.add=è®¤è¯è®¾ç½®-åå»º
permission.system_parameter_setting_auth.update=è®¤è¯è®¾ç½®-ç¼è¾
permission.system_parameter_setting_auth.delete=è®¤è¯è®¾ç½®-å é¤
permission.system_parameter_setting_memory_clean.read=åå­æ¸ç-æ¥è¯¢
permission.system_parameter_setting_memory_clean.update=åå­æ¸ç-ç¼è¾
permission.system_parameter_setting_qrcode.read=æ«ç ç»å½-æ¥è¯¢
permission.system_parameter_setting_qrcode.update=æ«ç ç»å½-ç¼è¾
permission.system_parameter_setting_ai_model.read=AIæ¨¡åè®¾ç½®-æ¥è¯¢
permission.system_parameter_setting_ai_model.update=AIæ¨¡åè®¾ç½®-ç¼è¾
permission.organization_user_role.name=ç¨æ·ç»
permission.organization_member.name=æå
permission.service_integration.name=æå¡éæ
permission.system_auth=ææ
permission.system_organization_project_member.add=æ·»å æå
permission.system_organization_project_member.update=ç¼è¾æå
permission.system_organization_project_member.delete=ç§»é¤æå
permission.system_operation_log.name=æ¥å¿
permission.organization_operation_log.name=æ¥å¿
permission.personal_settings=ä¸ªäººè®¾ç½®
permission.my_settings=æçè®¾ç½®
permission.api_key=APIKEY
permission.my_settings_personal_info=ä¸ªäººä¿¡æ¯
permission.organization_project.recover=æ¤éå é¤
permission.organization_member.add=æ·»å 
permission.organization_member.invite=éè¯·ç¨æ·
permission.organization_member.update=ç¼è¾
permission.organization_member.delete=ç§»é¤
# template
permission.system_template_custom_field.name=æ¨¡æ¿åå­æ®µçå³èå³ç³»
template_custom_field.exist=æ¨¡æ¿åå­æ®µçå³èå³ç³»å·²å­å¨
template_custom_field.id.not_blank=IDä¸è½ä¸ºç©º
template_custom_field.id.length_range=IDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
template_custom_field.field_id.not_blank=å­æ®µIDä¸è½ä¸ºç©º
template_custom_field.field_id.length_range=å­æ®µIDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
template_custom_field.template_id.not_blank=æ¨¡çIDä¸è½ä¸ºç©º
template_custom_field.template_id.length_range=æ¨¡çIDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
template_custom_field.default_value.length_range=é»è®¤å¼é¿åº¦å¿é¡»å°äº{max}
permission.organization_custom_field.name=èªå®ä¹å­æ®µ
permission.organization_template.name=æ¨¡æ¿
permission.system_organization_template.enable=å¯ç¨é¡¹ç®æ¨¡æ¿
# ç¶ææµ
permission.status_item.name=ç¶æé¡¹
status_item.id.not_blank=ç¶æIDä¸è½ä¸ºç©º
status_item.id.length_range=ç¶æIDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
status_item.name.not_blank=ç¶æåç§°ä¸è½ä¸ºç©º
status_item.name.length_range=ç¶æåç§°é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
status_item.scene.not_blank=ä½¿ç¨åºæ¯ä¸è½ä¸ºç©º
status_item.scene.length_range=ä½¿ç¨åºæ¯é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
status_item.scope_type.not_blank=ç»ç»æé¡¹ç®çº§å«å­æ®µï¼PROJECT, ORGANIZATIONï¼ä¸è½ä¸ºç©º
status_item.scope_type.length_range=ç»ç»æé¡¹ç®çº§å«å­æ®µï¼PROJECT, ORGANIZATIONï¼é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
status_item.scope_id.not_blank=ç»ç»æé¡¹ç®IDä¸è½ä¸ºç©º
status_item.scope_id.length_range=ç»ç»æé¡¹ç®IDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
status_definition.status_id.not_blank=ç¶æIDä¸è½ä¸ºç©º
status_definition.status_id.length_range=ç¶æIDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
status_definition.definition_id.not_blank=ç¶æå®ä¹ID(å¨ä»£ç ä¸­å®ä¹)ä¸è½ä¸ºç©º
status_definition.definition_id.length_range=ç¶æå®ä¹ID(å¨ä»£ç ä¸­å®ä¹)é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
status_flow.id.not_blank=IDä¸è½ä¸ºç©º
status_flow.id.length_range=IDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
status_flow.from_id.not_blank=èµ·å§ç¶æIDä¸è½ä¸ºç©º
status_flow.from_id.length_range=èµ·å§ç¶æIDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
status_flow.to_id.not_blank=ç®çç¶æIDä¸è½ä¸ºç©º
status_flow.to_id.length_range=ç®çç¶æIDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
# message
user.remove=å·²è¢«ç§»é¤
alert_others=éç¥äºº

current_user_local_config_not_exist=å½åç¨æ·æ¬å°éç½®ä¸å­å¨
current_user_local_config_exist=å½åç¨æ·æ¬å°éç½®å·²å­å¨
# user_local_config
user_local_config.id.not_blank=ç¨æ·æ¬å°éç½®IDä¸è½ä¸ºç©º
user_local_config.id.length_range=ç¨æ·æ¬å°éç½®IDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user_local_config.user_url.not_blank=ç¨æ·æ¬å°éç½®ç¨æ·URLä¸è½ä¸ºç©º
user_local_config.user_url.length_range=ç¨æ·æ¬å°éç½®ç¨æ·URLé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
user_local_config.type.not_blank=ç¨æ·æ¬å°éç½®ç±»åä¸è½ä¸ºç©º
user_local_config.type.length_range=ç¨æ·æ¬å°éç½®ç±»åé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
current_user_local_config_not_validate=å½åç¨æ·æ¬å°éç½®ä¸åæ³

# operation_history
operation_history.id.not_blank=åæ´è®°å½ ID ä¸è½ä¸ºç©º
operation_history.project_id.not_blank=åæ´è®°å½é¡¹ç® ID ä¸è½ä¸ºç©º
operation_history.project_id.length_range=åæ´è®°å½é¡¹ç® ID é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
operation_history.type.not_blank=åæ´è®°å½æä½ç±»åä¸è½ä¸ºç©º
operation_history.type.length_range=åæ´è®°å½æä½ç±»åé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
operation_history.source_id.not_blank=åæ´è®°å½èµæº ID ä¸è½ä¸ºç©º
operation_history.version_id.not_blank=åæ´è®°å½çæ¬ ID ä¸è½ä¸ºç©º
operation_history.version_id.length_range=åæ´è®°å½çæ¬ ID é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´

permission.organization_task_center.name=ä»»å¡ä¸­å¿
permission.organization_task_center.stop=åæ­¢
permission.case_task_center.read=ç³»ç»å³æ¶ä»»å¡-æ¥è¯¢
permission.case_task_center.exec=ç³»ç»å³æ¶ä»»å¡-æ§è¡/åæ­¢
permission.case_task_center.delete=ç³»ç»å³æ¶ä»»å¡-å é¤
permission.schedule_task_center.read=ç³»ç»åå°ä»»å¡-æ¥è¯¢
permission.schedule_task_center.update=ç³»ç»åå°ä»»å¡-ç¼è¾
permission.schedule_task_center.delete=ç³»ç»åå°ä»»å¡-å é¤
user_open_source_max=ç³»ç»ç¨æ·æ°è¶é¢ï¼{0}äººï¼ï¼ç»§ç»­æ·»å ç¨æ·å¯ç³è¯·ä¼ä¸çéç¨
user_dept_max=ç³»ç»ç¨æ·æ°è¶é¢ï¼{0}äººï¼ï¼ç»§ç»­æ·»å ç¨æ·å¯ç³è¯·ä¼ä¸çæ©å®¹

# file_upload
file_upload.size_limit=ä¸ä¼ æä»¶å¤§å°è¶è¿ç³»ç»éå¶

#æ¨¡å
model_source.name.not_blank=æ¨¡ååç§°ä¸è½ä¸ºç©º
model_source.type.not_blank=æ¨¡åç±»åä¸è½ä¸ºç©º
model_source.provider.not_blank=æ¨¡åä¾åºåä¸è½ä¸ºç©º
model_source.avatar.not_blank=æ¨¡åå¾çä¸è½ä¸ºç©º
model_source.permission_type.not_blank=æ¨¡åæå±ç±»åä¸è½ä¸ºç©º
model_source.status.not_blank=æ¨¡åç¶æä¸è½ä¸ºç©º
model_source.owner.not_blank=æ¨¡åææèä¸è½ä¸ºç©º
model_source.owner_type.not_blank=æ¨¡åææèç±»åä¸è½ä¸ºç©º
model_source.base_name.not_blank=æ¨¡ååºç¡åç§°ä¸è½ä¸ºç©º
model_source.app_key.not_blank=æ¨¡åKeyä¸è½ä¸ºç©º
model_source.api_url.not_blank=æ¨¡åå°åä¸è½ä¸ºç©º
