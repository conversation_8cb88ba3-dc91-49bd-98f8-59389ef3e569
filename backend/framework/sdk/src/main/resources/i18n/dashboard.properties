# dashboard
functional_case.reviewRate=è¯å®¡ç
functional_case.hasReview=å·²è¯å®¡
functional_case.unReview=æªè¯å®¡
functional_case.passRate=éè¿ç
functional_case.hasPass=å·²éè¿
functional_case.unPass=æªéè¿
functional_case.associateRate=å³èç
functional_case.hasAssociate=å·²å³è
functional_case.unAssociate=æªå³è
api_definition.completionRate=å®æç
api_management.execTime=æ§è¡æ¬¡æ°
api_management.execCount=å·²æ§è¡
api_management.unExecCount=æªæ§è¡
api_management.apiCaseCount=æ¥å£ç¨ä¾æ°
api_management.apiScenarioCount=åºæ¯ç¨ä¾æ°
api_management.fakeErrorCount=è¯¯æ¥æ°
api_management.apiCaseExecRate=ç¨ä¾æ§è¡ç
api_management.apiCasePassRate=ç¨ä¾éè¿ç
api_management.scenarioExecRate=åºæ¯æ§è¡ç
api_management.scenarioPassRate=åºæ¯éè¿ç
api_management.passCount=å·²éè¿
api_management.unPassCount=æªéè¿

bug_management.retentionRate=éçç
bug_management.totalCount=ç¼ºé·æ»æ°
bug_management.retentionCount=éçç¼ºé·æ°
get_platform_end_status_error=è·åå¹³å°ç»æç¶æå¤±è´¥

plan_executor=æªåé







