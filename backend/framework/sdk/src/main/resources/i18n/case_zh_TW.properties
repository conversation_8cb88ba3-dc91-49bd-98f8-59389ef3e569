# permission
permission.case_management.name=用例管理
permission.functional_case.name=功能用例
permission.functional_case.minder=腦圖編輯
permission.functional_case.comment=評論
permission.case_review.name=用例評審
permission.case_review.review=評審
permission.case_review.relevance=關聯/取消關聯
permission.case_review.start_time=週期評審開始時間不得早於目前時間
permission.case_review.end_time=評審週期結束時間不得早於目前時間

#module：FunctionalCase
functional_case.module.default.name=未規劃用例
functional_case.module.default.name.add_error=未規劃用例模組下不支援新增模組
functional_case.module.default.name.cut_error=未規劃用例模組不能被移動
all.module.default.name.cut_error=全部用例不能被移動
review.module.default.name=未規劃評審
functional_case.id.not_blank=ID不能為空
functional_case.num.not_blank=业务ID不能為空
functional_case.custom_num.length_range=自定義業務ID長度必須在1-64之間
functional_case.custom_num.not_blank=自定義業務ID不能為空
functional_case.module_id.length_range=模塊ID長度必須在1-50之間
functional_case.module_id.not_blank=模塊ID不能為空
functional_case.project_id.length_range=項目ID長度必須在1-50之間
functional_case.project_id.not_blank=項目ID不能為空
functional_case.template_id.not_blank=模板ID不能為空
functional_case.name.length_range=名稱長度必須在1-255之間
functional_case.name.not_blank=名稱不能為空
functional_case.pos.not_blank=順序不能為空
functional_case.review_status.length_range=評審狀態長度必須在1-64之間
functional_case.review_status.not_blank=評審狀態不能為空
functional_case.case_edit_type.length_range=編輯模式長度必須在1-64之間
functional_case.case_edit_type.not_blank=編輯模式不能為空
functional_case.version_id.length_range=版本ID長度必須在1-50之間
functional_case.version_id.not_blank=版本ID不能為空
functional_case.ref_id.length_range=指向初始版本ID必须在1-50之間
functional_case.ref_id.not_blank=指向初始版本ID不能為空
functional_case.last_execute_result.length_range=最近的執行結果長度必須在1-64之間
functional_case.last_execute_result.not_blank=最近的執行結果不能為空
functional_case.deleted.length_range=是否在回收站長度必須在1-1之間
functional_case.deleted.not_blank=是否在回收站不能為空
functional_case.public_case.length_range=是否是公共用例長度必須在1-1之間
functional_case.public_case.not_blank=是否是公共用例不能為空
functional_case.latest.length_range=是否为最新版本長度必須在1-1之間
functional_case.latest.not_blank=是否为最新版本不能為空
functional_case.create_user.length_range=創建人長度必須在1-100之間
functional_case.create_user.not_blank=創建人不能為空
functional_case.cover.not_blank=是否覆蓋原用例不能為空
functional_case.file_source.not_blank=用例文件來源不能為空
#module：FunctionalCaseBlob
functional_case_blob.functional_case_id.not_blank=功能用例ID不能為空
#module：FunctionalCaseComment
functional_case_comment.id.not_blank=ID不能為空
functional_case_comment.case_id.length_range=功能用例ID長度必須在1-50之間
functional_case_comment.case_id.not_blank=功能用例ID不能為空
functional_case_comment.create_user.length_range=評論人長度必須在1-50之間
functional_case_comment.create_user.not_blank=評論人不能為空
functional_case_comment.type.length_range=評論類型長度必須在1-64之間
functional_case_comment.type.not_blank=評論類型不能為空
functional_case_comment.content.not_blank=評論內容不能為空
functional_case_comment.event.not_blank=評論類型不能為空
#module：FunctionalCaseModule
functional_case_module.id.not_blank=ID不能為空
functional_case_module.project_id.length_range=項目ID長度必須在1-50之間
functional_case_module.project_id.not_blank=項目ID不能為空
functional_case_module.name.length_range=名稱長度必須在1-100之間
functional_case_module.name.not_blank=名稱不能為空
functional_case_module.pos.length_range=同一節點下的順序長度必須在1-10之間
functional_case_module.pos.not_blank=同一節點下的順序不能為空
functional_case_module.create_user.length_range=創建人長度必須在1-50之間
functional_case_module.create_user.not_blank=創建人不能為空
#module：FunctionalCaseAttachment
functional_case_attachment.case_id.not_blank=功能用例ID不能爲空
functional_case_attachment.case_id.length_range=功能用例ID長度必須在1-50之間
functional_case_attachment.file_id.not_blank=文件ID不能爲空
functional_case_attachment.file_id.length_range=文件ID長度必須在1-50之間
functional_case_attachment.id.not_blank=ID不能爲空
functional_case_attachment.id.length_range=ID長度必須在1-50之間
functional_case_attachment.file_name.not_blank=文件名不能爲空
functional_case_attachment.file_name.length_range=文件名長度必須在1-255之間
functional_case_attachment.size.not_blank=文件大小不能爲空
functional_case_attachment.association.not_blank=關聯類型不能爲空
#module：FunctionalCaseFollow
functional_case_follow.case_id.not_blank=功能用例ID不能爲空
functional_case_follow.follow_id.not_blank=關注人ID不能爲空
#module：FunctionalCaseRelationshipEdge
functional_case_relationship_edge.id.not_blank=ID不能為空
functional_case_relationship_edge.source_id.length_range=源節點的ID長度必須在1-50之间
functional_case_relationship_edge.source_id.not_blank=源節點的ID不能爲空
functional_case_relationship_edge.target_id.length_range=目標節點的ID長度必須在1-50之間
functional_case_relationship_edge.target_id.not_blank=目標節點的ID不能為空
functional_case_relationship_edge.graph_id.length_range=所屬關係圖的ID長度必須在1-50之間
functional_case_relationship_edge.graph_id.not_blank=所屬關係圖的ID不能為空
functional_case_relationship_edge.create_user.length_range=創建人長度必須在1-50之間
functional_case_relationship_edge.create_user.not_blank=創建人不能為空
#module：FunctionalCaseTest
functional_case_test.id.not_blank=ID不能為空
functional_case_test.functional_case_id.length_range=功能用例ID長度必須在1-50之間
functional_case_test.functional_case_id.not_blank=功能用例ID不能為空
functional_case_test.source_id.length_range=其他類型用例ID長度必須在1-50之間
functional_case_test.source_id.not_blank=其他類型用例ID不能為空
functional_case_test.source_type.length_range=用例類型長度必須在1-64之間
functional_case_test.source_type.not_blank=用例類型不能為空
functional_test_case.disassociate_type.not_blank=關聯的用例類型不能為空
#FunctionalCaseCustomField
functional_case_custom_field.case_id.not_blank=功能用例ID不能爲空
functional_case_custom_field.field_id.not_blank=自定義字段ID不能爲空
#FunctionalCaseDemand
functional_case_demand.demand_platform.not_blank=需求平台不能為空
functional_case_demand.demand_name.not_blank=需求標題不能為空
functional_case_demand.with_parent.not_blank=與父需求是否關聯不能為空
functional_case_demand.parent.not_blank=父需求ID不能為空
functional_case_demand.case_id.not_blank=功能用例ID不能為空
functional_case_demand.id.not_blank=需求ID不能為空
functional_case_demand.demand_name.length_range=需求標題長度必須在1-255之間
functional_case_demand.parent.length_range=父需求ID必須在1-255之間
functional_case_demand.case_id.length_range=功能用例ID必須在1-50之間
functional_case_demand.id.length_range=需求ID必須在1-50之間
#FunctionalCaseFollower
functional_case_follower.case_id.not_blank=功能用例ID不能為空
functional_case_follower.user_id.not_blank=關注人不能為空
#FunctionalMinderExtraNode
functional_minder_extra_node.id.not_blank=腦圖節點不能為空
functional_minder_extra_node.parent_id.not_blank=腦圖父節點不能為空
functional_minder_extra_node.group_id.not_blank=專案ID不能為空
functional_minder_extra_node.node_data.not_blank=儲存腦圖節點額外資訊不能為空
#module：MinderExtraNode
minder_extra_node.id.not_blank=ID不能為空
minder_extra_node.parent_id.length_range=父節點的ID長度必須在1-50之間
minder_extra_node.parent_id.not_blank=父節點的ID不能為空
minder_extra_node.group_id.length_range=項目ID長度必須在1-50之間
minder_extra_node.group_id.not_blank=項目ID不能為空
minder_extra_node.type.length_range=類型長度必須在1-30之間
minder_extra_node.type.not_blank=類型不能為空
minder_extra_node.case=用例
minder_extra_node.module=模塊
minder_extra_node.prerequisite=前置條件
minder_extra_node.steps=步驟描述
minder_extra_node.steps_expected_result=預期結果
minder_extra_node.steps_actual_result=實際結果
minder_extra_node.text_description=文字描述
minder_extra_node.text_expected_result=預期結果
minder_extra_node.description=備註資訊
minder_extra_node.text_node_empty=文字節點名稱不能為空
minder_extra_node.case_node_empty=用例名稱不能為空
mind_import_case_name_empty=部分用例名稱為空，校驗失敗；
#module：CaseReview
case_review.id.not_blank=ID不能為空
case_review.name.length_range=名稱長度必須在1-200之間
case_review.name.not_blank=名稱不能為空
case_review.reviewers.not_empty=預設評審人不能為空
case_review.module_id.not_blank=模組不能為空
case_review.copy_id.not_blank=用例評審被複製的ID不能為空
case_review.case_review_id.not_blank=用例評審ID不能為空
case_review.user_ids.not_empty=評審者不能為空
case_review_case.project_id.not_blank = 功能用例所屬項目ID不能為空白
case_review.move_mode.not_blank=節點移動類型不能為空
case_review.status.length_range=評審狀態長度必須在1-64之間
case_review.status.not_blank=評審狀態不能為空
case_review.project_id.length_range=項目ID長度必須在1-50之間
case_review.project_id.not_blank=項目ID不能為空
case_review.create_user.length_range=創建人長度必須在1-50之間
case_review.create_user.not_blank=創建人不能為空
case_review.review_pass_rule.length_range=評審規則長度必須在1-64之間
case_review.review_pass_rule.not_blank=評審規則不能為空
#module：CaseReviewModule
case_review_module.id.not_blank=ID不能為空
case_review_module.project_id.not_blank=項目ID不能為空
case_review_module.name.not_blank=名稱不能為空
case_review_module.parent_id.not_blank=父節點ID不能為空
case_review_module.pos.not_blank=同一節點下的順序不能為空
#module：CaseReviewUser
case_review_user.review_id.not_blank=評審ID不能為空
case_review_user.user_id.not_blank=評審人ID不能為空
#module：CaseReviewHistory
case_review_history.id.not_blank=ID不能為空
case_review_history.id.length_range=ID長度必須在1-50之間
case_review_history.review_id.not_blank=評審ID不能為空
case_review_history.review_id.length_range=評審ID長度必須在1-50之間
case_review_history.case_id.not_blank=功能用例ID不能為空
case_review_history.case_id.length_range=功能用例ID長度必須在1-50之間
case_review_history.status.not_blank=評審結果不能為空
case_review_history.status.length_range=評審結果長度必須在1-64之間
case_review_history.deleted.not_blank=是否是取消關聯或評審被刪除的不能為空
case_review_history.abandoned.not_blank=是否為廢棄的評審紀錄不能為空
#module：CaseReviewFunctionalCase
case_review_functional_case.id.not_blank=ID不能為空
case_review_functional_case.review_id.length_range=評審ID長度必須在1-50之間
case_review_functional_case.review_id.not_blank=評審ID不能為空
case_review_functional_case.case_id.length_range=用例ID長度必須在1-50之間
case_review_functional_case.case_id.not_blank=用例ID不能為空
case_review_functional_case.status.length_range=評審狀態長度必須在1-64之間
case_review_functional_case.status.not_blank=評審狀態不能為空
case_review_functional_case.create_user.length_range=創建人長度必須在1-50之間
case_review_functional_case.create_user.not_blank=創建人不能為空
case_review_functional_case.deleted.length_range=關聯的用例是否放入回收站長度必須在1-1之間
case_review_functional_case.deleted.not_blank=關聯的用例是否放入回收站不能為空
#module：CaseReviewFunctionalCaseUser
case_review_functional_case_user.case_id.length_range=功能用例和評審中間錶的ID長度必須在1-50之間
case_review_functional_case_user.case_id.not_blank=功能用例和評審中間錶的ID不能為空
case_review_functional_case_user.review_id.length_range=評審ID長度必須在1-50之間
case_review_functional_case_user.review_id.not_blank=評審ID不能為空
case_review_functional_case_user.user_id.length_range=評審人ID長度必須在1-50之間
case_review_functional_case_user.user_id.not_blank=評審人ID不能為空
#CaseReviewFunctionalCaseArchive
case_review_functional_case_archive.review_id.not_blank=評審ID不能為空
case_review_functional_case_archive.review_id.length_range=評審ID長度必須在1-50之間
case_review_functional_case_archive.case_id.not_blank=功能用例ID不能為空
case_review_functional_case_archive.case_id.length_range=功能用例ID長度必須在1-50之間
#module：CaseReviewFollower
case_review_follower.review_id.not_blank=評審ID不能為空
case_review_follower.follow_id.not_blank=關注人不能為空
#module：CustomFieldTestCase
custom_field_test_case.resource_id.not_blank=資源ID不能為空
custom_field_test_case.field_id.not_blank=字段ID不能為空
default_template_not_found=默認模板不存在
#comment
case_comment.case_is_null=功能用例不存在
case_comment.parent_id_is_null=目前回覆的評論id為空
case_comment.parent_case_is_null=目前回應的評論不存在
case_comment.reply_user_is_null=回覆的用戶為空
case_comment.id_is_null=目前評論id為空
case_comment.user_self=只能刪除自己的評論
un_follow_functional_case=取消關注用例
follow_functional_case=關注用例
tags_length_large_than=標籤數量不能超過{max}
#module
case_module.not.exist=用例模組不存在
file.transfer.failed=文件轉存失敗
#case
case.demand.not.exist=需求不存在
case.demand.name.not.exist=需求名稱不能為空
case.demand.id.not.exist=需求id不能為空]
case_review.prepared=未開始
case_review.underway=進行中
case_review.completed=已完成
case_review.archived=已歸檔
case_review.single=單人評審
case_review.multiple=多人評審
case_review.not.exist=用例評審不存在
case_review_content.not.exist = 評審意見不能為空
case_review_history.system=系統觸發
case_review.viewFlag.not_blank=查看標誌不能為空
functional_case_relationship_edge.type.not_blank=類型不能為空
cycle_relationship=關聯后存在循環依賴，請檢查依賴關係
case_review_user=您沒有評審權限
#minder
case.minder.all.case=全部用例

case.minder.status.success=成功
case.minder.status.error=失敗
case.minder.status.blocked=阻塞

case.review.status.un_reviewed=未評審
case.review.status.under_reviewed=評審中
case.review.status.pass=已通過
case.review.status.un_pass=不通過
case.review.status.re_reviewed=重新提審
case.execute.status.pending=未執行
case.execute.status.success=成功
case.execute.status.error=失敗
case.execute.status.blocked=阻塞
functional_case_comment_template=【用例評論：%s（%s）】\n%s\n
functional_case_execute_comment_template=【執行評論：%s %s（%s）】\n%s\n
functional_case_review_comment_template=【評審評論：%s %s（%s）】\n%s\n
functional_case_xmind_template=思維導圖用例模板
download_template_failed=下載思維導圖模板失敗
functional_case=功能用例
xmind_prerequisite=前置條件
xmind_description=備注
xmind_tags=標簽
xmind_textDescription=文本描述
xmind_expectedResult=預期結果
xmind_step=用例步驟
xmind_stepDescription=步驟描述

#import
case.find_file_error=找不到該文件


# case export columns
case.export.system.columns.name=用例名稱
case.export.system.columns.id=ID
case.export.system.columns.prerequisite=前置條件
case.export.system.columns.module=所屬模塊
case.export.system.columns.text_description=步驟描述
case.export.system.columns.expected_result=預期結果
case.export.system.other.columns.last_execute_result=執行結果
case.export.system.other.columns.review_status=評審結果
case.export.system.other.columns.create_user=創建人
case.export.system.other.columns.create_time=創建時間
case.export.system.other.columns.update_user=更新人
case.export.system.other.columns.update_time=更新時間
case.export.columns.case_edit_type=編輯模式
case.export.system.other.columns.case_comment=用例評論
case.export.system.other.columns.execute_comment=執行評論
case.export.system.other.columns.review_comment=評審評論

export_case_task_stop=停止導出
export_case_task_existed=已有導出任務

demand.sync.job=定時同步需求