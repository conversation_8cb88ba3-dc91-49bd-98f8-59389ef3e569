# bug
bug.id.not_blank=ID不能為空
bug.id.length_range=ID長度必須在1-50之間
bug.title.not_blank=缺陷标题不能為空
bug.title.length_range=缺陷标题長度必須在1-255之間
bug.assign_user.not_blank=指派人不能為空
bug.assign_user.length_range=指派人長度必須在1-50之間
bug.create_user.not_blank=创建人不能為空
bug.create_user.length_range=创建人長度必須在1-50之間
bug.update_user.not_blank=更新人不能為空
bug.update_user.length_range=更新人長度必須在1-50之間
bug.delete_user.not_blank=删除人不能為空
bug.delete_user.length_range=删除人長度必須在1-50之間
bug.project_id.not_blank=项目ID不能為空
bug.project_id.length_range=项目ID長度必須在1-50之間
bug.template_id.not_blank=模板ID不能為空
bug.template_id.length_range=模板ID長度必須在1-50之間
bug.platform_id.not_blank=平台缺陷ID不能為空
bug.platform_id.length_range=平台缺陷ID長度必須在1-50之間
bug.platform.not_blank=缺陷平台不能為空
bug.platform.length_range=缺陷平台長度必須在1-50之間
bug.status.not_blank=平台状态不能為空
bug.status.length_range=平台状态長度必須在1-50之間
bug.deleted.not_blank=删除状态不能為空
bug.deleted.length_range=删除状态長度必須在1-50之間

# bugContent
bug_content.bug_id.not_blank=缺陷ID不能為空
bug_content.bug_id.length_range=缺陷ID長度必須在1-50之間

# bugFollower
bug_follower.bug_id.not_blank=缺陷ID不能為空
bug_follower.bug_id.length_range=缺陷ID長度必須在1-50之間
bug_follower.user_id.not_blank=关注人ID不能為空
bug_follower.user_id.length_range=关注人ID長度必須在1-50之間

# bugComment
bug_comment.id.not_blank=ID不能為空
bug_comment.id.length_range=ID長度必須在1-50之間
bug_comment.bug_id.not_blank=缺陷ID不能為空
bug_comment.bug_id.length_range=缺陷ID長度必須在1-50之間
bug_comment.create_user.not_blank=评论人不能為空
bug_comment.create_user.length_range=评论人長度必須在1-50之間
bug_comment.update_user.not_blank=更新人不能為空
bug_comment.update_user.length_range=更新人長度必須在1-50之間

# bugLocalAttachment
bug_local_attachment.id.not_blank=ID不能為空
bug_local_attachment.id.length_range=ID長度必須在1-50之間
bug_local_attachment.bug_id.not_blank=缺陷ID不能為空
bug_local_attachment.bug_id.length_range=缺陷ID長度必須在1-50之間
bug_local_attachment.file_id.not_blank=文件ID不能為空
bug_local_attachment.file_id.length_range=文件ID長度必須在1-50之間
bug_local_attachment.file_name.not_blank=文件名称不能為空
bug_local_attachment.file_name.length_range=文件名称長度必須在1-50之間
bug_local_attachment.source.not_blank=文件来源不能為空
bug_local_attachment.source.length_range=文件来源長度必須在1-50之間
bug_local_attachment.create_user.not_blank=创建人不能為空
bug_local_attachment.create_user.length_range=创建人長度必須在1-50之間

# bugCustomField
bug_custom_field.bug_id.not_blank=缺陷ID不能為空
bug_custom_field.bug_id.length_range=缺陷ID長度必須在1-50之間
bug_custom_field.field_id.not_blank=字段ID不能為空
bug_custom_field.field_id.length_range=字段ID長度必須在1-50之間

# bugRelationCase
bug_relation_case.id.not_blank=ID不能為空
bug_relation_case.id.length_range=ID長度必須在1-50之間
bug_relation_case.case_id.not_blank=关联功能用例ID不能為空
bug_relation_case.case_id.length_range=关联功能用例ID長度必須在1-50之間
bug_relation_case.bug_id.not_blank=缺陷ID不能為空
bug_relation_case.bug_id.length_range=缺陷ID長度必須在1-50之間
bug_relation_case.case_type.not_blank=关联的用例类型不能為空
bug_relation_case.case_type.length_range=关联的用例类型長度必須在1-50之間
bug_relation_case.create_user.not_blank=创建人不能為空
bug_relation_case.create_user.length_range=创建人長度必須在1-50之間

# error
bug_not_exist=缺陷不存在
not_local_bug_error=非本地缺陷，無法操作
third_party_not_config=請正確配置服務集成或項目應用設置的參數, 並啟用;
bug_tags_size_large_than=缺陷标签数量超过{0}个
bug_attachment_upload_error=缺陷附件上傳失敗
bug_attachment_link_error=缺陷附件關聯失敗
bug_attachment_delete_error=缺陷附件刪除失敗
no_bug_select=未勾選缺陷
bug_select_not_found=未查詢到勾選的缺陷
bug_comment.event.not_blank=評論事件類型不能為空
bug_comment.parent_id.not_blank=缺陷評論父級ID不能為空
bug_comment.parent.not_exist=父級評論不存在
bug_comment.reply_user.not_blank=缺陷回復人不能為空
bug_comment_not_exist=缺陷評論不存在
bug_comment_not_owner=非當前評論創建人, 無法操作!
bug_relate_case_not_found=未查詢到關聯的用例
bug_relate_case_type_unknown=關聯的用例類型未知, 無法查看
unknown_case_type_of_relate_case=參數錯誤, 未知的用例類型
bug_relate_case_permission_error=無權限查看, 請聯繫管理員
bug_status_can_not_be_empty=缺陷狀態不能為空
handle_user_can_not_be_empty=缺陷處理人不能為空
bug.title=缺項名稱
bug.handle_user=處理人
bug.status=狀態
bug.tag=標籤
bug.description=缺陷內容

# bug export
bug.system_columns.not_empty=系統字段不能為空
bug.export.system.columns.name=缺陷名稱
bug.export.system.columns.id=ID
bug.export.system.columns.content=缺陷內容
bug.export.system.columns.status=缺陷狀態
bug.export.system.columns.handle_user=處理人
bug.export.system.other.columns.create_user=創建人
bug.export.system.other.columns.create_time=創建時間
bug.export.system.other.columns.case_count=用例數
bug.export.system.other.columns.comment=評論
bug.export.system.other.columns.platform=所屬平台
permission.bug.name=缺陷管理
test_plan_related=測試計劃關聯
direct_related=直接關聯
permission.bug=缺陷

# sync mode
sync_mode.manual=手動同步
sync_mode.auto=定時同步

bug.sync.job=定時同步缺陷