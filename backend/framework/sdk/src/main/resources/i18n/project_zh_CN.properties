fake_error.id.not_blank=IDä¸è½ä¸ºç©º
file.module.default.name=æªè§åæä»¶
fake_error.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
fake_error.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
fake_error.name.length_range=è¯¯æ¥åç§°é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
fake_error.name.not_blank=è¯¯æ¥åç§°ä¸è½ä¸ºç©º
fake_error.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
fake_error.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
fake_error.update_user.length_range=æ´æ°äººé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
fake_error.update_user.not_blank=æ´æ°äººä¸è½ä¸ºç©º
fake_error.type.not_blank=ç±»åä¸è½ä¸ºç©º
fake_error.type.length_range=ç±»åé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
fake_error.resp_type.not_blank=ååºç±»åä¸è½ä¸ºç©º
fake_error.relation.not_blank=æä½ç±»åä¸è½ä¸ºç©º
fake_error.relation.length_range=æä½ç±»åé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
fake_error.expression.not_blank=è¡¨è¾¾å¼ä¸è½ä¸ºç©º
fake_error.expression.length_range=è¡¨è¾¾å¼é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
fake_error.enable.not_blank=æ¯å¦å¯ç¨ä¸è½ä¸ºç©º
fake_error.enable.length_range=æ¯å¦å¯ç¨é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
fake_error.error_code.length_range=éè¯¯ç é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
fake_error.error_code.not_blank=éè¯¯ç ä¸è½ä¸ºç©º
fake_error.match_type.length_range=å¹éç±»åé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
fake_error.match_type.not_blank=å¹éç±»åä¸è½ä¸ºç©º
bug_template_extend.id.not_blank=IDä¸è½ä¸ºç©º
project_application.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
project_application.type.not_blank=ç±»åä¸è½ä¸ºç©º
project_application.type_value.not_blank=ç±»åå¼ä¸è½ä¸ºç©º
custom_function_blob.id.not_blank=IDä¸è½ä¸ºç©º
fake_error_blob.id.not_blank=IDä¸è½ä¸ºç©º
file_module.id.not_blank=IDä¸è½ä¸ºç©º
file_module.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
file_module.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
file_module.name.length_range=åç§°é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
file_module.name.not_blank=åç§°ä¸è½ä¸ºç©º
file.association.error.type=ä¸æ¯æçæä»¶å³èèµæºç±»å
file.association.not.exist=æä»¶å¹¶æªå³è
file.association.source.not.exist=æä»¶å³èæ¶èµæºä¸å­å¨
file_repository.connect.error=å­å¨åºè¿æ¥å¤±è´¥
file_repository.not.exist=å­å¨åºä¸å­å¨
file_repository.platform.error=å­å¨åºç±»åä¸æ­£ç¡®
file_repository.id.not_blank=å­å¨åºIDä¸è½ä¸ºç©º
file_repository.name.not_blank=å­å¨åºåç§°ä¸è½ä¸ºç©º
file_repository.type.not_blank=å­å¨åºç±»åä¸è½ä¸ºç©º
file_repository.token.not_blank=å­å¨åºtokenä¸è½ä¸ºç©º
file_repository.url.not_blank=å­å¨åºå°åä¸è½ä¸ºç©º
file_repository.branch.not_blank=å­å¨åºåæ¯ä¸è½ä¸ºç©º
file_repository.file_path.not_blank=å­å¨åºæä»¶è·¯å¾ä¸è½ä¸ºç©º
custom_field_template.id.not_blank=IDä¸è½ä¸ºç©º
custom_field_template.field_id.length_range=å­æ®µIDé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
custom_field_template.field_id.not_blank=å­æ®µIDä¸è½ä¸ºç©º
custom_field_template.template_id.length_range=æ¨¡æ¿IDé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
custom_field_template.template_id.not_blank=æ¨¡æ¿IDä¸è½ä¸ºç©º
custom_field_template.scene.length_range=åºæ¯é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
custom_field_template.scene.not_blank=åºæ¯ä¸è½ä¸ºç©º
file_metadata_blob.id.not_blank=IDä¸è½ä¸ºç©º
bug_template.id.not_blank=IDä¸è½ä¸ºç©º
bug_template.name.length_range=åç§°é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
bug_template.name.not_blank=åç§°ä¸è½ä¸ºç©º
bug_template.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
bug_template.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
bug_template.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
bug_template.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
functional_case_template.id.not_blank=IDä¸è½ä¸ºç©º
functional_case_template.name.length_range=åç§°é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
functional_case_template.name.not_blank=åç§°ä¸è½ä¸ºç©º
functional_case_template.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
functional_case_template.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
functional_case_template.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
functional_case_template.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
api_template.id.not_blank=IDä¸è½ä¸ºç©º
api_template.name.length_range=åç§°é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
api_template.name.not_blank=åç§°ä¸è½ä¸ºç©º
api_template.system.length_range=æ¯å¦æ¯ç³»ç»å­æ®µé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
api_template.system.not_blank=æ¯å¦æ¯ç³»ç»å­æ®µä¸è½ä¸ºç©º
api_template.global.length_range=æ¯å¦æ¯å¨å±å­æ®µé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
api_template.global.not_blank=æ¯å¦æ¯å¨å±å­æ®µä¸è½ä¸ºç©º
project_extend.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
project_extend.platform.length_range=å¹³å°é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
project_extend.platform.not_blank=å¹³å°ä¸è½ä¸ºç©º
project.id.not_blank=IDä¸è½ä¸ºç©º
project.module_menu.check.error=è¯¥é¡¹ç®çæ¨¡åèåæ£æ¥å¤±è´¥
project.organization_id.length_range=ç»ç»IDé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
project.organization_id.not_blank=ç»ç»IDä¸è½ä¸ºç©º
project.name.length_range=åç§°é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
project.name.not_blank=åç§°ä¸è½ä¸ºç©º
custom_function.id.not_blank=IDä¸è½ä¸ºç©º
custom_function.name.length_range=åç§°é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
custom_function.name.not_blank=åç§°ä¸è½ä¸ºç©º
custom_function.type.length_range=ç±»åé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
custom_function.type.not_blank=ç±»åä¸è½ä¸ºç©º
custom_function.status.length_range=èæ¬ç¶æé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
custom_function.status.not_blank=èæ¬ç¶æä¸è½ä¸ºç©º
custom_function.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
custom_function.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
custom_function.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
custom_function.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
custom_field.id.not_blank=IDä¸è½ä¸ºç©º
custom_field.name.length_range=åç§°é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
custom_field.name.not_blank=åç§°ä¸è½ä¸ºç©º
custom_field.scene.length_range=åºæ¯é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
custom_field.scene.not_blank=åºæ¯ä¸è½ä¸ºç©º
custom_field.type.length_range=ç±»åé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
custom_field.type.not_blank=ç±»åä¸è½ä¸ºç©º
custom_field.remark.length_range=æè¿°é¿åº¦å¿é¡»å¨{max}ä»¥å
file_module_blob.file_module_id.not_blank=æä»¶æ¨¡åIDä¸è½ä¸ºç©º
project_version.id.not_blank=IDä¸è½ä¸ºç©º
project_version.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
project_version.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
project_version.name.length_range=åç§°é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
project_version.name.not_blank=åç§°ä¸è½ä¸ºç©º
project_version.latest.length_range=ææ°çæ¬é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
project_version.latest.not_blank=ææ°çæ¬ä¸è½ä¸ºç©º
project_version.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
project_version.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
file_metadata.id.not_blank=IDä¸è½ä¸ºç©º
file_metadata.name.length_range=åç§°é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
file_metadata.name.not_blank=åç§°ä¸è½ä¸ºç©º
file_metadata.storage.length_range=å­å¨é¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
file_metadata.storage.not_blank=å­å¨ä¸è½ä¸ºç©º
functional_case_template_extend.id.not_blank=IDä¸è½ä¸ºç©º
functional_case_template_extend.step_model.length_range=æ­¥éª¤æ¨¡åé¿åº¦å¿é¡»å¨{min}-{max}ä¹é´
functional_case_template_extend.step_model.not_blank=æ­¥éª¤æ¨¡åä¸è½ä¸ºç©º
project_not_exist=é¡¹ç®ä¸å­å¨
fake_error_name_exist=è¯¯æ¥åç§°å·²å­å¨
module.name.is.empty=æ¨¡ååæ°ä¸ºç©º
module.name.is.error=æ¨¡ååæ°ä¸æ­£ç¡®
#æ¶æ¯ç®¡ç
save_message_task_user_no_exist=æéç¨æ·é¨åä¸å­å¨
# robot
robot_in_site=ç«åä¿¡
robot_in_site_description=ç³»ç»åç½®ï¼å¨é¡¶é¨å¯¼èªæ æ¾ç¤ºæ¶æ¯éç¥
robot_mail=é®ä»¶
robot_mail_description=ç³»ç»åç½®ï¼ä»¥æ·»å ç¨æ·é®ç®±ä¸ºéç¥æ¹å¼
robot_is_null=å½åæºå¨äººä¸å­å¨
ding_type_is_null=ééæºå¨äººçç±»åä¸è½ä¸ºç©º
ding_app_key_is_null=ééçAppKeyä¸è½ä¸ºç©º
ding_app_secret_is_null=ééçAppSecretä¸è½ä¸ºç©º
# permission
permission.project_user.name=ç¨æ·
permission.project_user.invite=éè¯·ç¨æ·
permission.project_group.name=ç¨æ·ç»
permission.project_environment.name=ç¯å¢ç®¡ç
permission.project_file.name=æä»¶ç®¡ç
permission.project_file.download=æä»¶ä¸è½½
permission.project_template.name=æ¨¡çç®¡ç
permission.project_message.name=æ¶æ¯ç®¡ç
permission.project_version.name=çæ¬ç®¡ç
permission.project_fake_error.name=è¯¯æ¥åº
permission.project_application.name=åºç¨è®¾ç½®
permission.project_application_test_plan.read=æµè¯è®¡å-æ¥è¯¢
permission.project_application_test_plan.update=æµè¯è®¡å-ç¼è¾
permission.project_application_ui.read=UIæµè¯-æ¥è¯¢
permission.project_application_ui.update=UIæµè¯-ç¼è¾
permission.project_application_performance_test.read=æ§è½æµè¯-æ¥è¯¢
permission.project_application_performance_test.update=æ§è½æµè¯-ç¼è¾
permission.project_application_api.read=æ¥å£æµè¯-æ¥è¯¢
permission.project_application_api.update=æ¥å£æµè¯-ç¼è¾
permission.project_application_case.read=ç¨ä¾ç®¡ç-æ¥è¯¢
permission.project_application_case.update=ç¨ä¾ç®¡ç-ç¼è¾
permission.project_application_bug.read=ç¼ºé·ç®¡ç-æ¥è¯¢
permission.project_application_bug.update=ç¼ºé·ç®¡ç-ç¼è¾
permission.project_application_task.read=ä»»å¡ä¸­å¿-æ¥è¯¢
permission.project_application_task.update=ä»»å¡ä¸­å¿-ç¼è¾
permission.project_application_workstation.read=å·¥ä½å°-æ¥è¯¢
permission.project_application_workstation.update=å·¥ä½å°-ç¼è¾
permission.project_base_info.name=åºæ¬ä¿¡æ¯
permission.project_log.name=æ¥å¿
# project version
project.version.exist=çæ¬å·²å­å¨
project.version.not_exist=çæ¬ä¸å­å¨
project.version.resource_exist=çæ¬å·ä¸å­å¨ä¸å¡æ°æ®ï¼è¯·åå é¤è¯¥çæ¬çä¸å¡æ°æ®
project.version.only=åªæä¸ä¸ªçæ¬å·æ¶å¯å³é­
project.version.latest.no_delete=ææ°çæ¬ä¸å¯å é¤
#environment datasource
environment_datasource.id.not_blank=IDä¸è½ä¸ºç©º
environment_datasource.driver.not_blank=é©±å¨ä¸è½ä¸ºç©º
environment_datasource.driverId.not_blank=é©±å¨IDä¸è½ä¸ºç©º
environment_datasource.dbUrl.not_blank=æ°æ®åºè¿æ¥ä¸è½ä¸ºç©º
environment_name_is_null=ç¯å¢åç§°ä¸è½ä¸ºç©º
environment_config_is_null=ç¯å¢éç½®ä¸è½ä¸ºç©º
# message
message.test_plan_task=æµè¯è®¡å
message.schedule_task=å®æ¶ä»»å¡
message.report_task=æ¥å
message.bug_task=ç¼ºé·
message.bug_sync_task=åæ­¥
message.functional_case_task=åè½ç¨ä¾
message.case_review_task=ç¨ä¾è¯å®¡
message.api_definition_task=å®ä¹
message.api_scenario_task=åºæ¯
message.ui_scenario_task=UIèªå¨å
message.load_test_task=æµè¯ç¨ä¾
message.jenkins_task=æ§è¡
message.batch_execution=æ¹éæ§è¡
message.manual_execution=æå¨æ§è¡
message.test_plan_management=æµè¯è®¡å
message.bug_management=ç¼ºé·ç®¡ç
message.case_management=æµè¯ç¨ä¾
message.api_test_management=æ¥å£æµè¯
message.ui_test_management=UIæµè¯
message.load_test_management=æ§è½æµè¯
message.jenkins_task_management=Jenkins
message.schedule_task_management=å®æ¶ä»»å¡
message.create=åå»º
message.update=æ´æ°
message.delete=å é¤
message.execute_completed=æ§è¡å®æ
message.comment=è¯è®º
message.at=è¢«@
message.replay=è¢«åå¤
message.review_passed=è¯å®¡éè¿
message.review_fail=è¯å®¡ä¸éè¿
message.review_at=Reviewed by @
message.review_completed=è¯å®¡å®æ
message.case_create=CASE åå»º
message.case_update=CASE æ´æ°
message.case_delete=CASE å é¤
message.case_execute_successful=CASE æ§è¡æå
message.case_execute_fake_error=CASE æ§è¡è¯¯æ¥
message.case_execute_failed=CASE æ§è¡å¤±è´¥
message.mock_create=MOCK åå»º
message.mock_update=MOCK æ´æ°
message.mock_delete=MOCK å é¤
message.scenario_execute_successful=æ§è¡æå
message.scenario_execute_fake_error=æ§è¡è¯¯æ¥
message.scenario_execute_failed=æ§è¡å¤±è´¥
message.execute_successful=æ§è¡æå
message.execute_failed=æ§è¡å¤±è´¥
message.execute_passed=æ§è¡éè¿
message.execute_fail=æ§è¡ä¸éè¿
message.execute_at=æ§è¡è¢«@
message.open=å¼å¯
message.close=å³é­
message.assign=åé
message.sync_completed=åæ­¥å®æ
message.create_user=åå»ºäºº
message.follow_people=å³æ³¨äºº
message.operator=æä½äºº
message.handle_user=å¤çäºº (ç¬¬ä¸æ¹å¹³å°çå¤çäºº, ä¸ä¼æ¥æ¶å°éç¥)
message.trigger_mode=è§¦åæ¹å¼
message.jenkins_name=åç§°
message.custom_field=èªå®ä¹å­æ®µ
message.case_field=ç¨ä¾å­æ®µ
message.report_field=æ¥åå­æ®µ
message.test_plan_task_create=${OPERATOR}åå»ºäºæµè¯è®¡å:${name}
message.test_plan_task_update=${OPERATOR}æ´æ°äºæµè¯è®¡å:${name}
message.test_plan_task_delete=${OPERATOR}å é¤äºæµè¯è®¡å:${name}
message.test_plan_task_execute=${OPERATOR}æ§è¡äºæµè¯è®¡å:${name}
message.test_plan_report_task_delete=${OPERATOR}å é¤äºæµè¯è®¡åæ¥å:${name}
message.bug_task_create=${OPERATOR}åå»ºäºç¼ºé·:${title}
message.bug_task_update=${OPERATOR}æ´æ°äºç¼ºé·:${title}
message.bug_task_delete=${OPERATOR}å é¤äºç¼ºé·:${title}
message.bug_task_comment=${OPERATOR}è¯è®ºäºä½ çç¼ºé·:${title}
message.bug_task_at_comment=${OPERATOR}è¯è®ºäºç¼ºé·:${title} å¹¶@äºä½ 
message.bug_task_reply_comment=${OPERATOR}å¨ç¼ºé· ${title} åå¤äºä½ çè¯è®º
message.bug_sync_task_execute_completed=${OPERATOR}åæ­¥äº${total}æ¡ç¼ºé·
message.bug_task_assign=${OPERATOR}ç»ä½ åéäºä¸ä¸ªç¼ºé·: ${title}
message.functional_case_task_create=${OPERATOR}åå»ºäºåè½ç¨ä¾:${name}
message.functional_case_task_update=${OPERATOR}æ´æ°äºåè½ç¨ä¾:${name}
message.functional_case_task_delete=${OPERATOR}å é¤äºåè½ç¨ä¾:${name}
message.functional_case_task_comment=${OPERATOR}è¯è®ºäºä½ çåè½ç¨ä¾:${name}
message.functional_case_task_review=${OPERATOR}è¯å®¡äº${reviewName}${name}
message.functional_case_task_review_at=${OPERATOR}å¨${reviewName}${name}@äºä½ 
message.functional_case_task_plan=${OPERATOR}æ§è¡äº${testPlanName}${name}
message.functional_case_task_plan_at=${OPERATOR}å¨${testPlanName}${name}@äºä½ 


message.functional_case_task_at_comment=${OPERATOR}è¯è®ºäºåè½ç¨ä¾:${name} å¹¶@äºä½ 
message.functional_case_task_reply_comment=${OPERATOR}å¨ç¨ä¾ ${name} åå¤äºä½ çè¯è®º
message.case_review_task_create=${OPERATOR}åå»ºäºç¨ä¾è¯å®¡:${name}
message.case_review_task_update=${OPERATOR}æ´æ°äºç¨ä¾è¯å®¡:${name}
message.case_review_task_delete=${OPERATOR}å é¤äºç¨ä¾è¯å®¡:${name}
message.case_review_task_review_completed=${OPERATOR}å®æäºç¨ä¾è¯å®¡:${name}
message.api_definition_task_create=${OPERATOR}åå»ºäºæ¥å£å®ä¹:${name}
message.api_definition_task_update=${OPERATOR}æ´æ°äºæ¥å£å®ä¹:${name}
message.api_definition_task_delete=${OPERATOR}å é¤äºæ¥å£å®ä¹:${name}
message.api_definition_task_case_create=${OPERATOR}åå»ºäºæ¥å£ç¨ä¾:${name}
message.api_definition_task_case_update=${OPERATOR}æ´æ°äºæ¥å£ç¨ä¾:${name}
message.api_definition_task_case_delete=${OPERATOR}å é¤äºæ¥å£ç¨ä¾:${name}
message.api_definition_task_case_execute=${OPERATOR}æ§è¡äºæ¥å£ç¨ä¾:${name}
message.api_definition_task_mock_create=${OPERATOR}åå»ºäºæ¥å£MOCK:${name}
message.api_definition_task_mock_update=${OPERATOR}æ´æ°äºæ¥å£MOCK:${name}
message.api_definition_task_mock_delete=${OPERATOR}å é¤äºæ¥å£MOCK:${name}
message.api_scenario_task_create=${OPERATOR}åå»ºäºæ¥å£åºæ¯:${name}
message.api_scenario_task_update=${OPERATOR}æ´æ°äºæ¥å£åºæ¯:${name}
message.api_scenario_task_delete=${OPERATOR}å é¤äºæ¥å£åºæ¯:${name}
message.api_scenario_task_scenario_execute=${OPERATOR}æ§è¡äºæ¥å£åºæ¯:${name}
message.api_report_task_delete=${OPERATOR}å é¤äºæ¥å£æ¥å:${name}
message.ui_scenario_task_create=${OPERATOR}åå»ºäºUIç¨ä¾:${name}
message.ui_scenario_task_update=${OPERATOR}æ´æ°äºUIç¨ä¾:${name}
message.ui_scenario_task_delete=${OPERATOR}å é¤äºUIç¨ä¾:${name}
message.ui_scenario_task_execute=${OPERATOR}æ§è¡äºUIç¨ä¾:${name}
message.ui_report_task_delete=${OPERATOR}å é¤äºUIæ¥å:${name}
message.load_test_task_create=${OPERATOR}åå»ºäºæ§è½ç¨ä¾:${name}
message.load_test_task_update=${OPERATOR}æ´æ°äºæ§è½ç¨ä¾:${name}
message.load_test_task_delete=${OPERATOR}å é¤äºæ§è½ç¨ä¾:${name}
message.load_test_task_execute_completed=${OPERATOR}æ§è¡äºæ§è½ç¨ä¾:${name}
message.load_report_task_delete=${OPERATOR}å é¤äºæ§è½æ¥å:${name}
message.jenkins_task_execute=Jenkinsæ§è¡äº:${name}
message.schedule_task_open=${OPERATOR}å¼å¯äºå®æ¶ä»»å¡:${name}
message.schedule_task_close=${OPERATOR}å³é­äºå®æ¶ä»»å¡:${name}

message.title.test_plan_task_create=æµè¯è®¡ååå»ºéç¥
message.title.test_plan_task_update=æµè¯è®¡åæ´æ°éç¥
message.title.test_plan_task_delete=æµè¯è®¡åå é¤éç¥
message.title.test_plan_task_execute_successful=æµè¯è®¡åæ§è¡æåéç¥
message.title.test_plan_task_execute_failed=æµè¯è®¡åæ§è¡å¤±è´¥éç¥
message.title.test_plan_report_task_delete=æµè¯è®¡åæ¥åå é¤éç¥
message.title.bug_task_create=ç¼ºé·åå»ºéç¥
message.title.bug_task_update=ç¼ºé·æ´æ°éç¥
message.title.bug_task_delete=ç¼ºé·å é¤éç¥
message.title.bug_task_comment=ç¼ºé·è¯è®ºéç¥
message.title.bug_sync_task_execute_completed=åæ­¥ç¼ºé·æ§è¡å®æéç¥
message.title.bug_task_assign=ç¼ºé·åééç¥
message.title.functional_case_task_create=åè½ç¨ä¾åå»ºéç¥
message.title.functional_case_task_update=åè½ç¨ä¾æ´æ°éç¥
message.title.functional_case_task_delete=åè½ç¨ä¾å é¤éç¥
message.title.functional_case_task_comment=åè½ç¨ä¾è¯è®ºéç¥
message.title.functional_case_task_review_passed=ç¨ä¾è¯å®¡éè¿éç¥
message.title.functional_case_task_review_fail=ç¨ä¾è¯å®¡ä¸éè¿éç¥
message.title.functional_case_task_review_at=ç¨ä¾è¯å®¡éç¥
message.title.functional_case_task_execute_passed=ç¨ä¾æ§è¡éè¿éç¥
message.title.functional_case_task_execute_fail=ç¨ä¾æ§è¡ä¸éè¿éç¥
message.title.functional_case_task_execute_at=ç¨ä¾æ§è¡éç¥
message.title.case_review_task_create=ç¨ä¾è¯å®¡åå»ºéç¥
message.title.case_review_task_update=ç¨ä¾è¯å®¡æ´æ°éç¥
message.title.case_review_task_delete=ç¨ä¾è¯å®¡å é¤éç¥
message.title.case_review_task_review_completed=ç¨ä¾è¯å®¡è¯å®¡å®æéç¥
message.title.api_definition_task_create=æ¥å£å®ä¹åå»ºéç¥
message.title.api_definition_task_update=æ¥å£å®ä¹æ´æ°éç¥
message.title.api_definition_task_delete=æ¥å£å®ä¹å é¤éç¥
message.title.api_definition_task_case_create=æ¥å£ç¨ä¾åå»ºéç¥
message.title.api_definition_task_case_update=æ¥å£ç¨ä¾æ´æ°éç¥
message.title.api_definition_task_case_delete=æ¥å£ç¨ä¾å é¤éç¥
message.title.api_definition_task_case_execute_successful=æ¥å£ç¨ä¾æ§è¡æåéç¥
message.title.api_definition_task_case_execute_fake_error=æ¥å£ç¨ä¾æ§è¡è¯¯æ¥éç¥
message.title.api_definition_task_case_execute_failed=æ¥å£ç¨ä¾æ§è¡å¤±è´¥éç¥
message.title.api_definition_task_mock_create=MOCKåå»ºéç¥
message.title.api_definition_task_mock_update=MOCKæ´æ°éç¥
message.title.api_definition_task_mock_delete=MOCKå é¤éç¥
message.title.api_scenario_task_create=æ¥å£åºæ¯åå»ºéç¥
message.title.api_scenario_task_update=æ¥å£åºæ¯æ´æ°éç¥
message.title.api_scenario_task_delete=æ¥å£åºæ¯å é¤éç¥
message.title.api_scenario_task_scenario_execute_successful=æ¥å£åºæ¯æ§è¡æåéç¥
message.title.api_scenario_task_scenario_execute_fake_error=æ¥å£åºæ¯æ§è¯¯æ¥éç¥
message.title.api_scenario_task_scenario_execute_failed=æ¥å£åºæ¯æ§è¡å¤±è´¥éç¥
message.title.api_report_task_delete=æ¥å£æ¥åå é¤éç¥
message.title.ui_scenario_task_create=UIç¨ä¾åå»ºéç¥
message.title.ui_scenario_task_update=UIç¨ä¾æ´æ°éç¥
message.title.ui_scenario_task_delete=UIç¨ä¾å é¤éç¥
message.title.ui_scenario_task_execute_successful=UIç¨ä¾æ§è¡æåéç¥
message.title.ui_scenario_task_execute_failed=UIç¨ä¾æ§è¡å¤±è´¥éç¥
message.title.ui_report_task_delete=UIæ¥åå é¤éç¥
message.title.load_test_task_create=æ§è½ç¨ä¾åå»ºéç¥
message.title.load_test_task_update=æ§è½ç¨ä¾æ´æ°éç¥
message.title.load_test_task_delete=æ§è½ç¨ä¾å é¤éç¥
message.title.load_test_task_execute_completed=æ§è½ç¨ä¾æ§è¡å®æéç¥
message.title.load_report_task_delete=æ§è½æ¥åå é¤éç¥
message.title.jenkins_task_execute_successful=Jenkinsä»»å¡æ§è¡æåéç¥
message.title.jenkins_task_execute_failed=Jenkinsä»»å¡æ§è¡å¤±è´¥éç¥
message.title.schedule_task_open=å¼å¯å®æ¶ä»»å¡éç¥
message.title.schedule_task_close=å³é­å®æ¶ä»»å¡éç¥

#åè½ç¨ä¾
message.domain.name=åç§°
message.domain.testPlanName=æµè¯è®¡ååç§°
message.domain.reviewName=è¯å®¡åç§°
message.domain.reviewStatus=è¯å®¡ç¶æ
message.domain.caseModel=ç¼è¾æ¨¡å¼
message.domain.lastExecuteResult=æè¿çæ§è¡ç»æ
message.domain.createUser=åå»ºäºº
message.domain.updateUser=æ´æ°äºº
message.domain.deleteUser=å é¤äºº
message.domain.createTime=åå»ºæ¶é´
message.domain.updateTime=æ´æ°æ¶é´
message.domain.deleteTime=å é¤æ¶é´
message.domain.triggerMode=è§¦åæ¹å¼
#æ¥å£å®ä¹åç¨ä¾
message.domain.id=ID
message.domain.protocol=æ¥å£åè®®
message.domain.method=httpåè®®ç±»å
message.domain.path=httpåè®®è·¯å¾/å¶å®åè®®åä¸ºç©º
message.domain.status=æ¥å£ç¶æ
message.domain.description=æè¿°
message.domain.caseName=æ¥å£ç¨ä¾åç§°
message.domain.priority=ç¨ä¾ç­çº§
message.domain.caseStatus=ç¨ä¾ç¶æ
message.domain.lastReportStatus=ç¨ä¾ææ°æ§è¡ç»æç¶æ
message.domain.principal=ç¨ä¾è´£ä»»äºº
message.domain.caseCreateTime=ç¨ä¾åå»ºæ¶é´
message.domain.caseCreateUser=ç¨ä¾åå»ºäºº
message.domain.caseUpdateTime=ç¨ä¾æ´æ°æ¶é´
message.domain.caseUpdateUser=ç¨ä¾æ´æ°äºº
message.domain.caseDeleteTime=ç¨ä¾å é¤æ¶é´
message.domain.caseDeleteUser=ç¨ä¾å é¤äºº
message.domain.mockName=ææåç§°
message.domain.reportUrl=æ¥åå°å
message.domain.shareUrl=åäº«å°å
message.domain.reportName=æ¥ååç§°
message.domain.startTime=å¼å§æ¶é´
message.domain.endTime=ç»ææ¶é´
message.domain.requestDuration=è¯·æ±æ»èæ¶
message.domain.reportStatus=æ¥åç¶æ
message.domain.environment=æ¥åç¯å¢
message.domain.errorCount=å¤±è´¥æ°
message.domain.fakeErrorCount=è¯¯æ¥æ°
message.domain.pendingCount=æªæ§è¡æ°
message.domain.successCount=æåæ°
message.domain.assertionCount=æ»æ­è¨æ°
message.domain.assertionSuccessCount=æ­è¨æåæ°
message.domain.requestErrorRate=è¯·æ±å¤±è´¥ç
message.domain.requestPendingRate=è¯·æ±æªæ§è¡ç
message.domain.requestFakeErrorRate=è¯·æ±è¯¯æ¥ç
message.domain.requestPassRate=éè¿ç
message.domain.assertionPassRate=æ­è¨éè¿ç
message.domain.projectId=é¡¹ç®id
#åºæ¯å­æ®µ
message.domain.api_scenario_name=åºæ¯åç§°
message.domain.api_scenario_level=åºæ¯çº§å«
message.domain.api_scenario_status=åºæ¯ç¶æ
message.domain.api_scenario_principal=è´£ä»»äºº
message.domain.api_scenario_stepTotal=åºæ¯æ­¥éª¤æ»æ°
message.domain.api_scenario_num=ç¼å·
message.domain.api_scenario_reportRequestPassRate=éè¿ç
message.domain.api_scenario_lastReportStatus=æåä¸æ¬¡æ§è¡çç»æç¶æ
message.domain.api_scenario_description=æè¿°
message.domain.api_scenario_tags=æ ç­¾
message.domain.api_scenario_grouped=æ¯å¦ä¸ºç¯å¢ç»
message.domain.api_scenario_createUser=åå»ºäºº
message.domain.api_scenario_updateUser=æ´æ°äºº
message.domain.api_scenario_deleteUser=å é¤äºº
message.domain.api_scenario_createTime=åå»ºæ¶é´
message.domain.api_scenario_updateTime=æ´æ°æ¶é´
message.domain.api_scenario_deleteTime=å é¤æ¶é´
message.domain.api_scenario_priority=ä¼åçº§
message.domain.api_scenario_requestPassRate=è¯·æ±éè¿ç
message.domain.api_scenario_reportUrl=æ¥åå°å
message.domain.api_scenario_shareUrl=åäº«å°å
message.domain.api_scenario_reportName=æ¥ååç§°
message.domain.api_scenario_startTime=å¼å§æ¶é´
message.domain.api_scenario_endTime=ç»ææ¶é´
message.domain.api_scenario_requestDuration=è¯·æ±æ»èæ¶
message.domain.api_scenario_reportStatus=æ¥åç¶æ
message.domain.api_scenario_environment=æ¥åç¯å¢
message.domain.api_scenario_errorCount=å¤±è´¥æ°
message.domain.api_scenario_fakeErrorCount=è¯¯æ¥æ°
message.domain.api_scenario_pendingCount=æªæ§è¡æ°
message.domain.api_scenario_successCount=æåæ°
message.domain.api_scenario_assertionCount=æ»æ­è¨æ°
message.domain.api_scenario_assertionSuccessCount=æ­è¨æåæ°
message.domain.api_scenario_requestErrorRate=è¯·æ±å¤±è´¥ç
message.domain.api_scenario_requestPendingRate=è¯·æ±æªæ§è¡ç
message.domain.api_scenario_requestFakeErrorRate=è¯·æ±è¯¯æ¥ç
message.domain.api_scenario_assertionPassRate=æ­è¨éè¿ç
# æµè¯è®¡å
message.domain.test_plan_name=åç§°
message.domain.test_plan_stage=æµè¯é¶æ®µ
message.domain.test_plan_status=æµè¯è®¡åç¶æ
message.domain.test_plan_description=æè¿°
message.domain.test_plan_tags=æ ç­¾
message.domain.test_plan_createUser=åå»ºäºº
message.domain.test_plan_updateUser=æ´æ°äºº
message.domain.test_plan_createTime=åå»ºæ¶é´
message.domain.test_plan_updateTime=æ´æ°æ¶é´
message.domain.test_plan_plannedStartTime=è®¡åå¼å§æ¶é´
message.domain.test_plan_plannedEndTime=è®¡åç»ææ¶é´
message.domain.test_plan_actualStartTime=å®éå¼å§æ¶é´
message.domain.test_plan_actualEndTime=å®éç»ææ¶é´
message.domain.test_plan_num=ç¼å·
message.domain.test_plan_type=ç±»å
message.domain.test_plan_reportName=æ¥ååç§°
message.domain.test_plan_reportUrl=æ¥åé¾æ¥
message.domain.test_plan_reportShareUrl=åäº«æ¥åé¾æ¥
message.domain.test_plan_startTime=å¼å§æ¶é´;è®¡åå¼å§æ§è¡çæ¶é´
message.domain.test_plan_endTime=ç»ææ¶é´;è®¡åç»ææ§è¡çæ¶é´
message.domain.test_plan_execStatus=æ§è¡ç¶æ
message.domain.test_plan_resultStatus=ç»æç¶æ
message.domain.test_plan_passRate=éè¿ç
message.domain.test_plan_passThreshold=éè¿éå¼
message.domain.test_plan_executeRate=æ§è¡ç

# ç¨ä¾è¯å®¡
message.domain.case_review_name=åç§°
message.domain.case_review_num=ID
message.domain.case_review_status=è¯å®¡ç¶æ
message.domain.case_review_description=æè¿°
message.domain.case_review_tags=æ ç­¾
message.domain.case_review_createUser=åå»ºäºº
message.domain.case_review_createTime=åå»ºæ¶é´
message.domain.case_review_updateTime=æ´æ°æ¶é´
message.domain.case_review_updateUser=æ´æ°äºº
message.domain.case_review_endTime=è¯å®¡ç»ææ¶é´
message.domain.case_review_startTime=è¯å®¡å¼å§æ¶é´
message.domain.case_review_passRate=éè¿ç
message.domain.case_review_caseCount=ç¨ä¾æ°
message.domain.case_review_reviewPassRule=è¯å®¡è§å
# ç¼ºé·
message.domain.bug_num=ä¸å¡ID
message.domain.bug_title=ç¼ºé·æ é¢
message.domain.bug_assignUser=ææ´¾äºº
message.domain.bug_platform=ç¼ºé·å¹³å°
message.domain.bug_tag=æ ç­¾
message.domain.bug_status=ç¶æ
message.domain.bug_createUser=åå»ºäºº
message.domain.bug_updateUser=æ´æ°äºº
message.domain.bug_createTime=åå»ºæ¶é´
message.domain.bug_updateTime=æ´æ°æ¶é´
message.domain.bug_deleteUser=å é¤äºº
message.domain.bug_deleteTime=å é¤æ¶é´
message.domain.bug_handleUser=å¤çäºº
message.domain.bug_sync_platform=åæ­¥å¹³å°
message.domain.bug_sync_total_count=åæ­¥æ°é
#UI
message.domain.ui_name=åºæ¯åç§°
message.domain.ui_level=ç¨ä¾ç­çº§
message.domain.ui_principal=è´£ä»»äºº
message.domain.ui_stepTotal=æ­¥éª¤æ»æ°
message.domain.ui_tags=æ ç­¾
message.domain.ui_status=ç¶æ
message.domain.ui_num=ä¸å¡ID
message.domain.ui_lastResult=æåæ§è¡ç»æ
message.domain.ui_createUser=åå»ºäºº
message.domain.ui_updateUser=æ´æ°äºº
message.domain.ui_createTime=åå»ºæ¶é´
message.domain.ui_updateTime=æ´æ°æ¶é´
message.domain.ui_deleteUser=å é¤äºº
message.domain.ui_deleteTime=å é¤æ¶é´
message.domain.ui_description=æè¿°
#æ§è½
message.domain.load_name=æµè¯åç§°
message.domain.load_status=ç¶æ
message.domain.load_num=ä¸å¡ID
message.domain.load_createUser=åå»ºäºº
message.domain.load_updateUser=æ´æ°äºº
message.domain.load_createTime=åå»ºæ¶é´
message.domain.load_updateTime=æ´æ°æ¶é´
message.domain.load_description=æè¿°
#å®æ¶ä»»å¡
message.domain.schedule_key=qrtz UUID
message.domain.schedule_type=æ§è¡ç±»å
message.domain.schedule_value=cron è¡¨è¾¾å¼
message.domain.schedule_job=Schedule Job Class Name
message.domain.schedule_name=åç§°
message.domain.schedule_config=éç½®
message.domain.schedule_createUser=åå»ºäºº
message.domain.schedule_updateUser=æ´æ°äºº
message.domain.schedule_createTime=åå»ºæ¶é´
message.domain.schedule_updateTime=æ´æ°æ¶é´
message.domain.schedule_enable=æ¯å¦å¼å¯
message.domain.schedule_resourceType=èµæºç±»å

resource_pool_not_exist=èµæºæ± ä¸å­å¨

#file management
file_module.not.exist=æä»¶æ¨¡åä¸å­å¨
module.branches.size.limit=æ¨¡åæå¨åæ¯æ»èç¹æ°éä¸è½è¶è¿{0}ä¸ª
drag_node.not.exist=ææ½èç¹ä¸å­å¨
drop_node.not.exist=ç®æ èç¹ä¸å­å¨
file_module.parent.not.exist=æä»¶æ¨¡åç¶èç¹ä¸å­å¨
upload.file.error=ä¸ä¼ æä»¶å¤±è´¥
file.not.exist=æä»¶ä¸å­å¨
file.some.not.exist=é¨åæä»¶ä¸å­å¨
old.file.not.exist=æ§æä»¶ä¸å­å¨
latest.file.not.exist=ææ°æä»¶ä¸å­å¨
file.not.jar=ä¸æ¯jaræä»¶
change.jar.enable=ä¿®æ¹äºjaræä»¶çå¯ç¨ç¶æ
file.name.exist=æä»¶åå·²å­å¨
log.delete_module=æ¨¡åä¸çæææ°æ®å¨é¨è¢«å é¤
file.module.root=æ ¹ç®å½
folder.error=æä»¶å¤¹ä¸åæ³
file.log.move_to=ç§»å¨å°
file.log.change_file_module=æä»¶è¿è¡äºç§»å¨
file.log.next=ä¹å
file.log.previous=ä¹å
file.log.upload=ä¸ä¼ 
file.log.repository.add=æ·»å äºå­å¨åºæä»¶
file.log.re-upload=éæ°ä¸ä¼ äºæä»¶
file.log.upload_file=ä¸ä¼ äºæä»¶
file.log.pull=æåäºæä»¶
file.log.association=å³èäºæä»¶
file.log.association.update=æ´æ°äºå³èäºæä»¶
file.log.association.delete=åæ¶å³èäºæä»¶
file.log.transfer.association=è½¬å­å¹¶å³èäºæä»¶
file.name.cannot.be.empty=æä»¶åç§°ä¸è½ä¸ºç©º
file.size.is.too.large=æä»¶ä¸è½è¶è¿50M
file.is.empty=æä»¶ä¸ºç©º
file.name.error=æä»¶åä¸åæ³
#file management over
# template
project_template_permission_error=æªå¼å¯é¡¹ç®æ¨¡æ¿

third_part_config_is_null=ç¬¬ä¸æ¹å¹³å°éç½®ä¿¡æ¯ä¸è½ä¸ºç©º
plugin_bug_template_remark=æ¨¡æ¿ä¸ºç³»ç»èªå¨è·åï¼ä¸æ¯æç¼è¾åæ¥ç
default_template=é»è®¤æ¨¡æ¿

global_params=å¨å±åæ°.json
env_info_all=ç¯å¢ä¿¡æ¯(æ»).json

# custom_function
custom_function_already_exist= èæ¬åç§°å·²å­å¨
permission.project_custom_function.name=å¬å±èæ¬
permission.project_custom_function.execute=æµè¯

message.domain.report.name=æ¥ååç§°

permission.project_task_center.name=ä»»å¡ä¸­å¿
permission.project_case_task_center.name=ç³»ç»å³æ¶ä»»å¡
permission.project_schedule_task_center.name=ç³»ç»åå°ä»»å¡
permission.project_case_task_center.exec=æ§è¡/åæ­¢