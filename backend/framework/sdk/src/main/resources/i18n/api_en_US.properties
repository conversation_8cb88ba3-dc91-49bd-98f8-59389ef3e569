#module：ApiLoadTest
api_load_test.id.not_blank=ID cannot be empty
api_load_test.resource_id.length_range=Interface use case fk/scene use case fk length must be between 1-50
api_load_test.resource_id.not_blank=Interface use case fk/scenario use case fk cannot be empty
api_load_test.load_test_id.length_range=Performance test case fk length must be between 1-50
api_load_test.load_test_id.not_blank=Performance test case fk cannot be empty
api_load_test.type.length_range=Resource type/CASE/SCENARIO length must be between 1-10
api_load_test.type.not_blank=Resource type /CASE/SCENARIO cannot be empty
#module：ApiTestCaseBlob
api_test_case_blob.api_test_case_id.not_blank=Interface use case pk cannot be empty
#module：ApiDefinitionTemplate
api_definition_template.id.not_blank=Template primary key cannot be empty
api_definition_template.name.length_range=The length of the API template name must be between 1-255
api_definition_template.name.not_blank=API template name cannot be empty
api_definition_template.system.length_range=Whether it is a system template length must be between 1-1
api_definition_template.system.not_blank=Whether it is a system template cannot be empty
api_definition_template.global.length_range=Whether it is a public template length must be between 1-1
api_definition_template.global.not_blank=Whether it is a public template cannot be empty
api_definition_template.create_user.length_range=Creator length must be between 1-50
api_definition_template.create_user.not_blank=Creator cannot be empty
api_definition_template.project_id.length_range=Item fk length must be between 1-50
api_definition_template.project_id.not_blank=Item fk cannot be empty
#module：ApiScenario
permission.system_api_scenario.name=Scenario
api_scenario.name.repeat=Scenario name is repeat
api_scenario.id.not_blank=Can not be empty
api_scenario.name.length_range=The scene name length must be between 1-255
api_scenario.name.not_blank=Scene name cannot be empty
api_scenario.create_user.length_range=Creator length must be between 1-50
api_scenario.create_user.not_blank=Creator cannot be empty
api_scenario.update_user.length_range=Updater length must be between 1-50
api_scenario.update_user.not_blank=Updater cannot be empty
api_scenario.priority.length_range=The scene level/P0/P1 etc. length must be between 1-10
api_scenario.priority.not_blank=Scene level/P0/P1, etc. cannot be empty
api_scenario.status.length_range=Scene status/unplanned/completed equal length must be between 1-20
api_scenario.status.not_blank=Scene status/unplanned/completed etc. cannot be empty
api_scenario.step_total.not_blank=Total number of steps cannot be empty
api_scenario.pass_rate.not_blank=Pass rate cannot be empty
api_scenario.principal.length_range=Responsible person/user fk length must be between 1-50
api_scenario.principal.not_blank=Responsible person/user fk cannot be empty
api_scenario.deleted.length_range=Delete status length must be between 1-1
api_scenario.deleted.not_blank=Delete status cannot be empty
api_scenario.environment_group.length_range=Whether it is an environment group length must be between 1-1
api_scenario.environment_group.not_blank=Whether it is an environment group cannot be empty
api_scenario.project_id.length_range=Item fk length must be between 1-50
api_scenario.project_id.not_blank=Item fk cannot be empty
api_scenario.request_pass_rate.not_blank=Pass rate cannot be empty
api_scenario.request_pass_rate.length_range=Pass rate length must be between 1-20
api_scenario.module_id.length_range=Module fk length must be between 1-50
#module: ApiScenarioStep
api_scenario_step.id.not_blank=ID cannot be empty
api_scenario_step.id.length_range=Step ID length must be between 1-50
api_scenario_step.scenario_id.not_blank=Scene ID cannot be empty
api_scenario_step.scenario_id.length_range=Scene ID length must be between 1-50
api_scenario_step.sort.not_blank=SORT cannot be empty
#module：ApiTestCaseFollow
api_test_case_follow.case_id.length_range=Use case fk length must be between 1-50
api_test_case_follow.case_id.not_blank=Use case fk cannot be empty
api_test_case_follow.follow_id.length_range=Follower/user fk length must be between 1-50
api_test_case_follow.follow_id.not_blank=Follower/user fk cannot be empty
#module：ApiReportBlob
api_report_blob.api_report_id.not_blank=Interface report fk cannot be empty
#module：ApiFakeErrorConfig
api_fake_error_config.id.not_blank=False positive pk cannot be empty
api_fake_error_config.create_user.length_range=Creator length must be between 1-50
api_fake_error_config.create_user.not_blank=Creator cannot be empty
api_fake_error_config.update_user.length_range=Modifier length must be between 1-50
api_fake_error_config.update_user.not_blank=Modifier cannot be empty
api_fake_error_config.name.length_range=False positive name length must be between 1-255
api_fake_error_config.name.not_blank=False positive name cannot be empty
api_fake_error_config.match_type.length_range=Match type length must be between 1-255
api_fake_error_config.match_type.not_blank=Match type cannot be empty
api_fake_error_config.project_id.length_range=Item fk length must be between 1-50
api_fake_error_config.project_id.not_blank=Item fk cannot be empty
#module：ApiScenarioReport
api_scenario_report.id.not_blank=Scenario report pk cannot be empty
api_scenario_report.name.length_range=Report name length must be between 1-255
api_scenario_report.name.not_blank=Report name cannot be empty
api_scenario_report.create_user.length_range=Creator length must be between 1-50
api_scenario_report.create_user.not_blank=Creator cannot be empty
api_scenario_report.deleted.length_range=The deletion ID length must be between 1-1
api_scenario_report.deleted.not_blank=Delete ID cannot be empty
api_scenario_report.update_user.length_range=Modifier length must be between 1-50
api_scenario_report.update_user.not_blank=Modifier cannot be empty
api_scenario_report.status.length_range=Report status/SUCCESS/ERROR length must be between 1-20
api_scenario_report.status.not_blank=Report status/SUCCESS/ERROR cannot be empty
api_scenario_report.trigger_mode.length_range=Trigger method length must be between 1-20
api_scenario_report.trigger_mode.not_blank=Trigger method cannot be empty
api_scenario_report.run_mode.length_range=Execution pattern length must be between 1-20
api_scenario_report.run_mode.not_blank=Execution mode cannot be empty
api_scenario_report.pool_id.length_range=Resource pool length must be between 1-50
api_scenario_report.pool_id.not_blank=Resource pool cannot be empty
api_scenario_report.integrated.length_range=Whether it is an integrated report length must be between 1-1
api_scenario_report.integrated.not_blank=Whether it is an integrated report cannot be empty
api_scenario_report.project_id.length_range=Item fk length must be between 1-50
api_scenario_report.project_id.not_blank=Item fk cannot be empty
api_scenario_report.scenario_id.length_range=Scene fk length must be between 1-50
api_scenario_report.scenario_id.not_blank=Scene fk cannot be empty
api_scenario_report.test_plan_id.not_blank=Test plan ID cannot be empty
api_scenario_report.test_plan_id.length_range=Test plan ID length must be between 1-50
api_scenario_report.start_time.not_blank=Start time cannot be empty
api_scenario_report.request_duration.not_blank=Request duration cannot be empty
api_scenario_report.error_count.not_blank=Error count cannot be empty
api_scenario_report.fake_error_count.not_blank=Fake error count cannot be empty
api_scenario_report.pending_count.not_blank=Pending count cannot be empty
api_scenario_report.success_count.not_blank=Success count cannot be empty
api_scenario_report.assertion_count.not_blank=Assertion count cannot be empty
api_scenario_report.assertion_success_count.not_blank=Assertion success count cannot be empty
api_scenario_report.request_error_rate.not_blank=Request error rate cannot be empty
api_scenario_report.request_error_rate.length_range=Request error rate length must be between 1-20
api_scenario_report.request_pending_rate.not_blank=Request pending rate cannot be empty
api_scenario_report.request_pending_rate.length_range=Request pending rate length must be between 1-20
api_scenario_report.request_fake_error_rate.not_blank=Request false positive rate cannot be empty
api_scenario_report.request_fake_error_rate.length_range=Request false positive rate length must be between 1-20
api_scenario_report.request_pass_rate.not_blank=Request pass rate cannot be empty
api_scenario_report.request_pass_rate.length_range=Request pass rate length must be between 1-20
api_scenario_report.assertion_pass_rate.not_blank=Assertion pass rate cannot be empty
api_scenario_report.assertion_pass_rate.length_range=Assertion pass rate length must be between 1-20
#module：ApiScenarioEnv
api_scenario_environment.id.not_blank=The scene environment pk cannot be empty
api_scenario_environment.api_scenario_id.length_range=Scene fk length must be between 1-50
api_scenario_environment.api_scenario_id.not_blank=Scene fk cannot be empty
api_scenario_environment.project_id.length_range=Item fk length must be between 1-50
api_scenario_environment.project_id.not_blank=Item fk cannot be empty
#module：ApiDefinition
api_definition.id.not_blank=Interface pk cannot be empty
api_definition.id.length_range=Interface pk length must be between 1 and 50
api_definition.create_user.length_range=Creator length must be between 1-50
api_definition.create_user.not_blank=Creator cannot be empty
api_definition.update_user.length_range=Modifier length must be between 1-50
api_definition.update_user.not_blank=Modifier cannot be empty
api_definition.deleted.length_range=Delete status length must be between 1-1
api_definition.deleted.not_blank=Delete status cannot be empty
api_definition.name.length_range=The interface name length must be between 1-255
api_definition.name.not_blank=Interface name cannot be empty
api_definition.method.length_range=Interface type length must be between 1-20
api_definition.method.not_blank=Interface type cannot be empty
api_definition.protocol.length_range=The interface protocol length must be between 1-20
api_definition.protocol.not_blank=Interface protocol cannot be empty
api_definition.status.length_range=Interface status/in progress/completed length must be between 1-50
api_definition.status.not_blank=Interface status/in progress/completed cannot be empty
api_definition.sync_enable.length_range=Whether to enable synchronization length must be between 1-1
api_definition.sync_enable.not_blank=Whether to enable synchronization can not be empty
api_definition.project_id.length_range=Item fk length must be between 1-50
api_definition.project_id.not_blank=Item fk cannot be empty
api_definition.latest.length_range=Is it the latest version 0: No, 1: Yes The length must be between 1-1
api_definition.latest.not_blank=Is it the latest version 0: No, 1: Yes Cannot be empty
api_definition.module_id.not_blank=Module fk cannot be empty
api_definition.module_id.length_range=Module fk length must be between 1 and 50
api_definition.version_id.not_blank=Version fk cannot be empty
api_definition.version_id.length_range=Version fk length must be between 1 and 50
api_definition.ref_id.not_blank=Reference fk cannot be empty
api_definition.ref_id.length_range=Reference fk length must be between 1 and 50
api_definition.description.length_range=Interface description length must be between 0-1000
#module：ApiDefinitionFollow
api_definition_follow.api_definition_id.length_range=Interface fk length must be between 1-50
api_definition_follow.api_definition_id.not_blank=Interface fk cannot be empty
api_definition_follow.follow_id.length_range=Follower/user fk length must be between 1-50
api_definition_follow.follow_id.not_blank=Follower/user fk cannot be empty
#module：ApiSyncConfig
api_sync_config.id.not_blank=Can not be empty
api_sync_config.resource_id.length_range=API/CASE source fk length must be between 1-50
api_sync_config.resource_id.not_blank=API/CASE source fk cannot be empty
api_sync_config.resource_type.length_range=Source type/API/CASE length must be between 1-50
api_sync_config.resource_type.not_blank=Source type/API/CASE cannot be empty
api_sync_config.notify_case_creator.length_range=Whether to notify the use case creator that the length must be between 1-1
api_sync_config.notify_case_creator.not_blank=Whether to notify the use case creator cannot be empty
api_sync_config.notify_scenario_creator.length_range=Whether to notify the scene creator that the length must be between 1-1
api_sync_config.notify_scenario_creator.not_blank=Whether to notify the scene creator can not be empty
api_sync_config.sync_enable.length_range=Whether the synchronization case length must be between 1-1
api_sync_config.sync_enable.not_blank=Whether the synchronization use case cannot be empty
#module：ApiScenarioReportLog
api_scenario_report_log.report_id.not_blank=Request resource id cannot be empty
#module：ApiScenarioFollow
api_scenario_follow.api_scenario_id.length_range=Scene fk length must be between 1-50
api_scenario_follow.api_scenario_id.not_blank=Scene fk cannot be empty
api_scenario_follow.follow_id.length_range=Follower/user fk length must be between 1-50
api_scenario_follow.follow_id.not_blank=Follower/user fk cannot be empty
#module：ApiDefinitionEnv
api_definition_env.id.not_blank=ID cannot be empty
api_definition_env.create_user.length_range=User fk length must be between 1-50
api_definition_env.create_user.not_blank=User fk cannot be empty
api_definition_env.environment_id.length_range=Environment fk length must be between 1-50
api_definition_env.environment_id.not_blank=Environment fk cannot be empty
#module：ApiScenarioBlob
api_scenario_blob.api_scenario_id.not_blank=scene pk cannot be empty

#module：ApiTestCase
api_test_case.id.not_blank=Interface use case pk cannot be empty
api_test_case.name.length_range=The length of the interface use case name must be between 1-255
api_test_case.name.not_blank=Interface use case name cannot be empty
api_test_case.create_user.length_range=Creator length must be between 1-50
api_test_case.create_user.not_blank=Creator cannot be empty
api_test_case.update_user.length_range=Updater length must be between 1-50
api_test_case.update_user.not_blank=Updater cannot be empty
api_test_case.deleted.length_range=The deletion ID length must be between 1-1
api_test_case.deleted.not_blank=Delete ID cannot be empty
api_test_case.priority.length_range=Use case class length must be between 1-50
api_test_case.priority.not_blank=Use case class cannot be empty
api_test_case.sync_enable.length_range=Whether to enable synchronization length must be between 1-1
api_test_case.sync_enable.not_blank=Whether to enable synchronization can not be empty
api_test_case.project_id.length_range=Item fk length must be between 1-50
api_test_case.tag.length_range=Use case tag length must be between 1-64
api_test_case.project_id.not_blank=Item fk cannot be empty
api_test_case.api_definition_id.length_range=Interface fk length must be between 1-50
api_test_case.api_definition_id.not_blank=Interface fk cannot be empty
api_test_case.principal.length_range=The responsible person length must be between 1-50
api_test_case.principal.not_blank=Responsible person cannot be empty
api_test_case.version_id.not_blank=Version fk cannot be empty
api_test_case.version_id.length_range=Version fk length must be between 1-50
#module：ApiDefinitionMock
api_definition_mock.id.not_blank=mock pk cannot be empty
api_definition_mock.create_user.length_range=Creator length must be between 1-50
api_definition_mock.create_user.not_blank=Creator cannot be empty
api_definition_mock.name.length_range=mock name length must be between 1-255
api_definition_mock.name.not_blank=mock name cannot be empty
api_definition_mock.expect_num.length_range=The length of the mock number must be between 1-50
api_definition_mock.expect_num.not_blank=The mock number cannot be empty
api_definition_mock.project_id.length_range=Item fk length must be between 1-50
api_definition_mock.project_id.not_blank=Item fk cannot be empty
api_definition_mock.api_definition_id.length_range=Interface fk length must be between 1-50
api_definition_mock.api_definition_id.not_blank=Interface fk cannot be empty
#module：ApiScenarioReportStep
api_scenario_report_step.step_id.not_blank=Step ID cannot be empty
api_scenario_report_step.step_id.length_range=Step ID length must be between 1-50
api_scenario_report_step.report_id.not_blank=Report ID cannot be empty
api_scenario_report_step.report_id.length_range=Report ID length must be between 1-50
api_scenario_report_step.sort.not_blank=Sort cannot be empty
api_scenario_report_step.step_type.not_blank=Step type cannot be empty
api_scenario_report_step.parent_id.not_blank=Parent ID cannot be empty
api_scenario_report_step.parent_id.length_range=Parent ID length must be between 1-50
#module：ApiReport
api_report.id.not_blank=Interface result report pk cannot be empty
api_report.name.length_range=The interface report name length must be between 1-255
api_report.name.not_blank=Interface report name cannot be empty
api_report.resource_id.length_range=Resource fk/api_definition_id/api_test_case_id length must be between 1-50
api_report.resource_id.not_blank=Resource fk/api_definition_id/api_test_case_id cannot be empty
api_report.create_user.length_range=Creator fk length must be between 1-50
api_report.test_plan_id.not_blank=Test plan fk cannot be empty
api_report.create_user.not_blank=Creator fk cannot be empty
api_report.update_user.length_range=Modifier length must be between 1-50
api_report.update_user.not_blank=Modifier cannot be empty
api_report.start_time.not_blank=Start time cannot be empty
api_report.request_duration.not_blank=Request duration cannot be empty
api_report.deleted.length_range=Delete status length must be between 1-1
api_report.deleted.not_blank=Delete status cannot be empty
api_report.status.length_range=Result status length must be between 1-50
api_report.status.not_blank=Result status cannot be empty
api_report.run_mode.length_range=Execution module/API/CASE/API_PLAN length must be between 1-20
api_report.run_mode.not_blank=Execution module/API/CASE/API_PLAN cannot be empty
api_report.pool_id.length_range=Resource pool length must be between 1-50
api_report.pool_id.not_blank=Resource pool cannot be empty
api_report.trigger_mode.length_range=Trigger mode/manual/batch/timed task/JENKINS length must be between 1-50
api_report.trigger_mode.not_blank=Trigger mode/manual/batch/timed task/JENKINS cannot be empty
api_report.project_id.length_range=Item fk length must be between 1-50
api_report.project_id.not_blank=Item fk cannot be empty
api_report.integrated_report_id.length_range=Integration report id/api_scenario_report_id length must be between 1-50
api_report.integrated_report_id.not_blank=Integration report id/api_scenario_report_id cannot be empty
api_report.integrated.length_range=Whether it is an integrated report, the default is no, the length must be between 1-1
api_report.integrated.not_blank=Whether it is an integrated report, the default is no, it cannot be empty
api_report.error_count.not_blank=Error count cannot be empty
api_report.fake_error_count.not_blank=Fake error count cannot be empty
api_report.pending_count.not_blank=Pending count cannot be empty
api_report.success_count.not_blank=Success count cannot be empty
api_report.assertion_count.not_blank=Assertion count cannot be empty
api_report.assertion_success_count.not_blank=Assertion success count cannot be empty
api_report.request_error_rate.not_blank=Request error rate cannot be empty
api_report.request_error_rate.length_range=Request error rate length must be between 1-20
api_report.request_pending_rate.not_blank=Request pending rate cannot be empty
api_report.request_pending_rate.length_range=Request pending rate length must be between 1-20
api_report.request_fake_error_rate.not_blank=Request false positive rate cannot be empty
api_report.request_fake_error_rate.length_range=Request false positive rate length must be between 1-20
api_report.request_pass_rate.not_blank=Request pass rate cannot be empty
api_report.request_pass_rate.length_range=Request pass rate length must be between 1-20
api_report.assertion_pass_rate.not_blank=Assertion pass rate cannot be empty
api_report.assertion_pass_rate.length_range=Assertion pass rate length must be between 1-20
#module: ApiReportDetail
api_report_detail.id.not_blank=ID cannot be empty
api_report_detail.id.length_range=ID length must be between 1-50
api_report_detail.report_id.not_blank=Report fk cannot be empty
api_report_detail.report_id.length_range=Report fk length must be between 1-50
api_report_detail.step_id.not_blank=Step fk cannot be empty
api_report_detail.step_id.length_range=Step fk length must be between 1-50
api_report_detail.status.not_blank=Status cannot be empty
api_report_detail.status.length_range=Status length must be between 1-20
api_report_detail.request_time.not_blank=Request time cannot be empty
api_report_detail.response_size.not_blank=Response size cannot be empty
#module：ApiReportStep
api_report_step.step_id.not_blank=Step pk cannot be empty
api_report_step.step_id.length_range=Step pk length must be between 1-50
api_report_step.report_id.not_blank=Report fk cannot be empty
api_report_step.report_id.length_range=Report fk length must be between 1-50
api_report_step.sort.not_blank=Sort cannot be empty
api_report_step.step_type.not_blank=Step type cannot be empty
#module：ApiDefinitionModule
api_definition_module.id.not_blank=Interface module pk cannot be empty
api_definition_module.update_user.length_range=Modifier length must be between 1-50
api_definition_module.update_user.not_blank=Modifier cannot be empty
api_definition_module.create_user.length_range=Creator length must be between 1-50
api_definition_module.create_user.not_blank=Creator cannot be empty
api_definition_module.name.length_range=Module name length must be between 1-255
api_definition_module.name.not_blank=Module name cannot be empty
api_definition_module.protocol.length_range=Protocol length must be between 1-20
api_definition_module.protocol.not_blank=Agreement cannot be empty
api_definition_module.parent_id.length_range=Parent fk length must be between 1-50
api_definition_module.parent_id.not_blank=parent fk cannot be empty
api_definition_module.project_id.length_range=Item fk length must be between 1-50
api_definition_module.project_id.not_blank=Item fk cannot be empty
api_definition_module.pos.not_blank=Module position cannot be empty
api_definition_module.api.all=All interfaces
#module：ApiDefinitionMockConfig
api_definition_mock_config.api_definition_mock_id.not_blank=Interface mock pk cannot be empty
#module：ApiScenarioModule
api_scenario_module.id.not_blank=Scene module pk cannot be empty
api_scenario_module.name.length_range=Module name length must be between 1-255
api_scenario_module.name.not_blank=Module name cannot be empty
api_scenario_module.update_user.length_range=Updater length must be between 1-50
api_scenario_module.update_user.not_blank=Updater cannot be empty
api_scenario_module.create_user.length_range=Creator length must be between 1-50
api_scenario_module.create_user.not_blank=Creator cannot be empty
api_scenario_module.project_id.length_range=Item fk length must be between 1-50
api_scenario_module.project_id.not_blank=Item fk cannot be empty
api_scenario_module.parent_id.length_range=Parent fk length must be between 1-50
api_scenario_module.parent_id.not_blank=parent fk cannot be empty
#module：ApiScenarioReference
api_scenario_reference.id.not_blank=Reference relationship pk cannot be empty
api_scenario_reference.api_scenario_id.length_range=Scene fk length must be between 1-50
api_scenario_reference.api_scenario_id.not_blank=Scene fk cannot be empty
api_scenario_reference.create_user.length_range=Creator length must be between 1-50
api_scenario_reference.create_user.not_blank=Creator cannot be empty
api_scenario_reference.reference_id.length_range=Reference step fk length must be between 1-50
api_scenario_reference.reference_id.not_blank=Reference step fk cannot be empty
#module：ApiScenarioReportDetail
api_scenario_report_detail.id.not_blank=ID cannot be empty
api_scenario_report_detail.report_id.length_range=Report fk length must be between 1-50
api_scenario_report_detail.report_id.not_blank=report fk cannot be empty
api_scenario_report_detail.resource_id.length_range=Each step in the scenario requests a unique identifier length must be between 1-50
api_scenario_report_detail.resource_id.not_blank=The unique identifier requested by each step in the scenario cannot be empty
api_scenario_report_detail.request_time.not_blank=Request time cannot be empty
api_scenario_report_detail.response_size.not_blank=Response size cannot be empty
#module：ApiDefinitionBlob
api_definition_blob.api_definition_id.not_blank=The interface fk/one-to-one relationship cannot be empty
#module: ApiDebug
api_debug.id.not_blank=ID cannot be blank
api_debug.id.length_range=The interface ID length must be between 1-50
api_debug.name.not_blank=Interface name cannot be blank
api_debug.name.length_range=The interface name length must be between 1-255
api_debug.protocol.not_blank=Protocol cannot be blank
api_debug.protocol.length_range=Protocol length must be between 1-20
api_debug.method.length_range=Api method length must be between 0-20
api_debug.path.length_range=Api path length must be between 0-500
api_debug.project_id.not_blank=Project ID cannot be blank
api_debug.project_id.length_range=Project ID length must be between 1-50
api_debug.module_id.not_blank=Module ID cannot be blank
api_debug.module_id.length_range=Module ID length must be between 1-50
api_debug.create_user.not_blank=The creator cannot be empty
api_debug.create_user.length_range=Creator length must be between {min}-{max}
api_debug.update_user.not_blank=Modifier cannot be blank
api_debug.update_user.length_range=Modifier length must be between {min}-{max}
#module: ApiDebugModule
api_debug_module.id.not_blank=ID cannot be blank
api_debug_module.id.length_range=Module ID length must be between 1-50
api_debug_module.name.not_blank=Module name cannot be blank
api_debug_module.name.length_range=Module name length must be between 1-255
api_debug_module.parent_id.not_blank=Parent ID cannot be blank
api_debug_module.parent_id.length_range=Parent ID length must be between 1-50
api_debug_module.project_id.not_blank=Project ID cannot be blank
api_debug_module.project_id.length_range=Project ID length must be between 1-50
api_debug_module.pos.not_blank=Module position cannot be blank
api_debug_module.name.not_contain_slash=Module name cannot contain slashes
api_debug_module.unplanned_request=Unplanned request
api_unplanned_request=Unplanned Api
api_unplanned_scenario=Unplanned scenario
#module: ApiEnvironmentConfig
api_environment_config.id.not_blank=ID cannot be blank
api_environment_config.environment_id.length_range=Environment ID length must be between 1-50
api_environment_config.environment_id.not_blank=Environment ID cannot be blank
#module：ApiDefinitionCustomField
api_definition_custom_field.api_id.not_blank=Interface pk cannot be empty
api_definition_custom_field.field_id.not_blank=Field ID cannot be empty
api_module.not.exist=The module does not exist
permission.api.name=API Test
permission.api_mock.name=API MOCK
api_debug_exist=The Request already exists
follow=Follow
unfollow=Unfollow
api_definition_exist=The API already exists
api_definition_mock_exist=The API MOCK already exists
execute_resource_pool_not_config_error=Select a resource pool in 【Project Management - Application Management - Interface Testing】
resource_pool_execute_error=The resource pool call failed
api_swagger_url_error=Swagger url unable to connect
api_scenario_exist=The scenario already exists
schedule_not_exist=The scheduled task does not exist
api_case_report_not_exist=Api report does not exist
api_scenario_report_not_exist=Scenario report does not exist
permission.api_plugin.name=Api Plugin
permission.api_step.name=Step
api_response_name_code_unique=Response name + response code need to be unique

#module：ApiScenarioCsv
api_scenario_csv.file_id.not_blank=File ID cannot be empty
api_scenario_csv.file_id.length_range=File ID length must be between 1-50
api_scenario_csv.scenario_id.not_blank=Scene ID cannot be empty
api_scenario_csv.scenario_id.length_range=Scene ID length must be between 1-50
api_scenario_csv.name.not_blank=Csv file name cannot be empty
api_scenario_csv.name.length_range=Csv file name length must be between 1-255
api_scenario_csv.scope.not_blank=Scope cannot be empty
api_scenario_csv.scope.length_range=Scope length must be between 1-20
api_scenario_csv.association.not_blank=Association cannot be empty
api_scenario_csv.encoding.not_blank=Encoding cannot be empty
api_scenario_csv.encoding.length_range=Encoding length must be between 1-20
api_scenario_csv.random.not_blank=Random cannot be empty
api_scenario_csv.ignore_first_line.not_blank=Ignore the first line cannot be empty
api_scenario_csv.allow_quoted_data.not_blank=Allow quoted data cannot be empty
api_scenario_csv.recycle_on_eof.not_blank=Recycle on EOF cannot be empty
api_scenario_csv.stop_thread_on_eof.not_blank=Stop thread on EOF cannot be empty

#module: ApiDefinitionSwagger
api_definition_swagger.id.not_blank=ID cannot be empty
api_definition_swagger.id.length_range=ID length must be between 1-50
api_definition_swagger.name.not_blank=Name cannot be empty
api_definition_swagger.name.length_range=Name length must be between 1-255
api_definition_swagger.swagger_url.not_blank=Swagger url cannot be empty
api_definition_swagger.swagger_url.length_range=Swagger url length must be between 1-500
api_definition_swagger.project_id.not_blank=Project ID cannot be empty
api_definition_swagger.project_id.length_range=Project ID length must be between 1-50
api_definition_swagger.module_id.length_range=Module ID length must be between 0-50

api_test_case.env_id.length_range=Environment ID length must be between 0-50
api_test_case.status.length_range=Status length must be between 0-20
api_test_case.priority.length_range_=Priority length must be between 0-50
api_test_case.environment_id.length_range=Environment ID length must be between 0-50
api_scenario.target_module_id.length_range=Target module ID length must be between 0-50

api_scenario.group_id.length_range=Group ID length must be between 0-50
api_scenario.environment_id.length_range=Environment ID length must be between 0-50

api_report_default_env=Default environment
tags_size_large_than=The number of tags cannot exceed 10

no_permission_to_resource=No permission to access the resource
api_scenario_circular_reference_error=There are circular references to the scenario
api_import_url_is_exist=The imported URL already exists
report.status.success=Success
report.status.error=Error
report.status.pending=Pending
report.status.fake_error=Fake error

api_definition.status.ongoing=Underway
api_definition.status.completed=Completed
api_definition.status.abandoned=Abandoned
api_definition.status.continuous=Continuous

api_test_case.clear.api_change=Ignore the differences in this change
api_test_case.ignore.api_change=Ignore all change differences

curl_script_is_empty=Curl script cannot be empty
curl_script_is_invalid=Curl script is invalid
curl_raw_content_is_invalid=Raw content is invalid

api_batch_task_name=Api cases batch task
api_scenario_batch_task_name=Scenarios batch task

# api doc share i18n
api_doc_share.not_exist=api doc share not exist
api_doc_share.id.not_blank=id cannot be empty
api_doc_share.id.length_range=id length must be between {min} and {max}
api_doc_share.name.not_blank=name cannot be empty
api_doc_share.name.length_range=name length must be between {min} and {max}
api_doc_share.is_private.not_blank=isPublic cannot be empty
api_doc_share.is_private.length_range=isPublic length must be between {min} and {max}
api_doc_share.allow_export.not_blank=allowExport cannot be empty
api_doc_share.allow_export.length_range=allowExport length must be between {min} and {max}
api_doc_share.api_range.not_blank=apiRange cannot be empty
api_doc_share.api_range.length_range=apiRange length must be between {min} and {max}
api_doc_share.create_user.not_blank=createUser cannot be empty
api_doc_share.create_user.length_range=createUser length must be between {min} and {max}
api_doc_share.project_id.not_blank=projectId cannot be empty
api_doc_share.project_id.length_range=projectId length must be between {min} and {max}
api_doc_share.name_duplicate=Share name duplicate