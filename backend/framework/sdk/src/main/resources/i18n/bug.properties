# bug
bug.id.not_blank=ID不能为空
bug.id.length_range=ID长度必须在1-50之间
bug.title.not_blank=缺陷标题不能为空
bug.title.length_range=缺陷标题长度必须在1-255之间
bug.assign_user.not_blank=指派人不能为空
bug.assign_user.length_range=指派人长度必须在1-50之间
bug.create_user.not_blank=创建人不能为空
bug.create_user.length_range=创建人长度必须在1-50之间
bug.update_user.not_blank=更新人不能为空
bug.update_user.length_range=更新人长度必须在1-50之间
bug.delete_user.not_blank=删除人不能为空
bug.delete_user.length_range=删除人长度必须在1-50之间
bug.project_id.not_blank=项目ID不能为空
bug.project_id.length_range=项目ID长度必须在1-50之间
bug.template_id.not_blank=模板ID不能为空
bug.template_id.length_range=模板ID长度必须在1-50之间
bug.platform_id.not_blank=平台缺陷ID不能为空
bug.platform_id.length_range=平台缺陷ID长度必须在1-50之间
bug.platform.not_blank=缺陷平台不能为空
bug.platform.length_range=缺陷平台长度必须在1-50之间
bug.status.not_blank=平台状态不能为空
bug.status.length_range=平台状态长度必须在1-50之间
bug.deleted.not_blank=删除状态不能为空
bug.deleted.length_range=删除状态长度必须在1-50之间

# bugContent
bug_content.bug_id.not_blank=缺陷ID不能为空
bug_content.bug_id.length_range=缺陷ID长度必须在1-50之间

# bugFollower
bug_follower.bug_id.not_blank=缺陷ID不能为空
bug_follower.bug_id.length_range=缺陷ID长度必须在1-50之间
bug_follower.user_id.not_blank=关注人ID不能为空
bug_follower.user_id.length_range=关注人ID长度必须在1-50之间

# bugComment
bug_comment.id.not_blank=ID不能为空
bug_comment.id.length_range=ID长度必须在1-50之间
bug_comment.bug_id.not_blank=缺陷ID不能为空
bug_comment.bug_id.length_range=缺陷ID长度必须在1-50之间
bug_comment.create_user.not_blank=评论人不能为空
bug_comment.create_user.length_range=评论人长度必须在1-50之间
bug_comment.update_user.not_blank=更新人不能为空
bug_comment.update_user.length_range=更新人长度必须在1-50之间

# bugLocalAttachment
bug_local_attachment.id.not_blank=ID不能为空
bug_local_attachment.id.length_range=ID长度必须在1-50之间
bug_local_attachment.bug_id.not_blank=缺陷ID不能为空
bug_local_attachment.bug_id.length_range=缺陷ID长度必须在1-50之间
bug_local_attachment.file_id.not_blank=文件ID不能为空
bug_local_attachment.file_id.length_range=文件ID长度必须在1-50之间
bug_local_attachment.file_name.not_blank=文件名称不能为空
bug_local_attachment.file_name.length_range=文件名称长度必须在1-50之间
bug_local_attachment.source.not_blank=文件来源不能为空
bug_local_attachment.source.length_range=文件来源长度必须在1-50之间
bug_local_attachment.create_user.not_blank=创建人不能为空
bug_local_attachment.create_user.length_range=创建人长度必须在1-50之间

# bugCustomField
bug_custom_field.bug_id.not_blank=缺陷ID不能为空
bug_custom_field.bug_id.length_range=缺陷ID长度必须在1-50之间
bug_custom_field.field_id.not_blank=字段ID不能为空
bug_custom_field.field_id.length_range=字段ID长度必须在1-50之间

# bugRelationCase
bug_relation_case.id.not_blank=ID不能为空
bug_relation_case.id.length_range=ID长度必须在1-50之间
bug_relation_case.case_id.not_blank=关联功能用例ID不能为空
bug_relation_case.case_id.length_range=关联功能用例ID长度必须在1-50之间
bug_relation_case.bug_id.not_blank=缺陷ID不能为空
bug_relation_case.bug_id.length_range=缺陷ID长度必须在1-50之间
bug_relation_case.case_type.not_blank=关联的用例类型不能为空
bug_relation_case.case_type.length_range=关联的用例类型长度必须在1-50之间
bug_relation_case.create_user.not_blank=创建人不能为空
bug_relation_case.create_user.length_range=创建人长度必须在1-50之间

# error
bug_not_exist=缺陷不存在
not_local_bug_error=非本地缺陷，无法操作
bug_tags_size_large_than=缺陷标签数量超过{0}个
third_party_not_config=请正确配置服务集成或项目应用设置的参数, 并启用;
bug_attachment_upload_error=缺陷附件上传失败
bug_attachment_link_error=缺陷附件关联失败
bug_attachment_delete_error=缺陷附件删除失败
no_bug_select=未勾选缺陷
bug_select_not_found=未查询到勾选的缺陷
bug_comment.event.not_blank=缺陷评论事件类型不能为空
bug_comment.parent_id.not_blank=缺陷评论父级ID不能为空
bug_comment.parent.not_exist=父级评论不存在
bug_comment.reply_user.not_blank=缺陷回复人不能为空
bug_comment_not_exist=缺陷评论不存在
bug_comment_not_owner=非当前评论创建人, 无法操作!
bug_relate_case_not_found=未查询到关联的用例
bug_relate_case_type_unknown=关联的用例类型未知, 无法查看
unknown_case_type_of_relate_case=参数错误, 未知的用例类型
bug_relate_case_permission_error=无用例查看权限, 请联系管理员
bug_status_can_not_be_empty=缺陷状态不能为空
handle_user_can_not_be_empty=缺陷处理人不能为空
bug.title=缺项名称
bug.handle_user=处理人
bug.status=状态
bug.tag=标签
bug.description=缺陷内容
# bug export
bug.system_columns.not_empty=系统字段不能为空
bug.export.system.columns.id=ID
bug.export.system.columns.name=缺陷名称
bug.export.system.columns.content=缺陷内容
bug.export.system.columns.status=缺陷状态
bug.export.system.columns.handle_user=处理人
bug.export.system.other.columns.create_user=创建人
bug.export.system.other.columns.create_time=创建时间
bug.export.system.other.columns.case_count=用例数
bug.export.system.other.columns.comment=评论
bug.export.system.other.columns.platform=所属平台

permission.bug.name=缺陷管理
permission.bug=缺陷
# sync mode
sync_mode.manual=手动同步
sync_mode.auto=定时同步