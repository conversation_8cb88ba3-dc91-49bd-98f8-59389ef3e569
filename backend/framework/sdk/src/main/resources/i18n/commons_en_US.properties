#commons
error_lang_invalid=Invalid language parameter
file_cannot_be_null=File cannot be empty!
length.too.large=too large
cannot_be_null=\tCannot be empty
number=Number
row=row
error=error
connection_failed=Connection failed
upload_config_save_param_error=File limit parameter error
connection_timeout=Connection timeout
delete_fail=Delete fail
start_engine_fail=Start fail
upload_fail=Upload fail
invalid_parameter=Invalid parameter!
name_already_exists=Name already exists
resource_not_exist=The resource does not exist or has been deleted
upload_file_fail_get_file_path_fail=Failed to upload the file, failed to get the file path
#user related
user_email_already_exists=User email already exists
user_id_is_null=User ID cannot be null
user_name_is_null=User name cannot be null
user_name_length_too_long=User name length cannot exceed 256 characters
user_email_is_null=User email cannot be null
password_is_null=Password cannot be null
password_length_too_long=Password length cannot exceed 256 characters
user_id_already_exists=User ID already exists
password_modification_failed=The old password is wrong. Please re-enter it
cannot_delete_current_user=Cannot delete the user currently logged in
user_already_exists=The user already exists in the current member list
cannot_remove_current=Unable to remove the currently logged in user
password_is_incorrect=Incorrect password or email
user_not_exist=user does not exist
user_has_been_disabled=the user has been disabled.
excessive_attempts=Excessive attempts
user_locked=the user has been locked.
user_expires=user expires.
not_authorized=not authorized.
login_fail=Login fail
user_apikey_limit=Can have up to 5 api keys
please_logout_current_user=Please logout current user first
resource.name=Resource
keep_at_least_one_administrator=Keep at least one administrator
project.member_count.not_blank=The number of members cannot be empty
#load test
edit_load_test_not_found=Cannot edit test, test not found=
run_load_test_not_found=Cannot run test, test not found=
run_load_test_file_not_found=Unable to run test, unable to get test file meta information, test ID=
run_load_test_file_content_not_found=Cannot run test, cannot get test file content, test ID=
run_load_test_file_init_error=Failed to run the test, please go to [Settings-System-System Parameter Setting] to check the current site configuration. For details, see https://metersphere.io/docs/faq/load_ test/#url
load_test_is_running=Load test is running, please wait.
load_test_kafka_invalid=Kafka is not available, please check the configuration
cannot_edit_load_test_running=Cannot modify the running test
test_not_found=Test cannot be found:
test_not_running=Test is not running
load_test_already_exists=Duplicate load test name
load_test_name_length=The length of the test name exceeds the limit
no_nodes_message=No node message
duplicate_node_ip=Duplicate IPs
duplicate_node_port=Duplicate Ports
duplicate_node_ip_port=Duplicate IPs & Ports
max_thread_insufficient=The number of concurrent users exceeds
related_case_del_fail_prefix=Connected to
related_case_del_fail_suffix=TestCase, please disassociate first
jmx_content_valid=JMX content is invalid
container_delete_fail=The container failed to stop, please try again
load_test_report_file_not_exist=There is no JTL file in the current report, please wait or execute it again to get it
startTime_must_be_less_than_endTime=Start time must be less than end time
start_time_is_null=Start time cannot be null
end_time_is_null=End time cannot be null

#organization
all_organization=All Organization
organization_not_exists=Organization is not exists
#test resource pool
test_resource_pool_id_is_null=Test Resource Pool ID cannot be null
test_resource_pool_name_is_null=Test Resource Pool name cannot be null
test_resource_pool_name_already_exists=The test resource pool name already exists
test_resource_pool_type_is_null=Test Resource Pool type cannot be null
resource_pool_application_organization_is_empty = Resource pool application organization is empty
test_resource_pool_used_content_is_null = Resource pool use-related configuration is empty

load_test=Load Test
test_resource_pool_is_use=This resource pool is in use and cannot be deleted
test_resource_pool_is_valid_fail =The verification fails, please check whether the resource pool configuration is normal
only_one_k8s=Only one K8S can be added
test_resource_pool_not_exists=Test resource pool not exists
default_test_resource_pool_is_empty=The default resource pool cannot be delete
test_resource_pool_invalid=Test resource pool invalid
selenium_grid_is_null=selenium_grid cannot be null
ip_is_null=ip address/domain name cannot be null
port_is_null = Port cannot be null
concurrent_number_is_null = ConcurrentNumber cannot be null
monitor_is_null = Monitor cannot be null
token_is_null = Token can not be null
namespace_is_null=Namespace can not be null
deploy_name_is_null=Deploy Name cannot be null
api_test_image_is_null=API cannot be null
#project
project_name_is_null=Project name cannot be null
project_name_already_exists=The project name already exists
project_file_already_exists=The file already exists
project_file_in_use=use this file and cannot be deleted.
#organization
organization_name_is_null=organization name cannot be null
organization_name_already_exists=The organization name already exists
organization_does_not_belong_to_user=The current organization does not belong to the current user
organization_id_is_null=Organization ID cannot be null
organization_default_not_exists=The default organization does not exist
#api
api_load_script_error=Load script error
illegal_xml_format=illegal XML format
api_report_is_null="Report is null, can't update"
api_test_environment_already_exists=Environment already exists
api_test=API Test
#test case
test_case_node_level=level
test_case_node_level_tip=The node tree maximum depth is
test_case_module_not_null=The owned module cannot be empty
test_case_create_module_fail=Failed to create module
test_case_import_template_name=Test_case_templates
test_case_import_template_sheet=Template
module_not_null=The module must not be blank
module_starts_with=The module must start with '/'
user_not_exists=The user in this project is not exists
test_case_already_exists=The test case in this project is exists
parse_data_error=Parse data error
parse_empty_data=Parse empty data
missing_header_information=Missing header information
test_case_exist=A test case already exists under this project:
node_deep_limit=The node depth does not exceed 8 layers!
before_delete_plan=There is an associated test case under this plan, please unlink it first!
incorrect_format=\tincorrect format
test_case_step_model_validate=\tmust be TEXT, STEP
test_case_priority_validate=\tmust be P0, P1, P2, P3
test_case_method_validate=\tmust be manual, auto
test_case_name=Name
test_case_type=Type
test_case_maintainer=Maintainer
test_case_priority=Priority
test_case_method=method
test_case_prerequisite=Prerequisite
test_case_remark=Remark
test_case_step_desc=Step description
test_case_step_result=Step result
test_case_module=Module
test_case=Case
user=User
user_import_template_name=User import templates
user_import_template_sheet=templates
user_import_format_wrong=input error
user_import_id_is_repeat=Id repeat
user_import_email_is_repeat=E-mail repeat
user_import_password_format_wrong=Wrong password format
user_import_phone_format_wrong=Wrong phone format
user_import_email_format_wrong=Wrong email format
user_import_organization_not_fond=Organization is not found
org_admin=Organization manager
org_member=Organization member
test_manager=Test manager
tester=Tester
read_only_user=Read-only user
module=Module
num_needed_modify_testcase=ID is needed when modifying testcase
num_needless_create_testcase=ID is needless when creating testcase
tag_tip_pattern=Labels should be separated by semicolons or commas
preconditions_optional=Preconditions optional
remark_optional=Remark optional
do_not_modify_header_order=Do not modify the header order
module_created_automatically=If there is no such module, will be created automatically
options=options
options_yes=Yes
options_no=No
required=Required
password_format_is_incorrect=Valid password: 8-30 digits, English upper and lower case letters + numbers + special characters (optional)
please_input_project_member=Please input project merber's email
test_case_report_template_repeat=The organization has the same name template
plan_name_already_exists=Test plan name already exists
test_case_already_exists_excel=There are duplicate test cases in the import file
test_case_module_already_exists=The module name already exists at the same level
api_test_name_already_exists=Test name already exists
functional_method_tip=Functional test not support auto method
custom_num_is_exist=Use case custom ID already exists
custom_num_is_not_exist=Use case custom ID not exists
id_required=ID required
id_repeat_in_table=ID is repeat in table
step_model_tip=Step description fill in STEP, text description please fill in TEXT (not required)
case_status_not_exist=The use case status must be Prepare, Underway way and Completed
issue_project_not_exist=ID does not exist or other errors
tapd_project_not_exist=The associated TAPD item ID does not exist
zentao_get_project_builds_fail=Get Affecting Version Errors
zentao_project_id_not_exist=The associated Zen Tao ID does not exist or other errors
#ldap
ldap_url_is_null=LDAP address is empty
ldap_dn_is_null=LDAP binding DN is empty
ldap_ou_is_null=LDAP parameter OU is empty
ldap_password_is_null=LDAP password is empty
ldap_connect_fail=Connection LDAP failed
ldap_connect_fail_user=Connection LDAP failed, wrong DN or password bound
ldap_user_filter_is_null=LDAP user filter is empty
ldap_user_mapping_is_null=LDAP user mapping is empty
authentication_failed=User authentication failed,wrong user name or password
user_not_found_or_not_unique=User does not exist or is not unique
find_more_user=Multiple users found
ldap_authentication_not_enabled=LDAP authentication is not enabled
login_fail_email_null=Login failed, user mailbox is empty
login_fail_ou_error=Login failed, please check the user OU
login_fail_filter_error=Login failed, please check the user filter
check_ldap_mapping=Check LDAP attribute mapping
ldap_mapping_value_null=LDAP user attribute mapping field is empty
oauth_mapping_config_error=OAuth2 attribute mapping misconfiguration
oauth_mapping_value_null=OAuth2 user attribute mapping field is empty
#quota
quota_project_excess_ws_api=The total number of interface tests for a project cannot exceed the organization quota
quota_project_excess_ws_performance=The total number of performance tests for a project cannot exceed the organization quota
quota_project_excess_ws_max_threads=The maximum concurrent number of projects cannot exceed the quota of the organization
quota_project_excess_ws_max_duration=The stress test duration of the project cannot exceed the organization quota
quota_project_excess_ws_resource_pool=The resource pool of the project cannot exceed the scope of the resource pool of the organization
quota_project_excess_ws_vum_total=The sum of the total number of vums of the project cannot exceed the organization quota
quota_vum_used_gt_vum_total=The total number of vum cannot be less than the number of consumed vum
quota_api_excess_organization=The number of interface tests exceeds the organization quota
quota_api_excess_project=The number of interface tests exceeds the project limit
quota_performance_excess_organization=The number of performance tests exceeds the organization quota
quota_performance_excess_project=The number of performance tests exceeds the project limit
quota_max_threads_excess_organization=The maximum number of concurrent threads exceeds the organization quota
quota_max_threads_excess_project=The maximum concurrent number exceeds the project limit
quota_duration_excess_organization=The stress test duration exceeds the work space quota
quota_duration_excess_project=The stress test time exceeds the project limit
quota_member_excess_organization=The number of members exceeds the organization quota
quota_member_excess_project=The number of members exceeds the project quota
quota_project_excess_project=Number of projects exceeds organization quota
quota_vum_used_excess_organization=The amount of vum consumed exceeds the organization quota
quota_vum_used_excess_project=The amount of vum consumed exceeds the project quota
import_xmind_count_error=The number of use cases imported into the mind map cannot exceed 800
import_xmind_not_found=Test case not found
license_valid_license_error=Authorization authentication failed
test_review_task_notice=Test review task notice
swagger_url_scheduled_import_notification=SwaggerUrl Scheduled import notification
test_track.length_less_than=The title is too long, the length must be less than
# check owner
check_owner_project=The current user does not have permission to operate this project
check_owner_test=The current user does not have permission to operate this test
check_owner_case=The current user does not have permission to operate this resource or Resource does not exist
check_owner_plan=The current user does not have permission to operate this plan
check_owner_review=The current user does not have permission to operate this review
check_owner_comment=The current user does not have permission to manipulate this comment
check_owner_organization=The current user does not have permission to operate this organization
upload_content_is_null=Imported content is empty
test_plan_notification=Test plan notification
task_defect_notification=Task defect notification
task_notification_=Timing task result notification
api_definition_url_not_repeating=The interface request address already exists
api_definition_name_not_repeating=The same name-url combination already exists
task_notification_jenkins=Jenkins Task notification
task_notification=Result notification
message_task_already_exists=Task recipient already exists
#automation
automation_name_already_exists=the scenario already exists in the module of the same project
automation_exec_info=There are no test steps to execute
delete_check_reference_by=be referenced by Scenario 
not_execute=Not execute
execute_not_pass=Not pass
execute_pass=Pass
import_fail_custom_num_exists=import fail, custom num is exists
#authsource
authsource_name_already_exists=Authentication source name already exists
authsource_name_is_null=Authentication source name cannot be empty
authsource_configuration_is_null=Authentication source configuration cannot be empty
authsource_type_is_null=Authentication source type cannot be empty
authsource_is_delete=Authentication source is deleted
mobile_phone_number_cannot_be_empty=When the receiving mode is pin and enterprise wechat: the user's mobile phone number cannot be empty
custom_field_already=A filed already exists under this organization:
template_already=A template already exists under this organization:
expect_name_exists=Expect name is exists
ssl_password_error=The authentication password is wrong, please re-enter the password
ssl_file_error=Failed to load the certification file, please check the certification file
#log
api_definition=Api definition
api_definition_case=Api definition case
api_automation_schedule=Api automation schedule
api_automation=Api automation
api_automation_report=Test Report
track_test_case=Test case
track_test_case_review=Case review
track_test_plan=Test plan
track_test_plan_schedule=Test plan schedule
track_bug=Defect management
track_report=Report
performance_test=Performance test
performance_test_report=Performance test report
system_user=System user
system_organization=System organization
system_test_resource=System test resource
system_parameter_setting=System parameter setting
system_quota_management=System Quota management
system_authorization_management=System authorization management
organization_member=Organization member
organization_service_integration=Organization service integration
organization_message_settings=Organization message settings
organization_template_settings_field=Organization template settings field
organization_template_settings_case=Organization template settings case
organization_template_settings_bug=Organization template settings bug
project_project_manager=Project manager
project_project_member=Project member
project_project_jar=Project jar
project_environment_setting=Project environment setting
project_file_management=Project file management
personal_information_personal_settings=Personal information personal settings
personal_information_apikeys=Personal information API Keys
auth_title=Auth
group_permission=Group
test_case_status_prepare=Prepare
test_case_status_again=Again
test_case_status_running=Running
test_case_status_finished=Finished
test_case_status_error=Error
test_case_status_success=Success
test_case_status_trash=Trash
test_case_status_saved=Saved
connection_expired=The connection has expired, please get it again
# track home
api_case=Api
performance_case=Performance
scenario_case=Scenario
ui_case=UI
scenario_name_is_null=Scenario name cannot be empty
create_user=Create user
test_case_status=Case status
id_not_rightful=ID is not rightful
tags_count=Tags count can not exceed 10
tag_length=Tag length cannot exceed 64 characters
step_length=Step length cannot exceed 1000 characters
result_length=Result length cannot exceed 1000 characters
project_reference_multiple_plateform=Projects point to multiple third-party platforms
# mock
mock_warning=No matching Mock expectation was found
zentao_test_type_error=invalid Zentao request 
#\u9879\u76EE\u62A5\u544A
enterprise_test_report=Enterprise report
count=Count
cannot_find_project=Cannot find project
project_repeatable_is_false=Url repeatable not open
#\u73AF\u5883\u7EC4
null_environment_group_name=Environment group name is null
environment_group_name=Environment group name
environment_group_exist=already exists
environment_group_has_duplicate_project=Environment group has duplicate project
#\u8BEF\u62A5\u5E93
error_report_library=Error report
bug_jira_info_error=Check the service integration information or Jira project ID
error_code_is_unique=Error code is not unique
no_version_exists=version not exists
jira_auth_error=Account name or password (Token) is wrong
jira_auth_url_error=The test connection failed, please check whether the Jira address is correct
#ui \u6307\u4EE4\u6821\u9A57
param_error=Param Error
is_null=can't be null
url_is_null=URL can't be null
frame_index_is_null=frame index can't be null
element_is_null=element can't be null
locator_is_null=locator can't be null
coord=coord
input_content=input
subitem_type=subitem type
subitem=subitem
varname=variable
varname_or_value=variable or value
attributeName=attribute name
webtitle_varname=title varname
webhandle_varname=handle varname
cant_be_negative=can't be negative
expression=expression
times=times
command=command
extract_type=extract type
cmdValidation=validation
cmdValidateValue=validate value
cmdValidateText=validate text
cmdValidateDropdown=validate dropdown
cmdValidateElement=validate element
cmdValidateTitle=validate title
cmdOpen=open
cmdSelectWindow=select window
cmdSetWindowSize=set window size
cmdSelectFrame=select frame
cmdDialog=dialog operation
cmdDropdownBox=dropdown
submit=submit
cmdSetItem=set item
cmdWaitElement=wait element
cmdInput=input
cmdMouseClick=click
cmdMouseMove=mouse move
cmdMouseDrag=mouse drag
cmdTimes=times
cmdForEach=ForEach
cmdWhile=While
cmdIf=If
cmdElse=Else
cmdElseIf=ElseIf
close=close
cmdExtraction=extracion
cmdExtractWindow=window extraction
cmdExtractElement=element extraction
tcp_mock_not_unique=This tcp port is be used
no_tcp_mock_port=No idle tcp port, please contact administrators.
name_already_exists_in_module=Name already exists in same module
repository_module_already_exists=The repository name already exists at the same project
can_not_move_to_repository_node=Can not move to repository node
# bug template copy
target_bug_template_not_checked=Cannot copy, target project not checked
source_bug_template_is_empty=Copy error, source project is empty
#æ¨¡åç¸å³
module.id.not_blank=Id not blank
module.project_id.not_blank=Project Id not blank
module.name.not_blank=Name not blank
module.not.exist=Module not exist
module.parent.not.exist=Parent module not exist
module.root=Root module

#plugin
get_plugin_instance_error=Get the plugin instance error!
# userRole
user_role_relation_exist_error=The user is already in the current user groupï¼
internal_user_role_permission_error=Internal user groups cannot be edited or deletedï¼
user_role_relation_remove_admin_user_permission_error=Unable to delete the admin user from the system administrator user groupï¼
internal_admin_user_role_permission_error=Internal admin user group cannot be edited or deleted!

# customField
internal_custom_field_permission_error=System fields cannot be deletedï¼
internal_template_permission_error=System template cannot be deletedï¼
default_template_permission_error=Default templates cannot be deletedï¼
field_validate_error=The field {0} value is invalid
simple_field_validate_error_tips=The field parameter value is invalid, please check the required field or the default value!
status_definition_required_error=This status cannot be canceled

#result message
http_result_success=operate success
http_result_unknown_exception=system unknown exception
http_result_validate=parameter validate failure
http_result_unauthorized=user authentication failure
http_result_forbidden=permission authentication failure

enum_value_valid_message=The enumeration value is invalid, must be

#system organization
organization_member_log=organization member
#system project
project_admin=Project admin
project_member=Project member
project=Project
add=Add
delete=Delete
update=Update
recover=Recover
copy=Copy
move=Move
archive=Archive
run=Run
project_is_not_exist=Project is not exist

#permission
permission.system.name=System
permission.org.name=Organization
permission.project.name=Project
permission.read=Read
permission.add=Create
permission.edit=Update
permission.delete=Delete
permission.import=Import
permission.recover=Recover
permission.export=Export
permission.execute=Execute
permission.debug=Debug
permission.association=Associate
permission.association_and_disassociation=Associate/Disassociate
file_name_illegal_error=File name is illegal
plugin_enable_error=Plugin is not enabled
plugin_permission_error=No access to this plugin

scheduled_tasks=Scheduled Tasks
template_scene_illegal_error=Scene is illegal
# åç½®çæ¨¡æ¿æå­æ®µ
custom_field.functional_priority=Case Priority
custom_field.bug_degree=Bug Degree
template.default=Default

set_default_template=Set the default template
project_template_enable=Enable the project template

global_parameters_already_exist=Global parameters already exist
global_parameters_is_not_exist=Global parameters is not exist
api_test_environment_not_exist=Environment is not exist
mock_environment_not_delete=Deletion is not allowed in the mock environment


parent.node.not_blank=Parent node can not blank
node.not_blank=Node can not blank
node.name.repeat=This module name already exists at this level
project.cannot.match.parent=Project can not match parent

# ç¶ææµ
status=Status
status_flow.name=Status Flow
status_item.bug_new=NEW
status_item.bug_in_process=IN PROCESS
status_item.bug_closed=CLOSED
status_item.bug_resolved=RESOLVED
status_item.bug_rejected=REJECTED

permission.api_test.name=API Test
permission.api_debug.name=Debug
permission.api_definition.name=API
permission.api_case.name=Case
permission.api_mock.name=Mock
permission.api_doc.name=Document
permission.api_doc.share=Document Share
permission.api_definition.import=Import
permission.api_definition.export=Export
permission.api_definition.execute=Execute
permission.api_report=Report

#æ¥å£ç®¡ç
api_definition_not_exist=API definition is not exist
api_test_case_name_exist=API test case name is exist
file_upload_fail=File upload fail
api_test_case_not_exist=API test case is not exist
please_recover_the_interface_data_first=Please recover the interface data first
batch_edit_type_error=Batch edit type error
environment_id_is_null=Environment ID is null
environment_is_not_exist=Environment is not exist
environment_group_id_is_null=Environment group ID is null
environment_group_is_not_exist=Environment group is not exist
status_is_null=Status is null
api_scenario_is_not_exist=API scenario is not exist
priority_is_null=Priority is null
apikey_has_expired=ApiKey has expired
user_key.id.not_blank=User key id can not blank
expire_time_not_null=Expire time can not null
permission.organization.name=Organization
swagger_parse_error_with_auth=Swagger parsing failed, please confirm whether the verification information is correct or the file format is correct!
swagger_parse_error=Swagger parsing failed or file format is incorrect!
#æµè¯è®¡å
permission.test_plan.name=Test plan
permission.test_plan=Plan
permission.test_plan_report=Report

excel.template.id=Non mandatory, ID is automatically generated by the system; When importing use cases, you can choose whether to cover use cases with the same ID; When the ID is empty or does not exist, it defaults to adding a new use case;;
excel.template.case_edit_type=Not mandatory, fill in STEP for step description, fill in Text for text description, default to Text if not filled in
excel.template.tag=Not mandatory labels should be separated by semicolons or commas
excel.template.text_description=Not mandatory, when the editing mode is STEP, the step description will be based on the identifier [1] [2] [3] To determine whether to split a cell into multiple steps, if not, it is a single step
excel.template.member=please fill in the relevant personnel email under this project
excel.template.not_required=Not required
case_import_table_header_missing=Header information is missing!
functional_case.module.length_less_than=The title is too long, the length must be less than
custom_field_required_tip=[%s] is required
custom_field_array_tip=[%s] must be array
custom_field_datetime_tip=[%s] must be in time-date format [%s]
custom_field_date_tip=[%s] must be in date format [%s]
custom_field_member_tip=[%s] must be current project member
custom_field_select_tip=[%s] must be %s
custom_field_int_tip=[%s] must be integer
custom_field_float_tip=[%s] must be number
check_import_excel_error=Verification failed, please check if the file is correct
custom_field_multip_input_tip=The number of [%s] cannot exceed 15
custom_field_multip_input_length_tip=The length of [%s] cannot exceed 64 characters
custom_field_input_length_tip=The length of [%s] cannot exceed 255 characters
custom_field_textarea_length_tip=The length of [%s] cannot exceed 1000 characters
#å³è
relate_source_id_not_blank=Source id cannot be empty
relate_source_id_length_range=The association source ID must be between {min} and {max}
relate_source_type_not_blank=The associated resource type cannot be empty

api_import_schedule=API import schedule

project.description.length_range=The description must be between {min} and {max} characters
api_test_environment_datasource_connect_failed=Data source connection failed

permission.api_definition.delete_and_recover=Delete/Recover
permission.service_integration.reset=Reset
ms_url_not_available=The resource pool cannot access the current site
api_key_not_exist=API key does not exist
current_user_can_not_operation_api_key=The current user cannot operate the API key

#å¨å±è¯·æ±å¤´
global_request_header=Global request header

url_format_error=Please check if the Swagger URL is entered correctly!

swagger_version_error=Swagger version not supported, please check if it is version 3.0!

test_plan=Test Plan ID

#user_view
internal_user_view_permission_error=System views cannot be deleted!
user_view.all_data=All data
user_view.my_follow=I followed
user_view.my_create=I created
user_view.my_review=I review
user_view.archived=Archived
user_view_exist=The view already exists

#task_error_message
task_error_message.invalid_resource_pool=There is no resource pool available
task_error_message.case_not_exist=The case doesn't exist