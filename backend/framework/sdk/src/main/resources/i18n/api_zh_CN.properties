#moduleï¼ApiLoadTest
api_load_test.id.not_blank=IDä¸è½ä¸ºç©º
api_load_test.resource_id.length_range=æ¥å£ç¨ä¾ID/åºæ¯ç¨ä¾IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_load_test.resource_id.not_blank=æ¥å£ç¨ä¾ID/åºæ¯ç¨ä¾IDä¸è½ä¸ºç©º
api_load_test.load_test_id.length_range=æ§è½æµè¯ç¨ä¾IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_load_test.load_test_id.not_blank=æ§è½æµè¯ç¨ä¾IDä¸è½ä¸ºç©º
api_load_test.type.length_range=èµæºç±»å/CASE/SCENARIOé¿åº¦å¿é¡»å¨1-10ä¹é´
api_load_test.type.not_blank=èµæºç±»å/CASE/SCENARIOä¸è½ä¸ºç©º
#moduleï¼ApiTestCaseBlob
api_test_case_blob.api_test_case_id.not_blank=æ¥å£ç¨ä¾pkä¸è½ä¸ºç©º
#moduleï¼ApiDefinitionTemplate
api_definition_template.id.not_blank=æ¨¡çä¸»é®ä¸è½ä¸ºç©º
api_definition_template.name.length_range=APIæ¨¡çåç§°é¿åº¦å¿é¡»å¨1-255ä¹é´
api_definition_template.name.not_blank=APIæ¨¡çåç§°ä¸è½ä¸ºç©º
api_definition_template.system.length_range=æ¯å¦æ¯ç³»ç»æ¨¡çé¿åº¦å¿é¡»å¨1-1ä¹é´
api_definition_template.system.not_blank=æ¯å¦æ¯ç³»ç»æ¨¡çä¸è½ä¸ºç©º
api_definition_template.global.length_range=æ¯å¦æ¯å¬å±æ¨¡çé¿åº¦å¿é¡»å¨1-1ä¹é´
api_definition_template.global.not_blank=æ¯å¦æ¯å¬å±æ¨¡çä¸è½ä¸ºç©º
api_definition_template.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition_template.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
api_definition_template.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition_template.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
#moduleï¼ApiScenario
permission.system_api_scenario.name=åºæ¯
api_scenario.name.repeat=åºæ¯åç§°éå¤
api_scenario.id.not_blank=ä¸è½ä¸ºç©º
api_scenario.name.length_range=åºæ¯åç§°é¿åº¦å¿é¡»å¨1-255ä¹é´
api_scenario.name.not_blank=åºæ¯åç§°ä¸è½ä¸ºç©º
api_scenario.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
api_scenario.update_user.length_range=æ´æ°äººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario.update_user.not_blank=æ´æ°äººä¸è½ä¸ºç©º
api_scenario.priority.length_range=åºæ¯çº§å«/P0/P1ç­é¿åº¦å¿é¡»å¨1-10ä¹é´
api_scenario.priority.not_blank=åºæ¯çº§å«/P0/P1ç­ä¸è½ä¸ºç©º
api_scenario.status.length_range=åºæ¯ç¶æ/æªè§å/å·²å®æ ç­é¿åº¦å¿é¡»å¨1-20ä¹é´
api_scenario.status.not_blank=åºæ¯ç¶æ/æªè§å/å·²å®æ ç­ä¸è½ä¸ºç©º
api_scenario.step_total.not_blank=æ­¥éª¤æ»æ°ä¸è½ä¸ºç©º
api_scenario.pass_rate.not_blank=éè¿çä¸è½ä¸ºç©º
api_scenario.principal.length_range=è´£ä»»äºº/ç¨æ·IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario.principal.not_blank=è´£ä»»äºº/ç¨æ·IDä¸è½ä¸ºç©º
api_scenario.deleted.length_range=å é¤ç¶æé¿åº¦å¿é¡»å¨1-1ä¹é´
api_scenario.deleted.not_blank=å é¤ç¶æä¸è½ä¸ºç©º
api_scenario.environment_group.length_range=æ¯å¦ä¸ºç¯å¢ç»é¿åº¦å¿é¡»å¨1-1ä¹é´
api_scenario.environment_group.not_blank=æ¯å¦ä¸ºç¯å¢ç»ä¸è½ä¸ºç©º
api_scenario.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
api_scenario.request_pass_rate.not_blank=éè¿çä¸è½ä¸ºç©º
api_scenario.request_pass_rate.length_range=éè¿çé¿åº¦å¿é¡»å¨1-20ä¹é´
api_scenario.module_id.length_range=æ¨¡åIDé¿åº¦å¿é¡»å¨1-50ä¹é´
#module: ApiScenarioStep
api_scenario_step.id.not_blank=IDä¸è½ä¸ºç©º
api_scenario_step.id.length_range=æ­¥éª¤IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_step.scenario_id.not_blank=åºæ¯IDä¸è½ä¸ºç©º
api_scenario_step.scenario_id.length_range=åºæ¯IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_step.sort.not_blank=æ­¥éª¤æåºä¸è½ä¸ºç©º
#moduleï¼ApiTestCaseFollow
api_test_case_follow.case_id.length_range=ç¨ä¾IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_test_case_follow.case_id.not_blank=ç¨ä¾IDä¸è½ä¸ºç©º
api_test_case_follow.follow_id.length_range=å³æ³¨äºº/ç¨æ·IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_test_case_follow.follow_id.not_blank=å³æ³¨äºº/ç¨æ·IDä¸è½ä¸ºç©º
#moduleï¼ApiReportBlob
api_report_blob.api_report_id.not_blank=æ¥å£æ¥åIDä¸è½ä¸ºç©º
#moduleï¼ApiFakeErrorConfig
api_fake_error_config.id.not_blank=è¯¯æ¥pkä¸è½ä¸ºç©º
api_fake_error_config.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_fake_error_config.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
api_fake_error_config.update_user.length_range=ä¿®æ¹äººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_fake_error_config.update_user.not_blank=ä¿®æ¹äººä¸è½ä¸ºç©º
api_fake_error_config.name.length_range=è¯¯æ¥åç§°é¿åº¦å¿é¡»å¨1-255ä¹é´
api_fake_error_config.name.not_blank=è¯¯æ¥åç§°ä¸è½ä¸ºç©º
api_fake_error_config.match_type.length_range=å¹éç±»åé¿åº¦å¿é¡»å¨1-255ä¹é´
api_fake_error_config.match_type.not_blank=å¹éç±»åä¸è½ä¸ºç©º
api_fake_error_config.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_fake_error_config.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
#moduleï¼ApiScenarioReport
api_scenario_report.id.not_blank=åºæ¯æ¥åpkä¸è½ä¸ºç©º
api_scenario_report.name.length_range=æ¥ååç§°é¿åº¦å¿é¡»å¨1-255ä¹é´
api_scenario_report.name.not_blank=æ¥ååç§°ä¸è½ä¸ºç©º
api_scenario_report.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_report.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
api_scenario_report.deleted.length_range=å é¤æ è¯é¿åº¦å¿é¡»å¨1-1ä¹é´
api_scenario_report.deleted.not_blank=å é¤æ è¯ä¸è½ä¸ºç©º
api_scenario_report.update_user.length_range=ä¿®æ¹äººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_report.update_user.not_blank=ä¿®æ¹äººä¸è½ä¸ºç©º
api_scenario_report.status.length_range=æ¥åç¶æ/SUCCESS/ERRORé¿åº¦å¿é¡»å¨1-20ä¹é´
api_scenario_report.status.not_blank=æ¥åç¶æ/SUCCESS/ERRORä¸è½ä¸ºç©º
api_scenario_report.trigger_mode.length_range=è§¦åæ¹å¼é¿åº¦å¿é¡»å¨1-20ä¹é´
api_scenario_report.trigger_mode.not_blank=è§¦åæ¹å¼ä¸è½ä¸ºç©º
api_scenario_report.run_mode.length_range=æ§è¡æ¨¡å¼é¿åº¦å¿é¡»å¨1-20ä¹é´
api_scenario_report.run_mode.not_blank=æ§è¡æ¨¡å¼ä¸è½ä¸ºç©º
api_scenario_report.pool_id.length_range=èµæºæ± é¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_report.pool_id.not_blank=èµæºæ± ä¸è½ä¸ºç©º
api_scenario_report.integrated.length_range=æ¯å¦æ¯éææ¥åé¿åº¦å¿é¡»å¨1-1ä¹é´
api_scenario_report.integrated.not_blank=æ¯å¦æ¯éææ¥åä¸è½ä¸ºç©º
api_scenario_report.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_report.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
api_scenario_report.scenario_id.length_range=åºæ¯IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_report.scenario_id.not_blank=åºæ¯IDä¸è½ä¸ºç©º
#moduleï¼ApiScenarioEnv
api_scenario_environment.id.not_blank=åºæ¯ç¯å¢pkä¸è½ä¸ºç©º
api_scenario_environment.api_scenario_id.length_range=åºæ¯IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_environment.api_scenario_id.not_blank=åºæ¯IDä¸è½ä¸ºç©º
api_scenario_environment.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_environment.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
#moduleï¼ApiDefinition
api_definition.id.not_blank=æ¥å£pkä¸è½ä¸ºç©º
api_definition.id.length_range=æ¥å£pké¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
api_definition.update_user.length_range=ä¿®æ¹äººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition.update_user.not_blank=ä¿®æ¹äººä¸è½ä¸ºç©º
api_definition.deleted.length_range=å é¤ç¶æé¿åº¦å¿é¡»å¨1-1ä¹é´
api_definition.deleted.not_blank=å é¤ç¶æä¸è½ä¸ºç©º
api_definition.name.length_range=æ¥å£åç§°é¿åº¦å¿é¡»å¨1-255ä¹é´
api_definition.name.not_blank=æ¥å£åç§°ä¸è½ä¸ºç©º
api_definition.method.length_range=æ¥å£ç±»åé¿åº¦å¿é¡»å¨1-20ä¹é´
api_definition.method.not_blank=æ¥å£ç±»åä¸è½ä¸ºç©º
api_definition.protocol.length_range=æ¥å£åè®®é¿åº¦å¿é¡»å¨1-20ä¹é´
api_definition.protocol.not_blank=æ¥å£åè®®ä¸è½ä¸ºç©º
api_definition.status.length_range=æ¥å£ç¶æ/è¿è¡ä¸­/å·²å®æé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition.status.not_blank=æ¥å£ç¶æ/è¿è¡ä¸­/å·²å®æä¸è½ä¸ºç©º
api_definition.sync_enable.length_range=æ¯å¦å¯ç¨åæ­¥é¿åº¦å¿é¡»å¨1-1ä¹é´
api_definition.sync_enable.not_blank=æ¯å¦å¯ç¨åæ­¥ä¸è½ä¸ºç©º
api_definition.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
api_definition.latest.length_range=æ¯å¦ä¸ºææ°çæ¬ 0:å¦ï¼1:æ¯é¿åº¦å¿é¡»å¨1-1ä¹é´
api_definition.latest.not_blank=æ¯å¦ä¸ºææ°çæ¬ 0:å¦ï¼1:æ¯ä¸è½ä¸ºç©º
api_definition.module_id.not_blank=æ¨¡åIDä¸è½ä¸ºç©º
api_definition.module_id.length_range=æ¨¡åIDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition.version_id.not_blank=çæ¬IDä¸è½ä¸ºç©º
api_definition.version_id.length_range=çæ¬IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition.ref_id.not_blank=å¼ç¨IDä¸è½ä¸ºç©º
api_definition.ref_id.length_range=å¼ç¨IDå¿é¡»å¨1-50ä¹é´
api_definition.description.length_range=æè¿°é¿åº¦å¿é¡»å¨0-1000ä¹é´
#moduleï¼ApiDefinitionFollow
api_definition_follow.api_definition_id.length_range=æ¥å£IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition_follow.api_definition_id.not_blank=æ¥å£IDä¸è½ä¸ºç©º
api_definition_follow.follow_id.length_range=å³æ³¨äºº/ç¨æ·IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition_follow.follow_id.not_blank=å³æ³¨äºº/ç¨æ·IDä¸è½ä¸ºç©º
#moduleï¼ApiSyncConfig
api_sync_config.id.not_blank=ä¸è½ä¸ºç©º
api_sync_config.resource_id.length_range=API/CASE æ¥æºIDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_sync_config.resource_id.not_blank=API/CASE æ¥æºIDä¸è½ä¸ºç©º
api_sync_config.resource_type.length_range=æ¥æºç±»å/API/CASEé¿åº¦å¿é¡»å¨1-50ä¹é´
api_sync_config.resource_type.not_blank=æ¥æºç±»å/API/CASEä¸è½ä¸ºç©º
api_sync_config.notify_case_creator.length_range=æ¯å¦éç¥ç¨ä¾åå»ºäººé¿åº¦å¿é¡»å¨1-1ä¹é´
api_sync_config.notify_case_creator.not_blank=æ¯å¦éç¥ç¨ä¾åå»ºäººä¸è½ä¸ºç©º
api_sync_config.notify_scenario_creator.length_range=æ¯å¦éç¥åºæ¯åå»ºäººé¿åº¦å¿é¡»å¨1-1ä¹é´
api_sync_config.notify_scenario_creator.not_blank=æ¯å¦éç¥åºæ¯åå»ºäººä¸è½ä¸ºç©º
api_sync_config.sync_enable.length_range=æ¯å¦åæ­¥ç¨ä¾é¿åº¦å¿é¡»å¨1-1ä¹é´
api_sync_config.sync_enable.not_blank=æ¯å¦åæ­¥ç¨ä¾ä¸è½ä¸ºç©º
#moduleï¼ApiScenarioReportLog
api_scenario_report_log.report_id.not_blank=è¯·æ±èµæº idä¸è½ä¸ºç©º
#moduleï¼ApiScenarioFollow
api_scenario_follow.api_scenario_id.length_range=åºæ¯IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_follow.api_scenario_id.not_blank=åºæ¯IDä¸è½ä¸ºç©º
api_scenario_follow.follow_id.length_range=å³æ³¨äºº/ç¨æ·IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_follow.follow_id.not_blank=å³æ³¨äºº/ç¨æ·IDä¸è½ä¸ºç©º
#moduleï¼ApiDefinitionEnv
api_definition_env.id.not_blank=IDä¸è½ä¸ºç©º
api_definition_env.create_user.length_range=ç¨æ·IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition_env.create_user.not_blank=ç¨æ·IDä¸è½ä¸ºç©º
api_definition_env.environment_id.length_range=ç¯å¢IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition_env.environment_id.not_blank=ç¯å¢IDä¸è½ä¸ºç©º
#moduleï¼ApiScenarioBlob
api_scenario_blob.api_scenario_id.not_blank=åºæ¯pkä¸è½ä¸ºç©º
#moduleï¼ApiTestCase
api_test_case.id.not_blank=æ¥å£ç¨ä¾pkä¸è½ä¸ºç©º
api_test_case.name.length_range=æ¥å£ç¨ä¾åç§°é¿åº¦å¿é¡»å¨1-255ä¹é´
api_test_case.name.not_blank=æ¥å£ç¨ä¾åç§°ä¸è½ä¸ºç©º
api_test_case.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_test_case.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
api_test_case.update_user.length_range=æ´æ°äººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_test_case.update_user.not_blank=æ´æ°äººä¸è½ä¸ºç©º
api_test_case.deleted.length_range=å é¤æ è¯é¿åº¦å¿é¡»å¨1-1ä¹é´
api_test_case.deleted.not_blank=å é¤æ è¯ä¸è½ä¸ºç©º
api_test_case.priority.length_range=ç¨ä¾ç­çº§é¿åº¦å¿é¡»å¨1-50ä¹é´
api_test_case.tag.length_range=æ ç­¾é¿åº¦å¿é¡»å¨1-64ä¹é´
api_test_case.priority.not_blank=ç¨ä¾ç­çº§ä¸è½ä¸ºç©º
api_test_case.sync_enable.length_range=æ¯å¦å¼å¯åæ­¥é¿åº¦å¿é¡»å¨1-1ä¹é´
api_test_case.sync_enable.not_blank=æ¯å¦å¼å¯åæ­¥ä¸è½ä¸ºç©º
api_test_case.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_test_case.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
api_test_case.api_definition_id.length_range=æ¥å£IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_test_case.api_definition_id.not_blank=æ¥å£IDä¸è½ä¸ºç©º
api_test_case.principal.length_range=è´£ä»»äººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_test_case.principal.not_blank=è´£ä»»äººä¸è½ä¸ºç©º
api_test_case.version_id.not_blank=çæ¬IDä¸è½ä¸ºç©º
api_test_case.version_id.length_range=çæ¬IDé¿åº¦å¿é¡»å¨1-50ä¹é´
#moduleï¼ApiDefinitionMock
api_definition_mock.id.not_blank=mock pkä¸è½ä¸ºç©º
api_definition_mock.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition_mock.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
api_definition_mock.name.length_range=mock åç§°é¿åº¦å¿é¡»å¨1-255ä¹é´
api_definition_mock.name.not_blank=mock åç§°ä¸è½ä¸ºç©º
api_definition_mock.expect_num.length_range=mockç¼å·é¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition_mock.expect_num.not_blank=mockç¼å·ä¸è½ä¸ºç©º
api_definition_mock.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition_mock.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
api_definition_mock.api_definition_id.length_range=æ¥å£IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition_mock.api_definition_id.not_blank=æ¥å£IDä¸è½ä¸ºç©º
#moduleï¼ApiScenarioReportStep
api_scenario_report_step.step_id.not_blank=æ­¥éª¤IDä¸è½ä¸ºç©º
api_scenario_report_step.step_id.length_range=æ­¥éª¤IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_report_step.report_id.not_blank=æ¥åIDä¸è½ä¸ºç©º
api_scenario_report_step.report_id.length_range=æ¥åIDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_report_step.sort.not_blank=æ­¥éª¤æåºä¸è½ä¸ºç©º
api_scenario_report_step.step_type.not_blank=æ­¥éª¤ç±»åä¸è½ä¸ºç©º
api_scenario_report_step.parent_id.not_blank=ç¶çº§IDä¸è½ä¸ºç©º
api_scenario_report_step.parent_id.length_range=ç¶çº§IDé¿åº¦å¿é¡»å¨1-50ä¹é´
#moduleï¼ApiReport
api_report.id.not_blank=æ¥å£ç»ææ¥åpkä¸è½ä¸ºç©º
api_report.name.length_range=æ¥å£æ¥ååç§°é¿åº¦å¿é¡»å¨1-255ä¹é´
api_report.name.not_blank=æ¥å£æ¥ååç§°ä¸è½ä¸ºç©º
api_report.resource_id.length_range=èµæºID/api_definition_id/api_test_case_idé¿åº¦å¿é¡»å¨1-50ä¹é´
api_report.resource_id.not_blank=èµæºID/api_definition_id/api_test_case_idä¸è½ä¸ºç©º
api_report.test_plan_id.not_blank=æµè¯è®¡åIDä¸è½ä¸ºç©º
api_report.create_user.length_range=åå»ºäººIDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_report.create_user.not_blank=åå»ºäººIDä¸è½ä¸ºç©º
api_report.update_user.length_range=ä¿®æ¹äººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_report.update_user.not_blank=ä¿®æ¹äººä¸è½ä¸ºç©º
api_report.deleted.length_range=å é¤ç¶æé¿åº¦å¿é¡»å¨1-1ä¹é´
api_report.deleted.not_blank=å é¤ç¶æä¸è½ä¸ºç©º
api_report.status.length_range=ç»æç¶æé¿åº¦å¿é¡»å¨1-50ä¹é´
api_report.status.not_blank=ç»æç¶æä¸è½ä¸ºç©º
api_report.start_time.not_blank=å¼å§æ¶é´ä¸è½ä¸ºç©º
api_report.request_duration.not_blank=è¯·æ±æ¶é¿ä¸è½ä¸ºç©º
api_report.run_mode.length_range=æ§è¡æ¨¡å/API/CASE/API_PLANé¿åº¦å¿é¡»å¨1-20ä¹é´
api_report.run_mode.not_blank=æ§è¡æ¨¡å/API/CASE/API_PLANä¸è½ä¸ºç©º
api_report.pool_id.length_range=èµæºæ± é¿åº¦å¿é¡»å¨1-50ä¹é´
api_report.pool_id.not_blank=èµæºæ± ä¸è½ä¸ºç©º
api_report.trigger_mode.length_range=è§¦åæ¨¡å¼/æå¨/æ¹é/å®æ¶ä»»å¡/JENKINSé¿åº¦å¿é¡»å¨1-50ä¹é´
api_report.trigger_mode.not_blank=è§¦åæ¨¡å¼/æå¨/æ¹é/å®æ¶ä»»å¡/JENKINSä¸è½ä¸ºç©º
api_report.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_report.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
api_report.integrated_report_id.length_range=éææ¥åid/api_scenario_report_idé¿åº¦å¿é¡»å¨1-50ä¹é´
api_report.integrated_report_id.not_blank=éææ¥åid/api_scenario_report_idä¸è½ä¸ºç©º
api_report.integrated.length_range=æ¯å¦ä¸ºéææ¥åï¼é»è®¤å¦é¿åº¦å¿é¡»å¨1-1ä¹é´
api_report.integrated.not_blank=æ¯å¦ä¸ºéææ¥åï¼é»è®¤å¦ä¸è½ä¸ºç©º
api_report.error_count.not_blank=å¤±è´¥æ°éä¸è½ä¸ºç©º
api_report.fake_error_count.not_blank=è¯¯æ¥æ°éä¸è½ä¸ºç©º
api_report.pending_count.not_blank=æªæ§è¡æ°éä¸è½ä¸ºç©º
api_report.success_count.not_blank=æåæ°éä¸è½ä¸ºç©º
api_report.assertion_count.not_blank=æ­è¨æ°éä¸è½ä¸ºç©º
api_report.assertion_success_count.not_blank=æ­è¨æåæ°éä¸è½ä¸ºç©º
api_report.request_error_rate.not_blank=è¯·æ±éè¯¯çä¸è½ä¸ºç©º
api_report.request_error_rate.length_range=è¯·æ±éè¯¯çé¿åº¦å¿é¡»å¨1-20ä¹é´
api_report.request_pending_rate.not_blank=è¯·æ±æªæ§è¡çä¸è½ä¸ºç©º
api_report.request_pending_rate.length_range=è¯·æ±æªæ§è¡çé¿åº¦å¿é¡»å¨1-20ä¹é´
api_report.request_fake_error_rate.not_blank=è¯·æ±è¯¯æ¥çä¸è½ä¸ºç©º
api_report.request_fake_error_rate.length_range=è¯·æ±è¯¯æ¥çé¿åº¦å¿é¡»å¨1-20ä¹é´
api_report.request_pass_rate.not_blank=è¯·æ±éè¿çä¸è½ä¸ºç©º
api_report.request_pass_rate.length_range=è¯·æ±éè¿çé¿åº¦å¿é¡»å¨1-20ä¹é´
api_report.assertion_pass_rate.not_blank=æ­è¨éè¿çä¸è½ä¸ºç©º
api_report.assertion_pass_rate.length_range=æ­è¨éè¿çé¿åº¦å¿é¡»å¨1-20ä¹é´
#moduleï¼ApiReportStep
api_report_step.step_id.not_blank=æ­¥éª¤IDä¸è½ä¸ºç©º
api_report_step.step_id.length_range=æ­¥éª¤IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_report_step.report_id.not_blank=æ¥åIDä¸è½ä¸ºç©º
api_report_step.report_id.length_range=æ¥åIDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_report_step.sort.not_blank=æ­¥éª¤æåºä¸è½ä¸ºç©º
api_report_step.step_type.not_blank=æ­¥éª¤ç±»åä¸è½ä¸ºç©º
#moduleï¼ApiDefinitionModule
api_definition_module.id.not_blank=æ¥å£æ¨¡åpkä¸è½ä¸ºç©º
api_definition_module.update_user.length_range=ä¿®æ¹äººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition_module.update_user.not_blank=ä¿®æ¹äººä¸è½ä¸ºç©º
api_definition_module.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition_module.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
api_definition_module.name.length_range=æ¨¡ååç§°é¿åº¦å¿é¡»å¨1-255ä¹é´
api_definition_module.name.not_blank=æ¨¡ååç§°ä¸è½ä¸ºç©º
api_definition_module.protocol.length_range=åè®®é¿åº¦å¿é¡»å¨1-20ä¹é´
api_definition_module.protocol.not_blank=åè®®ä¸è½ä¸ºç©º
api_definition_module.parent_id.length_range=ç¶çº§IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition_module.parent_id.not_blank=ç¶çº§IDä¸è½ä¸ºç©º
api_definition_module.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition_module.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
api_definition_module.pos.not_blank=æ¨¡åä½ç½®ä¸è½ä¸ºç©º
api_definition_module.api.all=å¨é¨æ¥å£
#moduleï¼ApiDefinitionMockConfig
api_definition_mock_config.api_definition_mock_id.not_blank=æ¥å£mock pkä¸è½ä¸ºç©º
#moduleï¼ApiScenarioModule
api_scenario_module.id.not_blank=åºæ¯æ¨¡åpkä¸è½ä¸ºç©º
api_scenario_module.name.length_range=æ¨¡ååç§°é¿åº¦å¿é¡»å¨1-255ä¹é´
api_scenario_module.name.not_blank=æ¨¡ååç§°ä¸è½ä¸ºç©º
api_scenario_module.update_user.length_range=æ´æ°äººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_module.update_user.not_blank=æ´æ°äººä¸è½ä¸ºç©º
api_scenario_module.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_module.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
api_scenario_module.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_module.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
api_scenario_module.parent_id.length_range=ç¶çº§IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_module.parent_id.not_blank=ç¶çº§IDä¸è½ä¸ºç©º
#moduleï¼ApiScenarioReference
api_scenario_reference.id.not_blank=å¼ç¨å³ç³»pkä¸è½ä¸ºç©º
api_scenario_reference.api_scenario_id.length_range=åºæ¯IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_reference.api_scenario_id.not_blank=åºæ¯IDä¸è½ä¸ºç©º
api_scenario_reference.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_reference.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
api_scenario_reference.reference_id.length_range=å¼ç¨æ­¥éª¤IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_reference.reference_id.not_blank=å¼ç¨æ­¥éª¤IDä¸è½ä¸ºç©º
#moduleï¼ApiScenarioReportDetail
api_scenario_report_detail.id.not_blank=IDä¸è½ä¸ºç©º
api_scenario_report_detail.report_id.length_range=æ¥åIDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_report_detail.report_id.not_blank=æ¥åIDä¸è½ä¸ºç©º
api_scenario_report_detail.resource_id.length_range=åºæ¯ä¸­åä¸ªæ­¥éª¤è¯·æ±å¯ä¸æ è¯é¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_report_detail.resource_id.not_blank=åºæ¯ä¸­åä¸ªæ­¥éª¤è¯·æ±å¯ä¸æ è¯ä¸è½ä¸ºç©º
api_scenario_report_detail.request_time.not_blank=è¯·æ±æ¶é´ä¸è½ä¸ºç©º
api_scenario_report_detail.response_size.not_blank=ååºå¤§å°ä¸è½ä¸ºç©º
#moduleï¼ApiDefinitionBlob
api_definition_blob.api_definition_id.not_blank=æ¥å£ID/ ä¸å¯¹ä¸å³ç³»ä¸è½ä¸ºç©º
#module: ApiDebug
api_debug.id.not_blank=IDä¸è½ä¸ºç©º
api_debug.id.length_range=æ¥å£IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_debug.name.not_blank=æ¥å£åç§°ä¸è½ä¸ºç©º
api_debug.name.length_range=æ¥å£åç§°é¿åº¦å¿é¡»å¨1-255ä¹é´
api_debug.protocol.not_blank=åè®®ä¸è½ä¸ºç©º
api_debug.protocol.length_range=åè®®é¿åº¦å¿é¡»å¨1-20ä¹é´
api_debug.method.length_range=è¯·æ±ç±»åé¿åº¦å¿é¡»å¨0-20ä¹é´
api_debug.path.length_range=æ¥å£è·¯å¾é·åº¦å¿é å¨0-500ä¹é
api_debug.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
api_debug.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_debug.module_id.not_blank=æ¨¡åIDä¸è½ä¸ºç©º
api_debug.module_id.length_range=æ¨¡åIDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_debug.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
api_debug.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
api_debug.update_user.not_blank=ä¿®æ¹äººä¸è½ä¸ºç©º
api_debug.update_user.length_range=ä¿®æ¹äººé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
#module: ApiDebugModule
api_debug_module.id.not_blank=IDä¸è½ä¸ºç©º
api_debug_module.id.length_range=æ¨¡åIDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_debug_module.name.not_blank=æ¨¡ååç§°ä¸è½ä¸ºç©º
api_debug_module.name.length_range=æ¨¡ååç§°é¿åº¦å¿é¡»å¨1-255ä¹é´
api_debug_module.parent_id.not_blank=ç¶çº§IDä¸è½ä¸ºç©º
api_debug_module.parent_id.length_range=ç¶çº§IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_debug_module.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
api_debug_module.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_debug_module.pos.not_blank=æ¨¡åä½ç½®ä¸è½ä¸ºç©º
api_debug_module.name.not_contain_slash=åç§°ä¸­ä¸è½åå«æ­£åææ 
api_debug_module.unplanned_request=æªè§åè¯·æ±
api_unplanned_request=æªè§åæ¥å£
api_unplanned_scenario=æªè§ååºæ¯
#module: ApiEnvironmentConfig
api_environment_config.id.not_blank=IDä¸è½ä¸ºç©º
api_environment_config.environment_id.length_range=ç¯å¢IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_environment_config.environment_id.not_blank=ç¯å¢IDä¸è½ä¸ºç©º
#moduleï¼ApiDefinitionCustomField
api_definition_custom_field.api_id.not_blank=æ¥å£IDä¸è½ä¸ºç©º
api_definition_custom_field.field_id.not_blank=èªå®ä¹å­æ®µIDä¸è½ä¸ºç©º
api_module.not.exist=æ¨¡åä¸å­å¨
permission.api.name=æ¥å£æµè¯
permission.api_mock.name=æ¥å£ MOCK
api_debug_exist=è¯·æ±å·²å­å¨
follow=å³æ³¨
unfollow=åæ¶å³æ³¨
api_definition_exist=æ¥å£å·²å­å¨
api_definition_mock_exist=æ¥å£ MOCK å·²å­å¨
execute_resource_pool_not_config_error=è¯·å¨ãé¡¹ç®ç®¡ç-åºç¨è®¾ç½®-æ¥å£æµè¯ãä¸­éæ©èµæºæ± 
resource_pool_execute_error=èµæºæ± è°ç¨å¤±è´¥
api_swagger_url_error=Swagger urlæ æ³è¿é
api_scenario_exist=åºæ¯å·²å­å¨
schedule_not_exist=å®æ¶ä»»å¡ä¸å­å¨
api_case_report_not_exist=ç¨ä¾æ¥åä¸å­å¨
api_scenario_report_not_exist=åºæ¯æ¥åä¸å­å¨
permission.api_plugin.name=æ¥å£æä»¶
permission.api_step.name=æ­¥éª¤
api_response_name_code_unique=ååºåç§° + ååºç éè¦å¯ä¸

#moduleï¼ApiScenarioCsv
api_scenario_csv.file_id.not_blank=æä»¶IDä¸è½ä¸ºç©º
api_scenario_csv.file_id.length_range=æä»¶IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_csv.scenario_id.not_blank=åºæ¯IDä¸è½ä¸ºç©º
api_scenario_csv.scenario_id.length_range=åºæ¯IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_csv.name.not_blank=csvåç§°ä¸è½ä¸ºç©º
api_scenario_csv.name.length_range=csvåç§°é¿åº¦å¿é¡»å¨1-255ä¹é´
api_scenario_csv.scope.not_blank=csvä½ç¨åä¸è½ä¸ºç©º
api_scenario_csv.scope.length_range=csvä½ç¨åé¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_csv.association.not_blank=csvæ¯å¦æ¯å³è
api_scenario_csv.encoding.not_blank=csvç¼ç ä¸è½ä¸ºç©º
api_scenario_csv.encoding.length_range=csvç¼ç é¿åº¦å¿é¡»å¨1-50ä¹é´
api_scenario_csv.random.not_blank=æ¯å¦éæº
api_scenario_csv.ignore_first_line.not_blank=æ¯å¦å¿½ç¥ç¬¬ä¸è¡
api_scenario_csv.allow_quoted_data.not_blank=æ¯å¦åè®¸å¸¦å¼å·
api_scenario_csv.recycle_on_eof.not_blank=æ¯å¦å¾ªç¯
api_scenario_csv.stop_thread_on_eof.not_blank=æ¯å¦åæ­¢çº¿ç¨

#module: ApiDefinitionSwagger
api_definition_swagger.id.not_blank=ä¸»é®ä¸è½ä¸ºç©º
api_definition_swagger.id.length_range=ä¸»é®é¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition_swagger.name.not_blank=åç§°ä¸è½ä¸ºç©º
api_definition_swagger.name.length_range=åç§°é¿åº¦å¿é¡»å¨1-255ä¹é´
api_definition_swagger.swagger_url.not_blank=swagger urlä¸è½ä¸ºç©º
api_definition_swagger.swagger_url.length_range=swagger urlé¿åº¦å¿é¡»å¨1-500ä¹é´
api_definition_swagger.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
api_definition_swagger.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨1-50ä¹é´
api_definition_swagger.module_id.length_range=æ¨¡åIDé¿åº¦å¿é¡»å¨0-50ä¹é´

api_test_case.env_id.length_range=ç¯å¢IDé¿åº¦å¿é¡»å¨0-50ä¹é´
api_test_case.status.length_range=ç¨ä¾ç¶æé¿åº¦å¿é¡»å¨0-20ä¹é´
api_test_case.priority.length_range_=ç¨ä¾ç­çº§é¿åº¦å¿é¡»å¨0-50ä¹é´
api_test_case.environment_id.length_range=ç¯å¢IDé¿åº¦å¿é¡»å¨0-50ä¹é´
api_scenario.target_module_id.length_range=ç®æ æ¨¡åIDé¿åº¦å¿é¡»å¨0-50ä¹é´

api_scenario.group_id.length_range=ç¯å¢ç»IDé¿åº¦å¿é¡»å¨0-50ä¹é´
api_scenario.environment_id.length_range=ç¯å¢IDé¿åº¦å¿é¡»å¨0-50ä¹é´

api_report_default_env=é»è®¤ç¯å¢
tags_size_large_than=æ ç­¾æ°éè¶è¿{0}ä¸ª

no_permission_to_resource=æ²¡ææéè®¿é®è¯¥èµæº
api_scenario_circular_reference_error=åºæ¯å­å¨å¾ªç¯å¼ç¨

api_import_url_is_exist=å¯¼å¥çURLå·²å­å¨
report.status.success=æå
report.status.error=å¤±è´¥
report.status.pending=æªæ§è¡
report.status.fake_error=è¯¯æ¥
api_definition.status.ongoing=è¿è¡ä¸­
api_definition.status.completed=å·²å®æ
api_definition.status.abandoned=å·²åºå¼
api_definition.status.continuous=èè°ä¸­

api_test_case.clear.api_change=å¿½ç¥æ¬æ¬¡åæ´å·®å¼
api_test_case.ignore.api_change=å¿½ç¥å¨é¨åæ´å·®å¼

curl_script_is_empty=cURLèæ¬ä¸è½ä¸ºç©º
curl_script_is_invalid=cURLèæ¬æ ¼å¼ä¸æ­£ç¡®
curl_raw_content_is_invalid=rawåå®¹æ ¼å¼ä¸æ­£ç¡®

api_batch_task_name=ç¨ä¾æ¹éæ§è¡ä»»å¡
api_scenario_batch_task_name=åºæ¯æ¹éæ§è¡

# api doc share i18n
api_doc_share.not_exist=æ¥å£ææ¡£åäº«ä¸å­å¨
api_doc_share.id.not_blank=ä¸»é®ä¸è½ä¸ºç©º
api_doc_share.id.length_range=ä¸»é®é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
api_doc_share.name.not_blank=åç§°ä¸è½ä¸ºç©º
api_doc_share.name.length_range=åç§°é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
api_doc_share.is_private.not_blank=æ¯å¦å¬å¼ä¸è½ä¸ºç©º
api_doc_share.is_private.length_range=æ¯å¦å¬å¼é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
api_doc_share.allow_export.not_blank=åè®¸å¯¼åºä¸è½çºç©º
api_doc_share.allow_export.length_range=åè®¸å¯¼åºé·åº¦å¿é å¨{min}å{max}ä¹é´
api_doc_share.api_range.not_blank=æ¥å£èå´ä¸è½ä¸ºç©º
api_doc_share.api_range.length_range=æ¥å£èå´é¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
api_doc_share.create_user.not_blank=åå»ºäººä¸è½ä¸ºç©º
api_doc_share.create_user.length_range=åå»ºäººé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
api_doc_share.project_id.not_blank=é¡¹ç®IDä¸è½ä¸ºç©º
api_doc_share.project_id.length_range=é¡¹ç®IDé¿åº¦å¿é¡»å¨{min}å{max}ä¹é´
api_doc_share.name_duplicate=åäº«åç§°éå¤