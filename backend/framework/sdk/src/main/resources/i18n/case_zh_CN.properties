# permission
permission.case_management.name=用例管理
permission.functional_case.name=功能用例
permission.functional_case.comment=评论
permission.functional_case.minder=脑图编辑
permission.case_review.name=用例评审
permission.case_review.review=评审
permission.case_review.relevance=关联/取消关联
permission.case_review.start_time=评审周期开始时间不得早于当前时间
permission.case_review.end_time=评审周期结束时间不得早于当前时间
#module：FunctionalCase
functional_case.module.default.name=未规划用例
functional_case.module.default.name.add_error=未规划用例模块下不支持新增模块
functional_case.module.default.name.cut_error=未规划用例模块不能被移动
all.module.default.name.cut_error=全部用例不能被移动
review.module.default.name=未规划评审
functional_case.id.not_blank=ID不能为空
functional_case.num.not_blank=业务ID不能为空
functional_case.custom_num.length_range=自定义业务ID长度必须在1-64之间
functional_case.custom_num.not_blank=自定义业务ID不能为空
functional_case.module_id.length_range=模块ID长度必须在1-50之间
functional_case.module_id.not_blank=模块ID不能为空
functional_case.project_id.length_range=项目ID长度必须在1-50之间
functional_case.project_id.not_blank=项目ID不能为空
functional_case.template_id.not_blank=模板ID不能为空
functional_case.name.length_range=名称长度必须在1-255之间
functional_case.name.not_blank=名称不能为空
functional_case.pos.not_blank=顺序不能为空
functional_case.review_status.length_range=评审状态长度必须在1-64之间
functional_case.review_status.not_blank=评审状态不能为空
functional_case.case_edit_type.length_range=编辑模式长度必须在1-64之间
functional_case.case_edit_type.not_blank=编辑模式不能为空
functional_case.version_id.length_range=版本ID长度必须在1-50之间
functional_case.version_id.not_blank=版本ID不能为空
functional_case.ref_id.length_range=指向初始版本ID必须在1-50之间
functional_case.ref_id.not_blank=指向初始版本ID不能为空
functional_case.last_execute_result.length_range=最近的执行结果长度必须在1-64之间
functional_case.last_execute_result.not_blank=最近的执行结果不能为空
functional_case.deleted.length_range=是否在回收站长度必须在1-1之间
functional_case.deleted.not_blank=是否在回收站不能为空
functional_case.public_case.length_range=是否是公共用例长度必须在1-1之间
functional_case.public_case.not_blank=是否是公共用例不能为空
functional_case.latest.length_range=是否为最新版本长度必须在1-1之间
functional_case.latest.not_blank=是否为最新版本不能为空
functional_case.create_user.length_range=创建人长度必须在1-100之间
functional_case.create_user.not_blank=创建人不能为空
functional_case.cover.not_blank=是否覆盖原用例不能为空
functional_case.file_source.not_blank=用例文件来源不能为空
#module：FunctionalCaseBlob
functional_case_blob.functional_case_id.not_blank=功能用例ID不能为空
#module：FunctionalCaseComment
functional_case_comment.id.not_blank=ID不能为空
functional_case_comment.case_id.length_range=功能用例ID长度必须在1-50之间
functional_case_comment.case_id.not_blank=功能用例ID不能为空
functional_case_comment.create_user.length_range=评论人长度必须在1-50之间
functional_case_comment.create_user.not_blank=评论人不能为空
functional_case_comment.type.length_range=评论类型长度必须在1-64之间
functional_case_comment.type.not_blank=评论类型不能为空
functional_case_comment.content.not_blank=评论内容不能为空
functional_case_comment.event.not_blank=评论类型不能为空
#module：FunctionalCaseModule
functional_case_module.id.not_blank=ID不能为空
functional_case_module.project_id.length_range=项目ID长度必须在1-50之间
functional_case_module.project_id.not_blank=项目ID不能为空
functional_case_module.name.length_range=名称长度必须在1-100之间
functional_case_module.name.not_blank=名称不能为空
functional_case_module.pos.length_range=同一节点下的顺序长度必须在1-10之间
functional_case_module.pos.not_blank=同一节点下的顺序不能为空
functional_case_module.create_user.length_range=创建人长度必须在1-50之间
functional_case_module.create_user.not_blank=创建人不能为空
#module：FunctionalCaseAttachment
functional_case_attachment.case_id.not_blank=功能用例ID不能为空
functional_case_attachment.case_id.length_range=功能用例ID长度必须在1-50之间
functional_case_attachment.file_id.not_blank=文件的ID不能为空
functional_case_attachment.file_id.length_range=文件的ID长度必须在1-50之间
functional_case_attachment.id.not_blank=ID不能为空
functional_case_attachment.id.length_range=ID长度必须在1-50之间
functional_case_attachment.file_name.not_blank=文件名称不能为空
functional_case_attachment.file_name.length_range=文件名称长度必须在1-255之间
functional_case_attachment.size.not_blank=文件大小不能为空
functional_case_attachment.association.not_blank=关联类型不能为空
#module：FunctionalCaseFollow
functional_case_follow.case_id.not_blank=功能用例ID不能为空
functional_case_follow.follow_id.not_blank=关注人ID不能为空
#module：FunctionalCaseRelationshipEdge
functional_case_relationship_edge.id.not_blank=ID不能为空
functional_case_relationship_edge.source_id.length_range=源节点的ID长度必须在1-50之间
functional_case_relationship_edge.source_id.not_blank=源节点的ID不能为空
functional_case_relationship_edge.target_id.length_range=目标节点的ID长度必须在1-50之间
functional_case_relationship_edge.target_id.not_blank=目标节点的ID不能为空
functional_case_relationship_edge.graph_id.length_range=所属关系图的ID长度必须在1-50之间
functional_case_relationship_edge.graph_id.not_blank=所属关系图的ID不能为空
functional_case_relationship_edge.create_user.length_range=创建人长度必须在1-50之间
functional_case_relationship_edge.create_user.not_blank=创建人不能为空
#module：FunctionalCaseTest
functional_case_test.id.not_blank=ID不能为空
functional_case_test.functional_case_id.length_range=功能用例ID长度必须在1-50之间
functional_case_test.functional_case_id.not_blank=功能用例ID不能为空
functional_case_test.source_id.length_range=其他类型用例ID长度必须在1-50之间
functional_case_test.source_id.not_blank=其他类型用例ID不能为空
functional_case_test.source_type.length_range=用例类型长度必须在1-64之间
functional_case_test.source_type.not_blank=用例类型不能为空
functional_test_case.disassociate_type.not_blank=关联的用例类型不能为空
#FunctionalCaseCustomField
functional_case_custom_field.case_id.not_blank=功能用例ID不能为空
functional_case_custom_field.field_id.not_blank=自定义字段ID不能为空
#FunctionalCaseDemand
functional_case_demand.demand_platform.not_blank=需求平台不能为空
functional_case_demand.demand_name.not_blank=需求标题不能为空
functional_case_demand.with_parent.not_blank=与父需求是否关联不能为空
functional_case_demand.parent.not_blank=父需求ID不能为空
functional_case_demand.case_id.not_blank=功能用例ID不能为空
functional_case_demand.id.not_blank=需求ID不能为空
functional_case_demand.demand_name.length_range=需求标题长度必须在1-255之间
functional_case_demand.parent.length_range=父需求ID必须在1-255之间
functional_case_demand.case_id.length_range=功能用例ID必须在1-50之间
functional_case_demand.id.length_range=需求ID必须在1-50之间
#FunctionalCaseFollower
functional_case_follower.case_id.not_blank=功能用例ID不能为空
functional_case_follower.user_id.not_blank=关注人不能为空
#FunctionalMinderExtraNode
functional_minder_extra_node.id.not_blank=脑图节点不能为空
functional_minder_extra_node.parent_id.not_blank=脑图父节点不能为空
functional_minder_extra_node.group_id.not_blank=项目ID不能为空
functional_minder_extra_node.node_data.not_blank=存储脑图节点额外信息不能为空
#module：MinderExtraNode
minder_extra_node.id.not_blank=ID不能为空
minder_extra_node.parent_id.length_range=父节点的ID长度必须在1-50之间
minder_extra_node.parent_id.not_blank=父节点的ID不能为空
minder_extra_node.group_id.length_range=项目ID长度必须在1-50之间
minder_extra_node.group_id.not_blank=项目ID不能为空
minder_extra_node.type.length_range=类型长度必须在1-30之间
minder_extra_node.type.not_blank=类型不能为空
minder_extra_node.case=用例
minder_extra_node.module=模块
minder_extra_node.prerequisite=前置条件
minder_extra_node.steps=步骤描述
minder_extra_node.steps_expected_result=预期结果
minder_extra_node.steps_actual_result=实际结果
minder_extra_node.text_description=文本描述
minder_extra_node.text_expected_result=预期结果
minder_extra_node.description=备注信息
minder_extra_node.text_node_empty=文本节点名称不能为空
minder_extra_node.case_node_empty=用例名称不能为空
mind_import_case_name_empty=部分用例名称为空，校验失败；

#module：CaseReview
case_review.id.not_blank=ID不能为空
case_review.name.length_range=名称长度必须在1-200之间
case_review.name.not_blank=名称不能为空
case_review.module_id.not_blank=模块不能为空
case_review.reviewers.not_empty=默认评审人不能为空
case_review.copy_id.not_blank=用例评审被复制的ID不能为空
case_review.case_review_id.not_blank=用例评审ID不能为空
case_review.user_ids.not_empty=评审人不能为空
case_review.move_mode.not_blank=节点移动类型不能为空
case_review_case.project_id.not_blank=功能用例所属项目ID不能为空
case_review.status.length_range=评审状态长度必须在1-64之间
case_review.status.not_blank=评审状态不能为空
case_review.project_id.length_range=项目ID长度必须在1-50之间
case_review.project_id.not_blank=项目ID不能为空
case_review.create_user.length_range=创建人长度必须在1-50之间
case_review.create_user.not_blank=创建人不能为空
case_review.review_pass_rule.length_range=评审规则长度必须在1-64之间
case_review.review_pass_rule.not_blank=评审规则不能为空
#module：CaseReviewModule
case_review_module.id.not_blank=ID不能为空
case_review_module.project_id.not_blank=项目ID不能为空
case_review_module.name.not_blank=名称不能为空
case_review_module.parent_id.not_blank=父节点ID不能为空
case_review_module.pos.not_blank=同一节点下的顺序不能为空
#module：CaseReviewUser
case_review_user.review_id.not_blank=评审ID不能为空
case_review_user.user_id.not_blank=评审人ID不能为空
#module：CaseReviewHistory
case_review_history.id.not_blank=ID不能为空
case_review_history.id.length_range=ID长度必须在1-50之间
case_review_history.review_id.not_blank=评审ID不能为空
case_review_history.review_id.length_range=评审ID长度必须在1-50之间
case_review_history.case_id.not_blank=功能用例ID不能为空
case_review_history.case_id.length_range=功能用例ID长度必须在1-50之间
case_review_history.status.not_blank=评审结果不能为空
case_review_history.status.length_range=评审结果长度必须在1-64之间
case_review_history.deleted.not_blank=是否是取消关联或评审被删除的不能为空
case_review_history.abandoned.not_blank=是否是废弃的评审记录不能为空
#module：CaseReviewFunctionalCase
case_review_functional_case.id.not_blank=ID不能为空
case_review_functional_case.review_id.length_range=评审ID长度必须在1-50之间
case_review_functional_case.review_id.not_blank=评审ID不能为空
case_review_functional_case.case_id.length_range=用例ID长度必须在1-50之间
case_review_functional_case.case_id.not_blank=用例ID不能为空
case_review_functional_case.status.length_range=评审状态长度必须在1-64之间
case_review_functional_case.status.not_blank=评审状态不能为空
case_review_functional_case.create_user.length_range=创建人长度必须在1-50之间
case_review_functional_case.create_user.not_blank=创建人不能为空
case_review_functional_case.deleted.length_range=关联的用例是否放入回收站长度必须在1-1之间
case_review_functional_case.deleted.not_blank=关联的用例是否放入回收站不能为空
#module：CaseReviewFunctionalCaseUser
case_review_functional_case_user.case_id.length_range=功能用例和评审中间表的ID长度必须在1-50之间
case_review_functional_case_user.case_id.not_blank=功能用例和评审中间表的ID不能为空
case_review_functional_case_user.review_id.length_range=评审ID长度必须在1-50之间
case_review_functional_case_user.review_id.not_blank=评审ID不能为空
case_review_functional_case_user.user_id.length_range=评审人ID长度必须在1-50之间
case_review_functional_case_user.user_id.not_blank=评审人ID不能为空
#CaseReviewFunctionalCaseArchive
case_review_functional_case_archive.review_id.not_blank=评审ID不能为空
case_review_functional_case_archive.review_id.length_range=评审ID长度必须在1-50之间
case_review_functional_case_archive.case_id.not_blank=功能用例ID不能为空
case_review_functional_case_archive.case_id.length_range=功能用例ID长度必须在1-50之间
#module：CaseReviewFollower
case_review_follower.review_id.not_blank=评审ID不能为空
case_review_follower.follow_id.not_blank=关注人不能为空
#module：CustomFieldTestCase
custom_field_test_case.resource_id.not_blank=资源ID不能为空
custom_field_test_case.field_id.not_blank=字段ID不能为空
default_template_not_found=默认模板不存在
#comment
case_comment.case_is_null=功能用例不存在
case_comment.parent_id_is_null=当前回复的评论id为空
case_comment.parent_case_is_null=当前回复的评论不存在
case_comment.reply_user_is_null=回复的用户为空
case_comment.id_is_null=当前评论id为空
case_comment.user_self=只能删除自己的评论
un_follow_functional_case=取消关注用例
follow_functional_case=关注用例
tags_length_large_than=标签数量超过{0}个
#module
case_module.not.exist=用例模块不存在
file.transfer.failed=文件转存失败
#case
case.demand.not.exist=需求不存在
case.demand.name.not.exist=需求名称不能为空
case.demand.id.not.exist=需求id不能为空
case_review.prepared=未开始
case_review.underway=进行中
case_review.completed=已完成
case_review.archived=已归档
case_review.single=单人评审
case_review.multiple=多人评审
case_review.not.exist=用例评审不存在
case_review_content.not.exist = 评审意见不能为空
case_review_history.system=系统触发
case_review.viewFlag.not_blank=查看标识不能为空
functional_case_relationship_edge.type.not_blank=类型不能为空
cycle_relationship=关联后存在循环依赖，请检查依赖关系
case_review_user=您没有评审权限
#minder
case.minder.all.case=全部用例

case.minder.status.success=成功
case.minder.status.error=失败
case.minder.status.blocked=阻塞

case.review.status.un_reviewed=未评审
case.review.status.under_reviewed=评审中
case.review.status.pass=已通过
case.review.status.un_pass=不通过
case.review.status.re_reviewed=重新提审
case.execute.status.pending=未执行
case.execute.status.success=成功
case.execute.status.error=失败
case.execute.status.blocked=阻塞
functional_case_comment_template=【用例评论：%s（%s）】\n%s\n
functional_case_execute_comment_template=【执行评论：%s %s（%s）】\n%s\n
functional_case_review_comment_template=【评审评论：%s %s（%s）】\n%s\n
#import
case.find_file_error=找不到该文件
functional_case_xmind_template=思维导图用例模版
download_template_failed=下载思维导图模版失败
functional_case=功能用例
xmind_prerequisite=前置条件
xmind_description=备注
xmind_tags=标签
xmind_textDescription=文本描述
xmind_expectedResult=预期结果
xmind_step=步骤
xmind_stepDescription=步骤描述
# case export columns
case.export.system.columns.name=用例名称
case.export.system.columns.id=ID
case.export.system.columns.prerequisite=前置条件
case.export.system.columns.module=所属模块
case.export.system.columns.text_description=步骤描述
case.export.system.columns.expected_result=预期结果
case.export.system.other.columns.last_execute_result=执行结果
case.export.system.other.columns.review_status=评审结果
case.export.system.other.columns.create_user=创建人
case.export.system.other.columns.create_time=创建时间
case.export.system.other.columns.update_user=更新人
case.export.system.other.columns.update_time=更新时间
case.export.columns.case_edit_type=编辑模式
case.export.system.other.columns.case_comment=用例评论
case.export.system.other.columns.execute_comment=执行评论
case.export.system.other.columns.review_comment=评审评论

export_case_task_stop=停止导出
export_case_task_existed=已有导出任务

demand.sync.job=定时同步需求