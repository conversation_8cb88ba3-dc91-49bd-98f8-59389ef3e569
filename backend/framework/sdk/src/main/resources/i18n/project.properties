fake_error.id.not_blank=ID不能为空
file.module.default.name=未规划文件
fake_error.project_id.length_range=项目ID长度必须在{min}-{max}之间
fake_error.project_id.not_blank=项目ID不能为空
fake_error.create_user.length_range=创建人长度必须在{min}-{max}之间
fake_error.create_user.not_blank=创建人不能为空
fake_error.update_user.length_range=更新人长度必须在{min}-{max}之间
fake_error.update_user.not_blank=更新人不能为空
fake_error.error_code.length_range=错误码长度必须在{min}-{max}之间
fake_error.error_code.not_blank=错误码不能为空
fake_error.match_type.length_range=匹配类型长度必须在{min}-{max}之间
fake_error.match_type.not_blank=匹配类型不能为空
bug_template_extend.id.not_blank=ID不能为空
project_application.project_id.not_blank=项目ID不能为空
project_application.type.not_blank=类型不能为空
custom_function_blob.id.not_blank=ID不能为空
fake_error_blob.id.not_blank=ID不能为空
file_module.id.not_blank=ID不能为空
file_module.project_id.length_range=项目ID长度必须在{min}-{max}之间
file_module.project_id.not_blank=项目ID不能为空
file_module.name.length_range=名称长度必须在{min}-{max}之间
file_module.name.not_blank=名称不能为空
file_repository.connect.error=存储库连接失败
file_repository.not.exist=存储库不存在
file_repository.platform.error=存储库类型不正确
file_repository.id.not_blank=存储库ID不能为空
file_repository.name.not_blank=存储库名称不能为空
file_repository.type.not_blank=存储库类型不能为空
file_repository.token.not_blank=存储库token不能为空
file_repository.url.not_blank=存储库地址不能为空
file_repository.branch.not_blank=存储库分支不能为空
file_repository.file_path.not_blank=存储库文件路径不能为空
file.association.error.type=不支持的文件关联资源类型
file.association.not.exist=文件并未关联
file.association.source.not.exist=文件关联时资源不存在
custom_field_template.id.not_blank=ID不能为空
custom_field_template.field_id.length_range=字段ID长度必须在{min}-{max}之间
custom_field_template.field_id.not_blank=字段ID不能为空
custom_field_template.template_id.length_range=模板ID长度必须在{min}-{max}之间
custom_field_template.template_id.not_blank=模板ID不能为空
custom_field_template.scene.length_range=场景长度必须在{min}-{max}之间
custom_field_template.scene.not_blank=场景不能为空
file_metadata_blob.id.not_blank=ID不能为空
bug_template.id.not_blank=ID不能为空
bug_template.name.length_range=名称长度必须在{min}-{max}之间
bug_template.name.not_blank=名称不能为空
bug_template.create_user.length_range=创建人长度必须在{min}-{max}之间
bug_template.create_user.not_blank=创建人不能为空
bug_template.project_id.length_range=项目ID长度必须在{min}-{max}之间
bug_template.project_id.not_blank=项目ID不能为空
functional_case_template.id.not_blank=ID不能为空
functional_case_template.name.length_range=名称长度必须在{min}-{max}之间
functional_case_template.name.not_blank=名称不能为空
functional_case_template.create_user.length_range=创建人长度必须在{min}-{max}之间
functional_case_template.create_user.not_blank=创建人不能为空
functional_case_template.project_id.length_range=项目ID长度必须在{min}-{max}之间
functional_case_template.project_id.not_blank=项目ID不能为空
api_template.id.not_blank=ID不能为空
api_template.name.length_range=名称长度必须在{min}-{max}之间
api_template.name.not_blank=名称不能为空
api_template.system.length_range=是否是系统字段长度必须在{min}-{max}之间
api_template.system.not_blank=是否是系统字段不能为空
api_template.global.length_range=是否是全局字段长度必须在{min}-{max}之间
api_template.global.not_blank=是否是全局字段不能为空
project_extend.project_id.not_blank=项目ID不能为空
project_extend.platform.length_range=平台长度必须在{min}-{max}之间
project_extend.platform.not_blank=平台不能为空
project.id.not_blank=ID不能为空
project.module_menu.check.error=该项目的模块菜单检查失败
project.organization_id.length_range=组织ID长度必须在{min}-{max}之间
project.organization_id.not_blank=组织ID不能为空
project.name.length_range=名称长度必须在{min}-{max}之间
project.name.not_blank=名称不能为空
custom_function.id.not_blank=ID不能为空
custom_function.name.length_range=名称长度必须在{min}-{max}之间
custom_function.name.not_blank=名称不能为空
custom_function.type.length_range=类型长度必须在{min}-{max}之间
custom_function.type.not_blank=类型不能为空
custom_function.status.length_range=脚本状态长度必须在{min}-{max}之间
custom_function.status.not_blank=脚本状态不能为空
custom_function.create_user.length_range=创建人长度必须在{min}-{max}之间
custom_function.create_user.not_blank=创建人不能为空
custom_function.project_id.length_range=项目ID长度必须在{min}-{max}之间
custom_function.project_id.not_blank=项目ID不能为空
custom_field.id.not_blank=ID不能为空
custom_field.name.length_range=名称长度必须在{min}-{max}之间
custom_field.name.not_blank=名称不能为空
custom_field.scene.length_range=场景长度必须在{min}-{max}之间
custom_field.scene.not_blank=场景不能为空
custom_field.type.length_range=类型长度必须在{min}-{max}之间
custom_field.type.not_blank=类型不能为空
custom_field.remark.length_range=描述长度必须在{max}以内
file_module_blob.file_module_id.not_blank=文件模块ID不能为空
project_version.id.not_blank=ID不能为空
project_version.project_id.length_range=项目ID长度必须在{min}-{max}之间
project_version.project_id.not_blank=项目ID不能为空
project_version.name.length_range=名称长度必须在{min}-{max}之间
project_version.name.not_blank=名称不能为空
project_version.latest.length_range=最新版本长度必须在{min}-{max}之间
project_version.latest.not_blank=最新版本不能为空
project_version.create_user.length_range=创建人长度必须在{min}-{max}之间
project_version.create_user.not_blank=创建人不能为空
file_metadata.id.not_blank=ID不能为空
file_metadata.name.length_range=名称长度必须在{min}-{max}之间
file_metadata.name.not_blank=名称不能为空
file_metadata.storage.length_range=存储长度必须在{min}-{max}之间
file_metadata.storage.not_blank=存储不能为空
functional_case_template_extend.id.not_blank=ID不能为空
functional_case_template_extend.step_model.length_range=步骤模型长度必须在{min}-{max}之间
functional_case_template_extend.step_model.not_blank=步骤模型不能为空
project_not_exist=项目不存在
#消息管理
save_message_task_user_no_exist=所选用户部分不存在
# robot
robot_in_site=站内信
robot_in_site_description=系统内置，在顶部导航栏显示消息通知
robot_mail=邮件
robot_mail_description=系统内置，以添加用户邮箱为通知方式
robot_is_null=当前机器人不存在
ding_type_is_null=钉钉机器人的类型不能为空
ding_app_key_is_null=钉钉的AppKey不能为空
ding_app_secret_is_null=钉钉的AppSecret不能为空
# permission
permission.project_user.name=用户
permission.project_user.invite=邀请用户
permission.project_group.name=用户组
permission.project_environment.name=环境管理
permission.project_file.name=文件管理
permission.project_template.name=模版管理
permission.project_message.name=消息管理
permission.project_version.name=版本管理
permission.project_fake_error.name=误报库
permission.project_application.name=应用设置
permission.project_application_test_plan.read=测试计划-查询
permission.project_application_test_plan.update=测试计划-编辑
permission.project_application_ui.read=UI测试-查询
permission.project_application_ui.update=UI测试-编辑
permission.project_base_info.name=基本信息
permission.project_log.name=日志

# message
message.test_plan_task=测试计划
message.schedule_task=定时任务
message.report_task=报告
message.bug_task=缺陷
message.bug_sync_task=同步
message.functional_case_task=功能用例
message.case_review_task=用例评审
message.api_definition_task=定义
message.api_scenario_task=场景
message.ui_scenario_task=UI自动化
message.load_test_task=测试用例
message.jenkins_task=执行
message.batch_execution=批量执行
message.manual_execution=手动执行
message.test_plan_management=测试计划
message.bug_management=缺陷管理
message.case_management=测试用例
message.api_test_management=接口测试
message.ui_test_management=UI测试
message.load_test_management=性能测试
message.jenkins_task_management=Jenkins
permission.project_application_task.read=任务中心-查询
permission.project_application_task.update=任务中心-编辑
message.schedule_task_management=定时任务
message.create=创建
message.update=更新
message.delete=删除
message.execute_completed=执行完成
message.comment=评论
message.at=被@
message.replay=被回复
message.review_passed=评审通过
message.review_fail=评审不通过
message.review_at=评审被@
message.review_completed=评审完成
message.case_create=CASE 创建
message.case_update=CASE 更新
message.case_delete=CASE 删除
message.case_execute_successful=CASE 执行成功
message.case_execute_fake_error=CASE 执行误报
message.case_execute_failed=CASE 执行失败
message.mock_create=MOCK 创建
message.mock_update=MOCK 更新
message.mock_delete=MOCK 删除
message.scenario_execute_successful=执行成功
message.scenario_execute_fake_error=执行误报
message.scenario_execute_failed=执行失败
message.execute_successful=执行成功
message.execute_failed=执行失败
message.execute_passed=执行通过
message.execute_fail=执行不通过
message.execute_at=执行被@
message.open=开启
message.close=关闭
message.assign=分配
message.sync_completed=同步完成
message.create_user=创建人
message.follow_people=关注人
message.operator=操作人
message.handle_user=处理人 (第三方平台的处理人, 不会接收到通知)
message.trigger_mode=触发方式
message.jenkins_name=名称
message.custom_field=自定义字段
message.case_field=用例字段
message.report_field=报告字段
message.test_plan_task_create=${OPERATOR}创建了测试计划:${name}
message.test_plan_task_update=${OPERATOR}更新了测试计划:${name}
message.test_plan_task_delete=${OPERATOR}删除了测试计划:${name}
message.test_plan_task_execute=${OPERATOR}执行了测试计划:${name}
message.test_plan_report_task_delete=${OPERATOR}删除了测试计划报告:${name}
message.bug_task_create=${OPERATOR}创建了缺陷:${title}
message.bug_task_update=${OPERATOR}更新了缺陷:${title}
message.bug_task_delete=${OPERATOR}删除了缺陷:${title}
message.bug_task_comment=${OPERATOR}评论了你的缺陷:${title}
message.bug_task_at_comment=${OPERATOR}评论了缺陷:${title} 并@了你
message.bug_task_reply_comment=${OPERATOR}在缺陷 ${title} 回复了你的评论
message.bug_sync_task_execute_completed=${OPERATOR}同步了${total}条缺陷
message.bug_task_assign=${OPERATOR}给你分配了一个缺陷: ${title}
message.functional_case_task_create=${OPERATOR}创建了功能用例:${name}
message.functional_case_task_update=${OPERATOR}更新了功能用例:${name}
message.functional_case_task_delete=${OPERATOR}删除了功能用例:${name}
message.functional_case_task_comment=${OPERATOR}评论了你的功能用例:${name}
message.functional_case_task_review=${OPERATOR}评审了${reviewName}${name}
message.functional_case_task_review_at=${OPERATOR}在${reviewName}${name}@了你
message.functional_case_task_plan=${OPERATOR}执行了${testPlanName}${name}
message.functional_case_task_plan_at=${OPERATOR}在${testPlanName}${name}@了你

message.functional_case_task_at_comment=${OPERATOR}评论了功能用例:${name} 并@了你
message.functional_case_task_reply_comment=${OPERATOR}在用例 ${name} 回复了你的评论
message.case_review_task_create=${OPERATOR}创建了用例评审:${name}
message.case_review_task_update=${OPERATOR}更新了用例评审:${name}
message.case_review_task_delete=${OPERATOR}删除了用例评审:${name}
message.case_review_task_review_completed=${OPERATOR}完成了用例评审:${name}
message.api_definition_task_create=${OPERATOR}创建了接口定义:${name}
message.api_definition_task_update=${OPERATOR}更新了接口定义:${name}
message.api_definition_task_delete=${OPERATOR}删除了接口定义:${name}
message.api_definition_task_case_create=${OPERATOR}创建了接口用例:${name}
message.api_definition_task_case_update=${OPERATOR}更新了接口用例:${name}
message.api_definition_task_case_delete=${OPERATOR}删除了接口用例:${name}
message.api_definition_task_case_execute=${OPERATOR}执行了接口用例:${name}
message.api_definition_task_mock_create=${OPERATOR}创建了接口MOCK:${name}
message.api_definition_task_mock_update=${OPERATOR}更新了接口MOCK:${name}
message.api_definition_task_mock_delete=${OPERATOR}删除了接口MOCK:${name}
message.api_scenario_task_create=${OPERATOR}创建了接口场景:${name}
message.api_scenario_task_update=${OPERATOR}更新了接口场景:${name}
message.api_scenario_task_delete=${OPERATOR}删除了接口场景:${name}
message.api_scenario_task_scenario_execute=${OPERATOR}执行了接口场景:${name}
message.api_report_task_delete=${OPERATOR}删除了接口报告:${name}
message.ui_scenario_task_create=${OPERATOR}创建了UI用例:${name}
message.ui_scenario_task_update=${OPERATOR}更新了UI用例:${name}
message.ui_scenario_task_delete=${OPERATOR}删除了UI用例:${name}
message.ui_scenario_task_execute=${OPERATOR}执行了UI用例:${name}
message.ui_report_task_delete=${OPERATOR}删除了UI报告:${name}
message.load_test_task_create=${OPERATOR}创建了性能用例:${name}
message.load_test_task_update=${OPERATOR}更新了性能用例:${name}
message.load_test_task_delete=${OPERATOR}删除了性能用例:${name}
message.load_test_task_execute_completed=${OPERATOR}执行了性能用例:${name}
message.load_report_task_delete=${OPERATOR}删除了性能报告:${name}
message.jenkins_task_execute=Jenkins执行了:${name}
message.schedule_task_open=${OPERATOR}开启了定时任务:${name}
message.schedule_task_close=${OPERATOR}关闭了定时任务:${name}


message.title.test_plan_task_create=测试计划创建通知
message.title.test_plan_task_update=测试计划更新通知
message.title.test_plan_task_delete=测试计划删除通知
message.title.test_plan_task_execute_successful=测试计划执行成功通知
message.title.test_plan_task_execute_failed=测试计划执行失败通知
message.title.test_plan_report_task_delete=测试计划报告删除通知
message.title.bug_task_create=缺陷创建通知
message.title.bug_task_update=缺陷更新通知
message.title.bug_task_delete=缺陷删除通知
message.title.bug_task_comment=缺陷评论通知
message.title.bug_sync_task_execute_completed=同步缺陷执行完成通知
message.title.bug_task_assign=缺陷分配通知
message.title.functional_case_task_create=功能用例创建通知
message.title.functional_case_task_update=功能用例更新通知
message.title.functional_case_task_delete=功能用例删除通知
message.title.functional_case_task_comment=功能用例评论通知
message.title.functional_case_task_review_passed=用例评审通过通知
message.title.functional_case_task_review_fail=用例评审不通过通知
message.title.functional_case_task_review_at=用例评审通知
message.title.functional_case_task_execute_passed=用例执行通过通知
message.title.functional_case_task_execute_fail=用例执行不通过通知
message.title.functional_case_task_execute_at=用例执行通知


message.title.case_review_task_create=用例评审创建通知
message.title.case_review_task_update=用例评审更新通知
message.title.case_review_task_delete=用例评审删除通知
message.title.case_review_task_review_completed=用例评审评审完成通知
message.title.api_definition_task_create=接口定义创建通知
message.title.api_definition_task_update=接口定义更新通知
message.title.api_definition_task_delete=接口定义删除通知
message.title.api_definition_task_case_create=接口用例创建通知
message.title.api_definition_task_case_update=接口用例更新通知
message.title.api_definition_task_case_delete=接口用例删除通知
message.title.api_definition_task_case_execute_successful=接口用例执行成功通知
message.title.api_definition_task_case_execute_fake_error=接口用例执行误报通知
message.title.api_definition_task_case_execute_failed=接口用例执行失败通知
message.title.api_definition_task_mock_create=MOCK创建通知
message.title.api_definition_task_mock_update=MOCK更新通知
message.title.api_definition_task_mock_delete=MOCK删除通知
message.title.api_scenario_task_create=接口场景创建通知
message.title.api_scenario_task_update=接口场景更新通知
message.title.api_scenario_task_delete=接口场景删除通知
message.title.api_scenario_task_scenario_execute_successful=接口场景执行成功通知
message.title.api_scenario_task_scenario_execute_fake_error=接口场景执误报通知
message.title.api_scenario_task_scenario_execute_failed=接口场景执行失败通知
message.title.api_report_task_delete=接口报告删除通知
message.title.ui_scenario_task_create=UI用例创建通知
message.title.ui_scenario_task_update=UI用例更新通知
message.title.ui_scenario_task_delete=UI用例删除通知
message.title.ui_scenario_task_execute_successful=UI用例执行成功通知
message.title.ui_scenario_task_execute_failed=UI用例执行失败通知
message.title.ui_report_task_delete=UI报告删除通知
message.title.load_test_task_create=性能用例创建通知
message.title.load_test_task_update=性能用例更新通知
message.title.load_test_task_delete=性能用例删除通知
message.title.load_test_task_execute_completed=性能用例执行完成通知
message.title.load_report_task_delete=性能报告删除通知
message.title.jenkins_task_execute_successful=Jenkins任务执行成功通知
message.title.jenkins_task_execute_failed=Jenkins任务执行失败通知
message.title.schedule_task_open=开启定时任务通知
message.title.schedule_task_close=关闭定时任务通知
#功能用例
message.domain.name=名称
message.domain.testPlanName=测试计划名称
message.domain.reviewName=评审名称
message.domain.reviewStatus=评审状态
message.domain.caseModel=编辑模式
message.domain.lastExecuteResult=最近的执行结果
message.domain.createUser=创建人
message.domain.updateUser=更新人
message.domain.deleteUser=删除人
message.domain.createTime=创建时间
message.domain.updateTime=更新时间
message.domain.deleteTime=删除时间
#接口定义和用例
message.domain.id=ID
message.domain.protocol=接口协议
message.domain.method=http协议类型
message.domain.path=http协议路径/其它协议则为空
message.domain.status=接口状态
message.domain.description=描述
message.domain.caseName=接口用例名称
message.domain.priority=用例等级
message.domain.caseStatus=用例状态
message.domain.lastReportStatus=用例最新执行结果状态
message.domain.principal=用例责任人
message.domain.caseCreateTime=用例创建时间
message.domain.caseCreateUser=用例创建人
message.domain.caseUpdateTime=用例更新时间
message.domain.caseUpdateUser=用例更新人
message.domain.caseDeleteTime=用例删除时间
message.domain.caseDeleteUser=用例删除人
message.domain.mockName=期望名称
message.domain.reportUrl=报告地址
message.domain.shareUrl=分享地址
message.domain.reportName=报告名称
message.domain.startTime=开始时间
message.domain.endTime=结束时间
message.domain.requestDuration=请求总耗时
message.domain.reportStatus=报告状态
message.domain.environment=报告环境
message.domain.errorCount=失败数
message.domain.fakeErrorCount=误报数
message.domain.pendingCount=未执行数
message.domain.successCount=成功数
message.domain.assertionCount=总断言数
message.domain.assertionSuccessCount=断言成功数
message.domain.requestErrorRate=请求失败率
message.domain.requestPendingRate=请求未执行率
message.domain.requestFakeErrorRate=请求误报率
message.domain.requestPassRate=通过率
message.domain.assertionPassRate=断言通过率
message.domain.projectId=项目id
#场景字段
message.domain.api_scenario_name=场景名称
message.domain.api_scenario_level=场景级别
message.domain.api_scenario_status=场景状态
message.domain.api_scenario_principal=责任人
message.domain.api_scenario_stepTotal=场景步骤总数
message.domain.api_scenario_num=编号
message.domain.api_scenario_reportRequestPassRate=通过率
message.domain.api_scenario_lastReportStatus=最后一次执行的结果状态
message.domain.api_scenario_description=描述
message.domain.api_scenario_tags=标签
message.domain.api_scenario_grouped=是否为环境组
message.domain.api_scenario_createUser=创建人
message.domain.api_scenario_updateUser=更新人
message.domain.api_scenario_deleteUser=删除人
message.domain.api_scenario_createTime=创建时间
message.domain.api_scenario_updateTime=更新时间
message.domain.api_scenario_deleteTime=删除时间
message.domain.api_scenario_priority=优先级
message.domain.api_scenario_requestPassRate=请求通过率
message.domain.api_scenario_reportUrl=报告地址
message.domain.api_scenario_shareUrl=分享地址
message.domain.api_scenario_reportName=报告名称
message.domain.api_scenario_startTime=开始时间
message.domain.api_scenario_endTime=结束时间
message.domain.api_scenario_requestDuration=请求总耗时
message.domain.api_scenario_reportStatus=报告状态
message.domain.api_scenario_environment=报告环境
message.domain.api_scenario_errorCount=失败数
message.domain.api_scenario_fakeErrorCount=误报数
message.domain.api_scenario_pendingCount=未执行数
message.domain.api_scenario_successCount=成功数
message.domain.api_scenario_assertionCount=总断言数
message.domain.api_scenario_assertionSuccessCount=断言成功数
message.domain.api_scenario_requestErrorRate=请求失败率
message.domain.api_scenario_requestPendingRate=请求未执行率
message.domain.api_scenario_requestFakeErrorRate=请求误报率
message.domain.api_scenario_assertionPassRate=断言通过率
# 测试计划
message.domain.test_plan_stage=测试阶段
message.domain.test_plan_status=测试计划状态
message.domain.test_plan_description=描述
message.domain.test_plan_tags=标签
message.domain.test_plan_createUser=创建人
message.domain.test_plan_updateUser=更新人
message.domain.test_plan_createTime=创建时间
message.domain.test_plan_updateTime=更新时间
message.domain.test_plan_plannedStartTime=计划开始时间
message.domain.test_plan_plannedEndTime=计划结束时间
message.domain.test_plan_actualStartTime=实际开始时间
message.domain.test_plan_actualEndTime=实际结束时间
message.domain.test_plan_num=编号
message.domain.test_plan_type=类型
message.domain.test_plan_reportName=报告名称
message.domain.test_plan_reportUrl=报告链接
message.domain.test_plan_reportShareUrl=分享报告链接
message.domain.test_plan_startTime=开始时间;计划开始执行的时间
message.domain.test_plan_endTime=结束时间;计划结束执行的时间
message.domain.test_plan_execStatus=执行状态
message.domain.test_plan_resultStatus=结果状态
message.domain.test_plan_passRate=通过率
message.domain.test_plan_passThreshold=通过阈值
message.domain.test_plan_executeRate=执行率

# 用例评审
message.domain.case_review_name=名称
message.domain.case_review_num=ID
message.domain.case_review_status=评审状态
message.domain.case_review_description=描述
message.domain.case_review_tags=标签
message.domain.case_review_createUser=创建人
message.domain.case_review_createTime=创建时间
message.domain.case_review_updateTime=更新时间
message.domain.case_review_updateUser=更新人
message.domain.case_review_endTime=评审结束时间
message.domain.case_review_startTime=评审开始时间
message.domain.case_review_passRate=通过率
message.domain.case_review_caseCount=用例数
message.domain.case_review_reviewPassRule=评审规则
# 缺陷
message.domain.bug_num=业务ID
message.domain.bug_title=缺陷标题
message.domain.bug_assignUser=指派人
message.domain.bug_platform=缺陷平台
message.domain.bug_tag=标签
message.domain.bug_status=状态
message.domain.bug_createUser=创建人
message.domain.bug_updateUser=更新人
message.domain.bug_createTime=创建时间
message.domain.bug_updateTime=更新时间
message.domain.bug_deleteUser=删除人
message.domain.bug_deleteTime=删除时间
message.domain.bug_handleUser=处理人
message.domain.bug_sync_platform=同步平台
message.domain.bug_sync_total_count=同步数量
#UI
message.domain.ui_name=场景名称
message.domain.ui_level=用例等级
message.domain.ui_principal=责任人
message.domain.ui_stepTotal=步骤总数
message.domain.ui_tags=标签
message.domain.ui_status=状态
message.domain.ui_num=业务ID
message.domain.ui_lastResult=最后执行结果
message.domain.ui_createUser=创建人
message.domain.ui_updateUser=更新人
message.domain.ui_createTime=创建时间
message.domain.ui_updateTime=更新时间
message.domain.ui_deleteUser=删除人
message.domain.ui_deleteTime=删除时间
message.domain.ui_description=描述
#性能
message.domain.load_name=测试名称
message.domain.load_status=状态
message.domain.load_num=业务ID
message.domain.load_createUser=创建人
message.domain.load_updateUser=更新人
message.domain.load_createTime=创建时间
message.domain.load_updateTime=更新时间
message.domain.load_description=描述
#定时任务
message.domain.schedule_key=qrtz UUID
message.domain.schedule_type=执行类型
message.domain.schedule_value=cron 表达式
message.domain.schedule_job=Schedule Job Class Name
message.domain.schedule_name=名称
message.domain.schedule_config=配置
message.domain.schedule_createUser=创建人
message.domain.schedule_updateUser=更新人
message.domain.schedule_createTime=创建时间
message.domain.schedule_updateTime=更新时间
message.domain.schedule_enable=是否开启
message.domain.schedule_resourceType=资源类型
#资源池
resource_pool_not_exist=资源池不存在

#file management
file_module.not.exist=文件模块不存在
drag_node.not.exist=拖拽节点不存在
module.branches.size.limit=模块所在分支总节点数量不能超过{0}个
drop_node.not.exist=目标节点不存在
file_module.parent.not.exist=文件模块父节点不存在
upload.file.error=上传文件失败
file.not.exist=文件不存在
file.some.not.exist=部分文件不存在
old.file.not.exist=旧文件不存在
latest.file.not.exist=最新文件不存在
file.not.jar=不是jar文件
change.jar.enable=修改了jar文件的启用状态
file.name.exist=文件名已存在
log.delete_module=模块下的所有数据全部被删除
file.module.root=根目录
folder.error=文件夹不合法
file.log.move_to=移动到
file.log.change_file_module=文件进行了移动
file.log.next=之后
file.log.previous=之前
file.log.upload=上传
file.log.repository.add=添加了存储库文件
file.log.re-upload=重新上传了文件
file.log.upload_file=上传了文件
file.log.pull=拉取了文件
file.log.association=关联了文件
file.log.association.update=更新了关联了文件
file.log.association.delete=取消关联了文件
file.log.transfer.association=转存并关联了文件
file.name.cannot.be.empty=文件名称不能为空
file.size.is.too.large=文件不能超过50M
file.is.empty=文件为空
file.name.error=文件名不合法
module.name.is.empty=模块参数为空
module.name.is.error=模块参数不正确
#file management over

# template
project_template_permission_error=未开启项目模板
plugin_bug_template_remark=模板为系统自动获取，不支持编辑和查看

global_params=全局参数.json
env_info_all=环境信息(总).json

# custom_function
custom_function_already_exist= 脚本名称已存在
permission.project_custom_function.name=公共脚本
permission.project_custom_function.execute=测试

message.domain.report.name=报告名称