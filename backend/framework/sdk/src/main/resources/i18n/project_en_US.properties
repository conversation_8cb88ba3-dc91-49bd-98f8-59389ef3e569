fake_error.id.not_blank=ID is required
file.module.default.name=Unplanned file
fake_error.project_id.length_range=Project ID length must be between {min} and {max}
fake_error.project_id.not_blank=Project ID is required
fake_error.name.length_range=Name length must be between {min} and {max}
fake_error.name.not_blank=Name is required
fake_error.create_user.length_range=Create User length must be between {min} and {max}
fake_error.create_user.not_blank=Create User is required
fake_error.update_user.length_range=Update User length must be between {min} and {max}
fake_error.update_user.not_blank=Update User is required
fake_error.error_code.length_range=Error Code length must be between {min} and {max}
fake_error.type.not_blank=Type is required
fake_error.type.length_range=Type length must be between {min} and {max}
fake_error.resp_type.not_blank=Resp Type is required
fake_error.relation.not_blank=Relation is required
fake_error.relation.length_range=Relation length must be between {min} and {max}
fake_error.expression.not_blank=Expression is required
fake_error.expression.length_range=Expression length must be between {min} and {max}
fake_error.enable.not_blank=Enable is required
fake_error.enable.length_range=Enable length must be between {min} and {max}
fake_error.error_code.not_blank=Error Code is required
fake_error.match_type.length_range=Match Type length must be between {min} and {max}
fake_error.match_type.not_blank=Match Type is required
bug_template_extend.id.not_blank=ID is required
project_application.project_id.not_blank=Project ID is required
project_application.type.not_blank=Type is required
project_application.type_value.not_blank=Type Value is required
custom_function_blob.id.not_blank=ID is required
fake_error_blob.id.not_blank=ID is required
file_module.id.not_blank=ID is required
file_module.project_id.length_range=Project ID length must be between {min} and {max}
file_module.project_id.not_blank=Project ID is required
file_module.name.length_range=Name length must be between {min} and {max}
file_module.name.not_blank=Name is required
file.association.error.type=Source type is error
file.association.not.exist=File not association
file.association.source.not.exist=Source not exist
file_repository.connect.error=Repository connect error
file_repository.not.exist=Repository not exist
file_repository.platform.error=Repository platform error
file_repository.id.not_blank=Repository ID is required
file_repository.name.not_blank=Repository name is required
file_repository.type.not_blank=Repository type is required
file_repository.token.not_blank=Repository token is required
file_repository.url.not_blank=Repository url is required
file_repository.branch.not_blank=Repository branch is required
file_repository.file_path.not_blank=Repository file path is required
custom_field_template.id.not_blank=ID is required
custom_field_template.field_id.length_range=Field ID length must be between {min} and {max}
custom_field_template.field_id.not_blank=Field ID is required
custom_field_template.template_id.length_range=Template ID length must be between {min} and {max}
custom_field_template.template_id.not_blank=Template ID is required
custom_field_template.scene.length_range=Scene length must be between {min} and {max}
custom_field_template.scene.not_blank=Scene is required
file_metadata_blob.id.not_blank=ID is required
bug_template.id.not_blank=ID is required
bug_template.name.length_range=Name length must be between {min} and {max}
bug_template.name.not_blank=Name is required
bug_template.create_user.length_range=Create User length must be between {min} and {max}
bug_template.create_user.not_blank=Create User is required
bug_template.project_id.length_range=Project ID length must be between {min} and {max}
bug_template.project_id.not_blank=Project ID is required
functional_case_template.id.not_blank=ID is required
functional_case_template.name.length_range=Name length must be between {min} and {max}
functional_case_template.name.not_blank=Name is required
functional_case_template.create_user.length_range=Create User length must be between {min} and {max}
functional_case_template.create_user.not_blank=Create User is required
functional_case_template.project_id.length_range=Project ID length must be between {min} and {max}
functional_case_template.project_id.not_blank=Project ID is required
api_template.id.not_blank=ID is required
api_template.name.length_range=Name length must be between {min} and {max}
api_template.name.not_blank=Name is required
api_template.system.length_range=System length must be between {min} and {max}
api_template.system.not_blank=System is required
api_template.global.length_range=Global length must be between {min} and {max}
api_template.global.not_blank=Global is required
project_extend.project_id.not_blank=Project ID is required
project_extend.platform.length_range=Platform length must be between {min} and {max}
project_extend.platform.not_blank=Platform is required
project.id.not_blank=ID is required
project.module_menu.check.error=Project module menu check error
project.organization_id.length_range=Organization ID length must be between {min} and {max}
project.organization_id.not_blank=Organization ID is required
project.name.length_range=Name length must be between {min} and {max}
project.name.not_blank=Name is required
custom_function.id.not_blank=ID is required
custom_function.name.length_range=Name length must be between {min} and {max}
custom_function.name.not_blank=Name is required
custom_function.type.length_range=Type length must be between {min} and {max}
custom_function.type.not_blank=Type is required
custom_function.status.length_range=Status length must be between {min} and {max}
custom_function.status.not_blank=Status is required
custom_function.create_user.length_range=Create User length must be between {min} and {max}
custom_function.create_user.not_blank=Create User is required
custom_function.project_id.length_range=Project ID length must be between {min} and {max}
custom_function.project_id.not_blank=Project ID is required
custom_field.id.not_blank=ID is required
custom_field.name.length_range=Name length must be between {min} and {max}
custom_field.name.not_blank=Name is required
custom_field.scene.length_range=Scene length must be between {min} and {max}
custom_field.scene.not_blank=Scene is required
custom_field.type.length_range=Type length must be between {min} and {max}
custom_field.type.not_blank=Type is required
custom_field.remark.length_range=Remark length must be within {max}
file_module_blob.file_module_id.not_blank=File Module ID is required
project_version.id.not_blank=ID is required
project_version.project_id.length_range=Project ID length must be between {min} and {max}
project_version.project_id.not_blank=Project ID is required
project_version.name.length_range=Name length must be between {min} and {max}
project_version.name.not_blank=Name is required
project_version.latest.length_range=Latest length must be between {min} and {max}
project_version.latest.not_blank=Latest is required
project_version.create_user.length_range=Create User length must be between {min} and {max}
project_version.create_user.not_blank=Create User is required
file_metadata.id.not_blank=ID is required
file_metadata.name.length_range=Name length must be between {min} and {max}
file_metadata.name.not_blank=Name is required
file_metadata.storage.length_range=Storage length must be between {min} and {max}
file_metadata.storage.not_blank=Storage is required
functional_case_template_extend.id.not_blank=ID is required
functional_case_template_extend.step_model.length_range=Step Model length must be between {min} and {max}
functional_case_template_extend.step_model.not_blank=Step Model is required
project_is_null=Project does not exist
fake_error_name_exist=Fake error name already exists
#æ¶æ¯ç®¡ç
save_message_task_user_no_exist=The selected user section does not exist
# robot
robot_in_site=Site message
robot_in_site_description=Built-in system, message notifications are displayed in the top navigation bar
robot_mail=Mail
robot_mail_description=Built-in system, adding user email as notification method
robot_is_null=The current robot does not exist
ding_type_is_null= DingTalk robot type is required
ding_app_key_is_null=DingTalk AppKey is required
ding_app_secret_is_null=DingTalk AppSecret is required
module.name.is.empty=Module parameter is empty
module.name.is.error=Module parameter is error
# permission
permission.project_user.name=User
permission.project_user.invite=Invite User
permission.project_group.name=User group
permission.project_environment.name=Environment management
permission.project_file.name=File management
permission.project_file.download=Download file
permission.project_template.name=Template management
permission.project_message.name=Message management
permission.project_version.name=Version management
permission.project_fake_error.name=Fake error
permission.project_application.name=Project application
permission.project_application_test_plan.read=Test plan read
permission.project_application_test_plan.update=Test plan update
permission.project_application_ui.read=UI test read
permission.project_application_ui.update=UI test update
permission.project_application_performance_test.read=Performance test read
permission.project_application_performance_test.update=Performance test update
permission.project_application_api.read=API test read
permission.project_application_api.update=API test update
permission.project_application_case.read=Case read
permission.project_application_case.update=Case update
permission.project_application_bug.read=Bug read
permission.project_application_bug.update=Bug update
permission.project_application_workstation.read=Workstation read
permission.project_application_workstation.update=Workstation update
permission.project_application_task.read=ä»»å¡ä¸­å¿-æ¥è¯¢
permission.project_application_task.update=ä»»å¡ä¸­å¿-ç¼è¾
permission.project_base_info.name=Project base info
permission.project_log.name=Operation log
# project version
project.version.exist=Version exist
project.version.not_exist=Version not exist
project.version.resource_exist=There is resource data associated with the version, please delete the data first
project.version.only=Can be closed when there is only one version
project.version.latest.no_delete=The latest version cannot be deleted
#environment datasource
environment_datasource.id.not_blank=ID is required
environment_datasource.driver.not_blank=Driver is required
environment_datasource.driverId.not_blank=Driver ID is required
environment_datasource.dbUrl.not_blank=Db Url is required
environment_name_is_null=Environment name is required
environment_config_is_null=Environment config is required
# message
message.test_plan_task=Test Plan
message.schedule_task=Scheduled tasks
message.report_task=Report
message.bug_task=Bug
message.bug_sync_task=Synchronize
message.functional_case_task=Functional case
message.case_review_task=Case review
message.api_definition_task=API
message.api_scenario_task=Scenario
message.ui_scenario_task=UI automation
message.load_test_task=Test case
message.jenkins_task=Execution
message.batch_execution=Batch execution
message.manual_execution=Manual execution
message.test_plan_management=Test Plan
message.bug_management=Bug
message.case_management=Functional
message.api_test_management=API Test
message.ui_test_management=UI test
message.load_test_management=Load test
message.jenkins_task_management=Jenkins
message.schedule_task_management=Schedule task
message.create=Create
message.update=Update
message.delete=Delete
message.execute_completed=Execution completed
message.comment=Comment
message.at=quilt@
message.replay=Replied
message.review_passed=Passed the review
message.review_fail=The review failed
message.review_at=è©å¯©è¢«@
message.review_completed=Review completed
message.case_create=CASE create
message.case_update=CASE update
message.case_delete=CASE delete
message.case_execute_successful=CASE executed successfully
message.case_execute_fake_error=CASE execution fake error
message.case_execute_failed=CASE execution failed
message.mock_create=MOCK create
message.mock_update=MOCK update
message.mock_delete=MOCK delete
message.scenario_execute_successful=Executed successfully
message.scenario_execute_fake_error=Execution fake error
message.scenario_execute_failed=Execution failed
message.execute_successful=Executed successfully
message.execute_failed=Execution failed
message.execute_passed=Execution passed
message.execute_fail=Execution failed
message.execute_at=Execution is @
message.open=turn on
message.close=closure
message.assign=assign
message.sync_completed=Sync completed
message.create_user=Create user
message.follow_people=Follow people
message.operator=Operator
message.handle_user=Handler (The handler of the third party platform will not receive the notification)
message.trigger_mode=Trigger mode
message.jenkins_name=Name
message.custom_field=Custom fields
message.case_field=Case field
message.report_field=Report fields
message.test_plan_task_create=${OPERATOR} created the test plan: ${name}
message.test_plan_task_update=${OPERATOR} updated the test plan: ${name}
message.test_plan_task_delete=${OPERATOR} deleted the test plan: ${name}
message.test_plan_task_execute=${OPERATOR} executed the test plan: ${name}
message.test_plan_report_task_delete=${OPERATOR} deleted test plan report: ${name}
message.bug_task_create=${OPERATOR} created a bug: ${title}
message.bug_task_update=${OPERATOR} updated a bug: ${title}
message.bug_task_delete=${OPERATOR} deleted a bug: ${title}
message.bug_task_comment=${OPERATOR} commented on your bug: ${title}
message.bug_task_at_comment=${OPERATOR} commented on the bug: ${title} and @you
message.bug_task_reply_comment=${OPERATOR} in defect ${title} replied to your comment
message.bug_sync_task_execute_completed=${OPERATOR} synchronized ${total} bugs
message.bug_task_assign=${OPERATOR} assign you a bug: ${title}
message.functional_case_task_create=${OPERATOR} created the functional case: ${name}
message.functional_case_task_update=${OPERATOR} updated the functional case: ${name}
message.functional_case_task_delete=${OPERATOR} deleted the functional case: ${name}
message.functional_case_task_comment=${OPERATOR} commented on your functional case: ${name}
message.functional_case_task_review=${OPERATOR} reviewed ${reviewName} ${name}
message.functional_case_task_review_at=${OPERATOR} @ you in ${reviewName} ${name}
message.functional_case_task_plan=${OPERATOR} executed ${testPlanName} ${name}
message.functional_case_task_plan_at=${OPERATOR} @ you in ${testPlanName} ${name}


message.functional_case_task_at_comment=${OPERATOR} commented on functional case: ${name} and @you
message.functional_case_task_reply_comment=${OPERATOR} in use case ${name} replied to your comment
message.case_review_task_create=${OPERATOR} created the case review: ${name}
message.case_review_task_update=${OPERATOR} updated the case review: ${name}
message.case_review_task_delete=${OPERATOR} deleted the case review: ${name}
message.case_review_task_review_completed=${OPERATOR} completed the case review: ${name}
message.api_definition_task_create=${OPERATOR} created the API definition: ${name}
message.api_definition_task_update=${OPERATOR} updated the API definition: ${name}
message.api_definition_task_delete=${OPERATOR} deleted the API definition: ${name}
message.api_definition_task_case_create=${OPERATOR} created the API case: ${name}
message.api_definition_task_case_update=${OPERATOR} updated the API case: ${name}
message.api_definition_task_case_delete=${OPERATOR} deleted the API case: ${name}
message.api_definition_task_case_execute=${OPERATOR} executed the API case: ${name}
message.api_definition_task_mock_create=${OPERATOR} created the API MOCK: ${name}
message.api_definition_task_mock_update=${OPERATOR} updated the API MOCK: ${name}
message.api_definition_task_mock_delete=${OPERATOR} deleted the API MOCK: ${name}
message.api_scenario_task_create=${OPERATOR} created the API scenario: ${name}
message.api_scenario_task_update=${OPERATOR} updated the API scenario: ${name}
message.api_scenario_task_delete=${OPERATOR} deleted the API scenario: ${name}
message.api_scenario_task_scenario_execute=${OPERATOR} executed the API scenario: ${name}
message.api_report_task_delete=${OPERATOR} deleted API report: ${name}
message.ui_scenario_task_create=${OPERATOR} created the UI case: ${name}
message.ui_scenario_task_update=${OPERATOR} updated the UI case: ${name}
message.ui_scenario_task_delete=${OPERATOR} deleted the UI case: ${name}
message.ui_scenario_task_execute=${OPERATOR} executed the UI case: ${name}
message.ui_report_task_delete=${OPERATOR} deleted UI report: ${name}
message.load_test_task_create=${OPERATOR} created the load case: ${name}
message.load_test_task_update=${OPERATOR} updated the load case: ${name}
message.load_test_task_delete=${OPERATOR} deleted the load case: ${name}
message.load_test_task_execute_completed=${OPERATOR} executed the load case: ${name}
message.load_report_task_delete=${OPERATOR} deleted load report: ${name}
message.jenkins_task_execute=Jenkins executed the ${name}
message.schedule_task_open=${OPERATOR} started a scheduled task: ${name}
message.schedule_task_close=${OPERATOR} closed the scheduled task: ${name}
message.title.test_plan_task_create=Test plan creation notification
message.title.test_plan_task_update=Test plan update notification
message.title.test_plan_task_delete=Test plan deletion notification
message.title.test_plan_task_execute_successful=Test plan execution success notification
message.title.test_plan_task_execute_failed=Test plan execution failed notification
message.title.test_plan_report_task_delete=Test plan report deletion notification
message.title.bug_task_create=Bug creation notification
message.title.bug_task_update=Bug update notification
message.title.bug_task_delete=Bug deletion notification
message.title.bug_task_comment=Bug comment notification
message.title.bug_sync_task_execute_completed=Synchronization bug execution completion notification
message.title.bug_task_assign=Bug assignment notification
message.title.functional_case_task_create=Functional case creation notification
message.title.functional_case_task_update=Functional case update notification
message.title.functional_case_task_delete=Functional case deletion notification
message.title.functional_case_task_comment=Functional case comment notification
message.title.functional_case_task_review_passed=Case review approval notification
message.title.functional_case_task_review_fail=Notification of failure in case review
message.title.functional_case_task_review_at=Case review notice
message.title.functional_case_task_execute_passed=Case execution via notification
message.title.functional_case_task_execute_fail=Case execution fails notification
message.title.functional_case_task_execute_at=Case execution notification

message.title.case_review_task_create=Case review creation notification
message.title.case_review_task_update=Case review update notification
message.title.case_review_task_delete=Case review deletion notification
message.title.case_review_task_review_completed=Case review completion notification
message.title.api_definition_task_create=API definition creation notification
message.title.api_definition_task_update=API definition update notification
message.title.api_definition_task_delete=API definition deletion notification
message.title.api_definition_task_case_create=API case creation notification
message.title.api_definition_task_case_update=API case update notification
message.title.api_definition_task_case_delete=API case deletion notification
message.title.api_definition_task_case_execute_successful=API case execution success notification
message.title.api_definition_task_case_execute_fake_error=API case execution fake error notification
message.title.api_definition_task_case_execute_failed=API case execution failure notification
message.title.api_definition_task_mock_create=MOCK creation notification
message.title.api_definition_task_mock_update=MOCK update notification
message.title.api_definition_task_mock_delete=MOCK deletion notification
message.title.api_scenario_task_create=API scenario creation notification
message.title.api_scenario_task_update=API scenario update notification
message.title.api_scenario_task_delete=API scenario deletion notification
message.title.api_scenario_task_scenario_execute_successful=API scenario execution success notification
message.title.api_scenario_task_scenario_execute_fake_error=API scenario execution fake error notification
message.title.api_scenario_task_scenario_execute_failed=API scenario execution failure notification
message.title.api_report_task_delete=API report deletion notification
message.title.ui_scenario_task_create=UI case creation notification
message.title.ui_scenario_task_update=UI case update notification
message.title.ui_scenario_task_delete=UI case deletion notification
message.title.ui_scenario_task_execute_successful=UI case execution success notification
message.title.ui_scenario_task_execute_failed=UI case execution failure notification
message.title.ui_report_task_delete=UI report deletion notification
message.title.load_test_task_create=Load case creation notification
message.title.load_test_task_update=Load case update notification
message.title.load_test_task_delete=Load case deletion notification
message.title.load_test_task_execute_completed=Load case execution completion notification
message.title.load_report_task_delete=Load report deletion notification
message.title.jenkins_task_execute_successful=Jenkins task execution success notification
message.title.jenkins_task_execute_failed=Jenkins task execution failure notification
message.title.schedule_task_open=Turn on scheduled task notifications
message.title.schedule_task_close=Turn off scheduled task notifications


#åè½case
message.domain.name=Name
message.domain.testPlanName=Test plan name
message.domain.review_name=Review name
message.domain.review_status=Review status
message.domain.case_model=Edit mode
message.domain.last_execute_result=Recent execution results
message.domain.create_user=Create user
message.domain.update_user=Update user
message.domain.delete_user=Delete user
message.domain.create_time=Create time
message.domain.update_time=Update time
message.domain.delete_time=Delete time
message.domain.triggerMode=Trigger mode
#æ¥å£å®ä¹åcase
message.domain.id=ID
message.domain.protocol=Interface Protocol
message.domain.method=Http protocol type
message.domain.path=Http protocol path/other protocols are empty
message.domain.status=Interface status
message.domain.description=Description
message.domain.case_name=Interface case name
message.domain.priority=Case level
message.domain.case_status=Case status
message.domain.last_report_status=The latest execution result status of the use case
message.domain.principal=Case responsible
message.domain.case_create_time=Case create time
message.domain.case_create_user=Case create user
message.domain.case_update_time=Case update time
message.domain.case_update_user=Case update user
message.domain.case_delete_time=Case delete time
message.domain.case_delete_user=Case delete user
message.domain.mock_name=Expect name
message.domain.reportUrl=Report URL
message.domain.shareUrl=Share URL
message.domain.reportName=Report name
message.domain.startTime=Start time
message.domain.endTime=End time
message.domain.requestDuration=Request duration
message.domain.reportStatus=Report status
message.domain.environment=Report environment
message.domain.errorCount=Error count
message.domain.fakeErrorCount=Fake Error Count
message.domain.pendingCount=Pending Count
message.domain.successCount=Success Count
message.domain.assertionCount=Assertion Count
message.domain.assertionSuccessCount=Assertion Success Count
message.domain.requestErrorRate=Request Error Rate
message.domain.requestPendingRate=Request Pending Rate
message.domain.requestFakeErrorRate=Request Fake Error Rate
message.domain.requestPassRate=Request Pass Rate
message.domain.assertionPassRate=Assertion Pass Rate
message.domain.projectId=Project Id
#Scenarioå­æ®µ
message.domain.api_scenario_name=Scenario name
message.domain.api_scenario_level=Scenario level
message.domain.api_scenario_status=Scenario status
message.domain.api_scenario_principal=Responsible
message.domain.api_scenario_stepTotal=Overview of scenario steps
message.domain.api_scenario_num=Number
message.domain.api_scenario_passRate=Passing rate
message.domain.api_scenario_lastReportStatus=The latest execution result status
message.domain.api_scenario_description=Description
message.domain.api_scenario_tags=Tag
message.domain.api_scenario_grouped=Whether it is an environment group
message.domain.api_scenario_createUser=Create user
message.domain.api_scenario_updateUser=Update user
message.domain.api_scenario_deleteUser=Delete user
message.domain.api_scenario_createTime=Create time
message.domain.api_scenario_updateTime=Update time
message.domain.api_scenario_deleteTime=Delete time
message.domain.api_scenario_priority=Priority
message.domain.api_scenario_requestPassRate=Request pass rate
message.domain.api_scenario_reportUrl=Report URL
message.domain.api_scenario_shareUrl=Share URL
message.domain.api_scenario_reportName=Report name
message.domain.api_scenario_startTime=Start time
message.domain.api_scenario_endTime=End time
message.domain.api_scenario_requestDuration=Request duration
message.domain.api_scenario_reportStatus=Report status
message.domain.api_scenario_environment=Report environment
message.domain.api_scenario_errorCount=Error count
message.domain.api_scenario_fakeErrorCount=Fake error count
message.domain.api_scenario_pendingCount=Pending count
message.domain.api_scenario_successCount=Success count
message.domain.api_scenario_assertionCount=Assertion count
message.domain.api_scenario_assertionSuccessCount=Assertion success count
message.domain.api_scenario_requestErrorRate=Request error rate
message.domain.api_scenario_requestPendingRate=Request pending rate
message.domain.api_scenario_requestFakeErrorRate=Request fake error rate
message.domain.api_scenario_assertionPassRate=Assertion pass rate
# Test plan
message.domain.test_plan_name=Name
message.domain.test_plan_stage=Testing phase
message.domain.test_plan_status=Test plan status
message.domain.test_plan_description=Description
message.domain.test_plan_tags=Tag
message.domain.test_plan_createUser=Create user
message.domain.test_plan_updateUser=Update user
message.domain.test_plan_createTime=Create time
message.domain.test_plan_updateTime=Update time
message.domain.test_plan_plannedStartTime=Planned start time
message.domain.test_plan_plannedEndTime=Planned end time
message.domain.test_plan_actualStartTime=Actual start time
message.domain.test_plan_actualEndTime=Actual end time
message.domain.test_plan_num=Num
message.domain.test_plan_type=Type
message.domain.test_plan_reportName=Report name
message.domain.test_plan_reportUrl=Report link
message.domain.test_plan_reportShareUrl=Share report link
message.domain.test_plan_startTime=Start time; the time when the plan starts execution
message.domain.test_plan_endTime=End time; the time when the plan ends execution
message.domain.test_plan_execStatus=Execution status
message.domain.test_plan_resultStatus=Result status
message.domain.test_plan_passRate=Passing rate
message.domain.test_plan_passThreshold=Pass threshold
message.domain.test_plan_executeRate=Exacutive rate

# case Review
message.domain.case_review_name=Name
message.domain.case_review_num=ID
message.domain.case_review_status=Review status
message.domain.case_review_description=Description
message.domain.case_review_tags=Tag
message.domain.case_review_createUser=Create user
message.domain.case_review_createTime=Create time
message.domain.case_review_updateTime=Update time
message.domain.case_review_updateUser=Update user
message.domain.case_review_endTime=Review end time
message.domain.case_review_startTime=Review start time
message.domain.case_review_passRate=Pass rate
message.domain.case_review_caseCount=Case count
message.domain.case_review_reviewPassRule=Review rules
# ç¼ºé·
message.domain.bug_num=Business ID
message.domain.bug_title=Defect title
message.domain.bug_assignUser=Designed
message.domain.bug_platform=Defective platform
message.domain.bug_tag=Tag
message.domain.bug_status=Status
message.domain.bug_createUser=Create user
message.domain.bug_updateUser=Update user
message.domain.bug_createTime=Create time
message.domain.bug_updateTime=Update time
message.domain.bug_deleteUser=Delete user
message.domain.bug_deleteTime=Delete time
message.domain.bug_handleUser=Processor
message.domain.bug_sync_platform=Platform
message.domain.bug_sync_total_count=Total
#UI
message.domain.ui_name=Scenario name
message.domain.ui_level=Case level
message.domain.ui_principal=Responsible
message.domain.ui_stepTotal=Total number of steps
message.domain.ui_tags=Tag
message.domain.ui_status=Status
message.domain.ui_num=Business ID
message.domain.ui_lastResult=Final execution result
message.domain.ui_createUser=Create user
message.domain.ui_updateUser=Update user
message.domain.ui_createTime=Create time
message.domain.ui_updateTime=Update time
message.domain.ui_deleteUser=Delete user
message.domain.ui_deleteTime=Delete time
message.domain.ui_description=Description
#æ§è½
message.domain.load_name=Test name
message.domain.load_status=Status
message.domain.load_num=Business ID
message.domain.load_createUser=Create user
message.domain.load_updateUser=Update user
message.domain.load_createTime=Create time
message.domain.load_updateTime=Update time
message.domain.load_description=Description
#å®æ¶ä»»å¡
message.domain.schedule_key=qrtz UUID
message.domain.schedule_type=Execute Type
message.domain.schedule_value=Cron expression
message.domain.schedule_job=Schedule Job Class Name
message.domain.schedule_name=Name
message.domain.schedule_config=Configuration
message.domain.schedule_createUser=Create user
message.domain.schedule_updateUser=Update user
message.domain.schedule_createTime=Create time
message.domain.schedule_updateTime=Update time
message.domain.schedule_enable=Whether to turn on
message.domain.schedule_resourceType=Resource type

resource_pool_not_exist=Resource pool does not exist

#file management
file_module.not.exist=File module does not exist
module.branches.size.limit=Module branches size limit {0}
drag_node.not.exist=Drag node is not exist
drop_node.not.exist=Drop node is not exist
file_module.parent.not.exist=File module parent does not exist
upload.file.error=Upload file error
file.not.exist=File does not exist
file.some.not.exist=Some file not exist
old.file.not.exist=Old file does not exist
latest.file.not.exist=New version file note exist
file.not.jar=Not jar file
change.jar.enable=Change jar file enable
file.name.exist=File name already exists
log.delete_module=has be deleted
file.module.root=root module
folder.error=File folder error
file.log.move_to=move to
file.log.change_file_module=File has be moved
file.log.next=next
file.log.previous=behind
file.log.upload=upload
file.log.repository.add=Add repository file
file.log.re-upload=Re-upload file
file.log.upload_file=Upload file
file.log.pull=Pull file
file.log.association=has association file
file.log.association.update=updated file
file.log.association.delete=delete file
file.log.transfer.association=transfer and association file
file.name.cannot.be.empty=File name cannot be empty
file.size.is.too.large=File cannot exceed 50M
file.is.empty=File is empty
file.name.error=File name error
#file management over

# template
project_template_permission_error=The project template is not turned on
third_part_config_is_null=Third party configuration cannot be empty
plugin_bug_template_remark=Templates are automatically obtained by the system and do not support editing and viewing
default_template=default template

global_params=Global params.json
env_info_all=All environment info.json

# custom_function
custom_function_already_exist= custom function name already exist
permission.project_custom_function.name=Common script
permission.project_custom_function.execute=Test

message.domain.report.name=Report name


permission.project_task_center.name=Task center
permission.project_case_task_center.name=System real time task
permission.project_schedule_task_center.name=Schedule task
permission.project_case_task_center.exec=exec/stop