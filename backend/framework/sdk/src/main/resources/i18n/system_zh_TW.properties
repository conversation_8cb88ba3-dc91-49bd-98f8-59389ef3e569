excel.parse.error=Excelè§£æå¤±æ
id.not_blank=IDä¸è½çºç©º
permission.system_user.invite=éè«ç¨æ¶
role.not.global.system=è§è²ä¸æ¯çºå¨å±ç³»çµ±è§è²
role.not.contains.member=è§è²ä¸åå«ç³»çµ±æå¡è§è²
schedule.cron.error=Cronè¡¨éå¼é¯èª¤
user.not.login=æªç²åå°ç»éç¨æ¶
user.not.empty=ç¨æ¶ä¸å¢çºç©º
user.not.exist=ç¨æ¶ä¸å­å¨
personal.no.permission=ç¡æ¬æä½éæ¬äººè³¬æ¶
personal.change.password=ä¿®æ¹äºå¯ç¢¼
personal.change.info=ä¿®æ¹äºä¿¡æ¯
personal.user.name=ç¨æ¶åç¨±
personal.user.phone=ææ©è
personal.user.email=ç¨æ¶éµç®±
default.module=é»èªæ¨¡å¡
file_format_does_not_meet_requirements=æä»¶æ ¼å¼ä¸ç¬¦å
auth_source.id.not_blank=èªè­æºIDä¸è½çºç©º
auth_source.status.length_range=èªè­æºçæé·åº¦å¿é å¨{min}å{max}ä¹é
auth_source.status.not_blank=èªè­æºçæä¸è½çºç©º
license.id.not_blank=License IDä¸è½çºç©º
message_task.id.not_blank=æ¶æ¯éç¥ä»»åIDä¸è½çºç©º
message_task.type.not_blank=æ¶æ¯éç¥ä»»åé¡åä¸è½çºç©º
message_task.type.length_range=æ¶æ¯éç¥ä»»åé¡åé·åº¦å¿é å¨{min}å{max}ä¹é
message_task.event.not_blank=æ¶æ¯éç¥ä»»åäºä»¶ä¸è½çºç©º
message_task.event.length_range=æ¶æ¯éç¥ä»»åäºä»¶é·åº¦å¿é å¨{min}å{max}ä¹é
message_task.receiver.not_blank=æ¶æ¯éç¥ä»»åæ¥æ¶èä¸è½çºç©º
message_task.receiver.length_range=æ¶æ¯éç¥ä»»åæ¥æ¶èé·åº¦å¿é å¨{min}å{max}ä¹é
message_task.task_type.not_blank=æ¶æ¯éç¥ä»»åä»»åé¡åä¸è½çºç©º
message_task.task_type.length_range=æ¶æ¯éç¥ä»»åä»»åé¡åé·åº¦å¿é å¨{min}å{max}ä¹é
message_task.test_id.not_blank=æ¶æ¯éç¥ä»»åæ¸¬è©¦IDä¸è½çºç©º
message_task.test_id.length_range=æ¶æ¯éç¥ä»»åæ¸¬è©¦IDé·åº¦å¿é å¨{min}å{max}ä¹é
message_task.project_id.not_blank=æ¶æ¯éç¥ä»»åé ç®IDä¸è½çºç©º
message_task.project_id.length_range=æ¶æ¯éç¥ä»»åé ç®IDé·åº¦å¿é å¨{min}å{max}ä¹é
message_task_blob.id.not_blank=æ¶æ¯éç¥ä»»åIDä¸è½çºç©º
notification.id.not_blank=æ¶æ¯éç¥IDä¸è½çºç©º
notification.type.not_blank=æ¶æ¯éç¥é¡åä¸è½çºç©º
notification.type.length_range=æ¶æ¯éç¥é¡åé·åº¦å¿é å¨{min}å{max}ä¹é
notification.receiver.not_blank=æ¶æ¯éç¥æ¥æ¶èä¸è½çºç©º
notification.receiver.length_range=æ¶æ¯éç¥æ¥æ¶èé·åº¦å¿é å¨{min}å{max}ä¹é
notification.title.not_blank=æ¶æ¯éç¥æ¨é¡ä¸è½çºç©º
notification.title.length_range=æ¶æ¯éç¥æ¨é¡é·åº¦å¿é å¨{min}å{max}ä¹é
notification.status.not_blank=æ¶æ¯éç¥çæä¸è½çºç©º
notification.status.length_range=æ¶æ¯éç¥çæé·åº¦å¿é å¨{min}å{max}ä¹é
notification.operator.not_blank=æ¶æ¯éç¥æä½èä¸è½çºç©º
notification.operator.length_range=æ¶æ¯éç¥æä½èé·åº¦å¿é å¨{min}å{max}ä¹é
notification.operation.not_blank=æ¶æ¯éç¥æä½ä¸è½çºç©º
notification.operation.length_range=æ¶æ¯éç¥æä½é·åº¦å¿é å¨{min}å{max}ä¹é
notification.resource_id.not_blank=æ¶æ¯éç¥è³æºIDä¸è½çºç©º
notification.resource_id.length_range=æ¶æ¯éç¥è³æºIDé·åº¦å¿é å¨{min}å{max}ä¹é
notification.resource_type.not_blank=æ¶æ¯éç¥è³æºé¡åä¸è½çºç©º
notification.resource_type.length_range=æ¶æ¯éç¥è³æºé¡åé·åº¦å¿é å¨{min}å{max}ä¹é
notification.resource_name.not_blank=æ¶æ¯éç¥è³æºåç¨±ä¸è½çºç©º
notification.resource_name.length_range=æ¶æ¯éç¥è³æºåç¨±é·åº¦å¿é å¨{min}å{max}ä¹é
novice_statistics.id.not_blank=æ°ææIDä¸è½çºç©º
novice_statistics.guide_step.not_blank=æ°æææ­¥é©ä¸è½çºç©º
novice_statistics.guide_step.length_range=æ°æææ­¥é©é·åº¦å¿é å¨{min}å{max}ä¹é
operation_log.id.not_blank=æä½æ¥èªIDä¸è½çºç©º
operation_log.project_id.not_blank=æä½æ¥èªé ç®IDä¸è½çºç©º
operation_log.project_id.length_range=æä½æ¥èªé ç®IDé·åº¦å¿é å¨{min}å{max}ä¹é
operation_log.organization_id.not_blank=æä½æ¥èªçµç¹IDä¸è½çºç©º
operation_log.organization_id.length_range=æä½æ¥èªçµç¹IDé·åº¦å¿é å¨{min}å{max}ä¹é
operation_log.batch_id.not_blank=æä½æ¥èªæ¹æ¬¡IDä¸è½çºç©º
operation_log_resource.id.not_blank=æä½æ¥èªè³æºIDä¸è½çºç©º
operation_log_resource.operating_log_id.not_blank=æä½æ¥èªè³æºæä½æ¥èªIDä¸è½çºç©º
operation_log_resource.operating_log_id.length_range=æä½æ¥èªè³æºæä½æ¥èªIDé·åº¦å¿é å¨{min}å{max}ä¹é
operation_log_resource.source_id.not_blank=æä½æ¥èªè³æºä¾æºIDä¸è½çºç©º
operation_log_resource.source_id.length_range=æä½æ¥èªè³æºä¾æºIDé·åº¦å¿é å¨{min}å{max}ä¹é
plugin_blob.id.not_blank=æä»¶IDä¸è½çºç©º
quota.id.not_blank=éé¡IDä¸è½çºç©º
schedule.id.not_blank=å®æä»»åIDä¸è½çºç©º
schedule.type.not_blank=å®æä»»åé¡åä¸è½çºç©º
schedule.type.length_range=å®æä»»åé¡åé·åº¦å¿é å¨{min}å{max}ä¹é
schedule.value.not_blank=å®æä»»åå¼ä¸è½çºç©º
schedule.value.length_range=å®æä»»åå¼é·åº¦å¿é å¨{min}å{max}ä¹é
schedule.job.not_blank=å®æä»»åä¸è½çºç©º
schedule.job.length_range=å®æä»»åé·åº¦å¿é å¨{min}å{max}ä¹é
schedule.create_user.not_blank=å®æä»»ååµå»ºäººä¸è½çºç©º
schedule.create_user.length_range=å®æä»»ååµå»ºäººé·åº¦å¿é å¨{min}å{max}ä¹é

#system
system_parameter.param_key.not_blank=ç³»çµ±åæ¸Keyä¸è½çºç©º
system_parameter.type.not_blank=ç³»çµ±åæ¸é¡åä¸è½çºç©º
system_parameter.type.length_range=ç³»çµ±åæ¸é¡åé·åº¦å¿é å¨{min}å{max}ä¹é
system_model_not_exist=æ¨¡åè³è¨ä¸å­å¨
system_model_not_enable=æ¨¡åæªåç¨
system_model_test_link_error=æ¨¡åé£çµå¤±æï¼è«æª¢æ¥éç½®
system_model_test_chat_error=æ¨¡åå¼å«é¯èª¤ï¼é¯èª¤ç¢¼ï¼
system_model_name_exist=æ¨¡ååç¨±
system_model_name_exist_label=å·²è¢«ç³»çµ±ä¸­å¶ä»ä½¿ç¨èä½ç¨ï¼è«æ´æå¯ä¸åç¨±å¾éè©¦

# èµæºæ± 
test_resource.id.not_blank=è³æºæ± ç¯é»IDä¸è½çºç©º
test_resource.test_resource_pool_id.not_blank=è³æºæ± IDä¸è½çºç©º
test_resource.test_resource_pool_id.length_range=è³æºæ± IDé·åº¦å¿é å¨{min}å{max}ä¹é
test_resource.status.not_blank=è³æºæ± ç¯é»çæä¸è½çºç©º
test_resource.status.length_range=è³æºæ± ç¯é»çæé·åº¦å¿é å¨{min}å{max}ä¹é
test_resource_pool.node_must_be_one=è³æºæ± ç¯é»æå¤çº1å
test_resource_pool.node_must_have_one=è³æºæ± ç¯é»è³å°ä¿çä¸å
test_resource_pool.id.not_blank=è³æºæ± IDä¸è½çºç©º
test_resource_pool.name.not_blank=è³æºæ± åç¨±ä¸è½çºç©º
test_resource_pool.name.length_range=è³æºæ± åç¨±é·åº¦å¿é å¨{min}å{max}ä¹é
test_resource_pool.type.not_blank=è³æºæ± é¡åä¸è½çºç©º
test_resource_pool.type.length_range=è³æºæ± é¡åé·åº¦å¿é å¨{min}å{max}ä¹é
test_resource_pool.status.not_blank=è³æºæ± çæä¸è½çºç©º
test_resource_pool.status.length_range=è³æºæ± çæé·åº¦å¿é å¨{min}å{max}ä¹é
user.not.delete=ç¨æ¶ä¸è½åªé¤
user.not.disable=ç¨æ¶ä¸è½ç¦ç¨
user.id.not_blank=ç¨æ¶IDä¸è½çºç©º
user.name.not_blank=ç¨æ¶åç¨±ä¸è½çºç©º
user.name.length_range=ç¨æ¶åç¨±é·åº¦å¿é å¨ {min} å {max} ä¹é
user.phone.not_blank=ç¨æ¶ææ©èä¸è½çºç©º
user.phone.error=ææ©èè¼¸å¥é¯èª¤
user.password.error=é©è­ç¨æ¶å¯ç¢¼å¤±æ
user.password.not.blank=ç¨æ¶å¯ç¢¼ä¸è½çºç©º
user.email.not_blank=ç¨æ¶emailä¸è½çºç©º
user.email.length_range=ç¨æ¶emailé·åº¦å¿é å¨{min}å{max}ä¹é
user.email.hi=ä½ å¥½
user.email.invite_ms=éè«ä½ å å¥MeterSphere
user.email.invite_click=é»æå å¥
user.email.invite_tips=å¦ææéç¡æ³é»æï¼è«ç´æ¥è¨ªåä»¥ä¸éæ¥ï¼
user.email.invite_limited_time=æ­¤éæ¥èªç¼éä¹æèµ·24å°æå¾éæ
user.email.repeat=éµç®±éè¤
user.email.import.in_system=ç³»çµ±å·²å­å¨
user.reset.password=éç½®å¯ç¢¼
user.delete=åªé¤ç¨æ¶
user.enable=åç¨ç¨æ¶
user.disable=ç¦ç¨ç¨æ¶
user.add.project=æ·»å é ç®
user.add.org=æ·»å çµç¹
user.add.group=æ·»å ç¨æ¶çµ
user.invite.email=éµç®±éè«
register.by.invite=éééµç®±éè«è¨»åãéè«äººï¼
user.not.invite.or.expired=è©²ç¨æ¶æ²æè¢«éè«æéè«å·²éæ
user.email.invalid=ç¨æ¶ email æ ¼å¼ä¸æ­£ç¢º
user.status.not_blank=ç¨æ¶çæä¸è½çºç©º
user.status.length_range=ç¨æ¶çæé·åº¦å¿é å¨{min}å{max}ä¹é
user.source.not_blank=ç¨æ¶ä¾æºä¸è½çºç©º
user.source.length_range=ç¨æ¶ä¾æºé·åº¦å¿é å¨{min}å{max}ä¹é
user.create_user.not_blank=ç¨æ¶åµå»ºäººä¸è½çºç©º
user.create_user.length_range=ç¨æ¶åµå»ºäººé·åº¦å¿é å¨{min}å{max}ä¹é
user_extend.id.not_blank=ç¨æ¶IDä¸è½çºç©º
user_key.id.not_blank=ç¨æ¶ApiKey IDä¸è½çºç©º
user_key.create_user.not_blank=ç¨æ¶ApiKeyåµå»ºäººä¸è½çºç©º
user_key.create_user.length_range=ç¨æ¶ApiKeyåµå»ºäººé·åº¦å¿é å¨{min}å{max}ä¹é
user_key.access_key.not_blank=ç¨æ¶ApiKey access keyä¸è½çºç©º
user_key.access_key.length_range=ç¨æ¶ApiKey access keyé·åº¦å¿é å¨{min}å{max}ä¹é
user_key.secret_key.not_blank=ç¨æ¶ApiKey secret keyä¸è½çºç©º
user_key.secret_key.length_range=ç¨æ¶ApiKey secret keyé·åº¦å¿é å¨{min}å{max}ä¹é
user.info.not_empty=ç¨æ¶ä¿¡æ¯ä¸è½çºç©º
user.organizationId.not_blank=ç¨æ¶çµç¹ä¸è½çºç©º
user.projectId.not_blank=ç¨æ¶é ç®ä¸è½çºç©º
user_role.id.not_blank=ç¨æ¶çµIDä¸è½çºç©º
user_role.name.not_blank=ç¨æ¶çµåç¨±ä¸è½çºç©º
user_role.name.length_range=ç¨æ¶çµåç¨±é·åº¦å¿é å¨{min}å{max}ä¹é
user_role.system.not_blank=æ¯å¦æ¯ç³»çµ±ç¨æ¶çµä¸è½çºç©º
user_role.system.length_range=æ¯å¦æ¯ç³»çµ±ç¨æ¶çµé·åº¦å¿é å¨{min}å{max}ä¹é
user_role.type.not_blank=ç¨æ¶çµé¡åä¸è½çºç©º
user_role.type.length_range=ç¨æ¶çµé¡åé·åº¦å¿é å¨{min}å{max}ä¹é
user_role.create_user.not_blank=ç¨æ¶çµåµå»ºäººä¸è½çºç©º
user_role.create_user.length_range=ç¨æ¶çµåµå»ºäººé·åº¦å¿é å¨{min}å{max}ä¹é
user_role.scope_id.not_blank=ç¨æ¶çµæç¨ç¯åä¸è½çºç©º
user_role.scope_id.length_range=ç¨æ¶çµæç¨ç¯åé·åº¦å¿é å¨{min}å{max}ä¹é
user_role_permission.id.not_blank=ç¨æ¶çµæ¬éIDä¸è½çºç©º
user_role_permission.role_id.not_blank=ç¨æ¶çµæ¬éç¨æ¶çµIDä¸è½çºç©º
user_role_permission.role_id.length_range=ç¨æ¶çµæ¬éç¨æ¶çµIDé·åº¦å¿é å¨{min}å{max}ä¹é
user_role_permission.permission_id.not_blank=ç¨æ¶çµæ¬éæ¬éIDä¸è½çºç©º
user_role_permission.permission_id.length_range=ç¨æ¶çµæ¬éæ¬éIDé·åº¦å¿é å¨{min}å{max}ä¹é
user_role_permission.module_id.not_blank=ç¨æ¶çµæ¬éæ¨¡å¡IDä¸è½çºç©º
user_role_permission.module_id.length_range=ç¨æ¶çµæ¬éæ¨¡å¡IDé·åº¦å¿é å¨{min}å{max}ä¹é
user_role_relation.id.not_blank=ç¨æ¶çµéä¿IDä¸è½çºç©º
user_role_relation.user_id.not_blank=ç¨æ¶çµéä¿ç¨æ¶IDä¸è½çºç©º
user_role_relation.user_id.length_range=ç¨æ¶çµéä¿ç¨æ¶IDé·åº¦å¿é å¨{min}å{max}ä¹é
user_role_relation.role_id.not_blank=ç¨æ¶çµéä¿ç¨æ¶çµIDä¸è½çºç©º
user_role_relation.role_id.length_range=ç¨æ¶çµéä¿ç¨æ¶çµIDé·åº¦å¿é å¨{min}å{max}ä¹é
user_role_relation.source_id.not_blank=ç¨æ¶çµéä¿ä¾æºIDä¸è½çºç©º
user_role_relation.source_id.length_range=ç¨æ¶çµéä¿ä¾æºIDé·åº¦å¿é å¨{min}å{max}ä¹é
organization.id.not_blank=çµç¹IDä¸è½çºç©º
organization.name.not_blank=çµç¹åç¨±ä¸è½çºç©º
organization.name.length_range=çµç¹åç¨±é·åº¦å¿é å¨{min}å{max}ä¹é
organization.create_user.not_blank=çµç¹åµå»ºäººä¸è½çºç©º
organization.create_user.length_range=çµç¹åµå»ºäººé·åº¦å¿é å¨{min}å{max}ä¹é
and_add_organization_admin=ä¸¦æ·»å çµç¹ç®¡çå¡
organization_add_member_ids_empty=çµç¹æ·»å æå¡ä¸è½ç²ç©º
organization_not_exist=çµç¹ä¸å­å¨
organization_member_not_exist=çµç¹æå¡ä¸å­å¨
global_user_role_permission_error=æ²ææ¬éæä½éå¨å±ç¨æ¶çµ
global_user_role_exist_error=å¨å±ç¨æ¶çµå·²å­å¨
global_user_role_relation_system_permission_error=æ²ææ¬éæä½éç³»çµ±ç´å¥ç¨æ¶çµ
global_user_role_limit_error=è³å°éè¦æä¸ä¸ªç¨æ·ç»
organization_user_role_permission_error=æ²ææ¬éæä½éçµç¹ç¨æ¶çµ
project_user_role_permission_error=æ²ææ¬éæä½éé ç®ç¨æ¶çµ
no_global_user_role_permission_error=æ²ææ¬éæä½å¨å±ç¨æ¶çµ
user_role_exist=ç¨æ¶çµå·²å­å¨
user_role_not_exist=ç¨æ¶çµä¸å­å¨
user_role_not_edit=ç¨æ¶çµç¡æ³ç·¨è¼¯
at_least_one_user_role_require=è³å°éè¦ä¸åç¨æ¶çµ
org_at_least_one_user_role_require=çµç¹æå¡è³å°æä¸åä½¿ç¨èç¾¤çµï¼å¦éå¾çµç¹ä¸­åªé¤æå¡è«å¨æå¡æ¸å®æä½ï¼
project_at_least_one_user_role_require=é¡¹ç®æå¡è³å°æä¸åä½¿ç¨èç¾¤çµï¼å¦éå¾é¡¹ç®ä¸­åªé¤æå¡è«å¨æå¡æ¸å®æä½ï¼
default_organization_not_allow_delete=é»èªçµç¹ç¡æ³åªé¤
organization_template_permission_error=æªéåçµç¹æ¨¡æ¿
# plugin
plugin.id.not_blank=IDä¸è½çºç©º
plugin.id.length_range=IDé·åº¦å¿é å¨{min}å{max}ä¹é´
plugin.name.not_blank=æä»¶åç§°ä¸è½çºç©º
plugin.name.length_range=æä»¶åç§°é·åº¦å¿é å¨{min}å{max}ä¹é´
plugin.plugin_id.not_blank=æä»¶IDï¼åç§°å çæ¬å·ï¼ä¸è½çºç©º
plugin.plugin_id.length_range=æä»¶IDï¼åç§°å çæ¬å·ï¼é·åº¦å¿é å¨{min}å{max}ä¹é´
plugin.file_name.not_blank=æä»¶åä¸è½çºç©º
plugin.file_name.length_range=æä»¶åé·åº¦å¿é å¨{min}å{max}ä¹é´
plugin.create_user.not_blank=åå»ºäººä¸è½çºç©º
plugin.create_user.length_range=åå»ºäººé·åº¦å¿é å¨{min}å{max}ä¹é´
plugin.scenario.not_blank=æä»¶ä½¿ç¨åºæ¯PAI/PLATFORMä¸è½çºç©º
plugin.scenario.length_range=æä»¶ä½¿ç¨åºæ¯PAI/PLATFORMé·åº¦å¿é å¨{min}å{max}ä¹é´
plugin.exist=æä»¶åç¨±å·²å­å¨
plugin.type.exist=æä»¶é¡åå·²å­å¨
plugin.script.exist=è³æ¬idéè¤
plugin.script.format=è³æ¬æ ¼å¼é¯èª¤
plugin.parse.error=æä»¶è§£æå¤±æ, è«éæ°ä¸å³
# serviceIntegration
service_integration.id.not_blank=IDä¸è½çºç©º
service_integration.id.length_range=IDé·åº¦å¿é å¨{min}å{max}ä¹é
service_integration.plugin_id.not_blank=æä»¶çIDä¸è½çºç©º
service_integration.plugin_id.length_range=æä»¶çIDé·åº¦å¿é å¨{min}å{max}ä¹é
service_integration.organization_id.not_blank=çµç¹IDä¸è½çºç©º
service_integration.organization_id.length_range=çµç¹IDé·åº¦å¿é å¨{min}å{max}ä¹é
service_integration_exist_error=æåéæéç½®å·²å­å¨
service_integration.configuration.not_blank=æå¡éæéç½®ä¸è½çºç©º
# customField
permission.system_custom_field.name=èªå®ç¾©å­æ®µ
custom_field.exist=èªå®ç¾©å­æ®µå·²å­å¨
template.exist=æ¨¡æ¿å·²å­å¨
status_item.not.exist=çæé ä¸å­å¨
status_item.exist=çæé å·²å­å¨
custom_field_option.field_id.not_blank=èªå®ç¾©å­æ®µIDä¸è½çºç©º
custom_field_option.field_id.length_range=èªå®ç¾©å­æ®µIDé·åº¦å¿é å¨{min}å{max}ä¹é
custom_field_option.value.not_blank=é¸é å¼ä¸è½çºç©º
custom_field_option.value.length_range=é¸é å¼é·åº¦å¿é å¨{min}å{max}ä¹é
custom_field_option.text.not_blank=é¸é å¼åç¨±ä¸è½çºç©º
custom_field_option.text.length_range=é¸é å¼åç¨±é·åº¦å¿é å¨{min}å{max}ä¹é
custom_field_option.internal.not_blank=é¸é å¼æ¯å¦å§ç½®ä¸è½ç²ç©º
custom_field_option.pos.not_blank=é¸é å¼é åºä¸è½ç²ç©º

# permission
permission.system_plugin.name=æä»¶
permission.system_organization_project.name=çµç¹èé ç®
permission.system_user.name=ç¨æ¶
permission.system_user_role.name=ç¨æ¶çµ
permission.system_test_resource_pool.name=è³æºæ± 
permission.system_parameter_setting.name=åæ¸è¨­ç½®
permission.system_parameter_setting_base.read=åºç¤è¨­ç½®-æ¥è©¢
permission.system_parameter_setting_base.update=åºç¤è¨­ç½®-ç·¨è¼¯
permission.system_parameter_setting_display.read=çé¢è¨­ç½®-æ¥è©¢
permission.system_parameter_setting_display.update=çé¢è¨­ç½®-ç·¨è¼¯
permission.system_parameter_setting_auth.read=èªè­è¨­ç½®-æ¥è©¢
permission.system_parameter_setting_auth.add=èªè­è¨­ç½®-åµå»º
permission.system_parameter_setting_auth.update=èªè­è¨­ç½®-ç·¨è¼¯
permission.system_parameter_setting_auth.delete=èªè­è¨­ç½®-åªé¤
permission.system_parameter_setting_memory_clean.read=å§å­æ¸ç-æ¥è©¢
permission.system_parameter_setting_memory_clean.update=å§å­æ¸ç-ç·¨è¼¯
permission.system_parameter_setting_qrcode.read=æç¢¼ç»å¥-æ¥è©¢
permission.system_parameter_setting_qrcode.update=æç¢¼ç»å¥-ç·¨è¼¯
permission.system_parameter_setting_ai_model.read=AIæ¨¡åè¨­å®-æ¥è©¢
permission.system_parameter_setting_ai_model.update=AIæ¨¡åè¨­å®-ç·¨è¼¯
permission.organization_user_role.name=ç¨æ¶çµ
permission.organization_member.name=æå¡
permission.service_integration.name=æåéæ
permission.system_auth=ææ¬
permission.system_organization_project_member.add=æ·»å æå
permission.system_organization_project_member.update=ç·¨è¼¯æå¡
permission.system_organization_project_member.delete=ç§»é¤æå¡
permission.system_operation_log.name=æ¥å¿
permission.organization_operation_log.name=æ¥å¿
permission.personal_settings=åäººè¨­ç½®
permission.my_settings=æçè¨­ç½®
permission.api_key=APIKEY
permission.my_settings_personal_info=åäººä¿¡æ¯
permission.organization_project.recover=æ¤é·åªé¤
permission.organization_member.add=æ·»å 
permission.organization_member.invite=éè«ç¨æ¶
permission.organization_member.update=ç·¨è¼¯
permission.organization_member.delete=ç§»é¤
# template
permission.system_template_custom_field.name=æ¨¡æ¿åå­æ®µçéè¯éä¿
template_custom_field.exist=æ¨¡æ¿åå­æ®µçéè¯éä¿å·²å­å¨
template_custom_field.id.not_blank=IDä¸è½çºç©º
template_custom_field.id.length_range=IDé·åº¦å¿é å¨{min}å{max}ä¹é
template_custom_field.field_id.not_blank=å­æ®µIDä¸è½çºç©º
template_custom_field.field_id.length_range=å­æ®µIDé·åº¦å¿é å¨{min}å{max}ä¹é
template_custom_field.template_id.not_blank=æ¨¡çIDä¸è½çºç©º
template_custom_field.template_id.length_range=æ¨¡çIDé·åº¦å¿é å¨{min}å{max}ä¹é
template_custom_field.default_value.length_range=é»èªå¼é·åº¦å¿é å°æ¼{max}
permission.organization_custom_field.name=èªå®ç¾©å­æ®µ
permission.organization_template.name=æ¨¡æ¿
permission.system_organization_template.enable=åç¨é ç®æ¨¡æ¿
# ç¶ææµ
permission.status_item.name=çæé 
status_item.id.not_blank=çæIDä¸è½çºç©º
status_item.id.length_range=çæIDé·åº¦å¿é å¨{min}å{max}ä¹é
status_item.name.not_blank=çæåç¨±ä¸è½çºç©º
status_item.name.length_range=çæåç¨±é·åº¦å¿é å¨{min}å{max}ä¹é
status_item.scene.not_blank=ä½¿ç¨å ´æ¯ä¸è½çºç©º
status_item.scene.length_range=ä½¿ç¨å ´æ¯é·åº¦å¿é å¨{min}å{max}ä¹é
status_item.scope_type.not_blank=çµç¹æé ç®ç´å¥å­æ®µï¼PROJECT, ORGANIZATIONï¼ä¸è½çºç©º
status_item.scope_type.length_range=çµç¹æé ç®ç´å¥å­æ®µï¼PROJECT, ORGANIZATIONï¼é·åº¦å¿é å¨{min}å{max}ä¹é
status_item.scope_id.not_blank=çµç¹æé ç®IDä¸è½çºç©º
status_item.scope_id.length_range=çµç¹æé ç®IDé·åº¦å¿é å¨{min}å{max}ä¹é
status_definition.status_id.not_blank=çæIDä¸è½çºç©º
status_definition.status_id.length_range=çæIDé·åº¦å¿é å¨{min}å{max}ä¹é
status_definition.definition_id.not_blank=çæå®ç¾©ID(å¨ä»£ç¢¼ä¸­å®ç¾©)ä¸è½çºç©º
status_definition.definition_id.length_range=çæå®ç¾©ID(å¨ä»£ç¢¼ä¸­å®ç¾©)é·åº¦å¿é å¨{min}å{max}ä¹é
status_flow.id.not_blank=IDä¸è½çºç©º
status_flow.id.length_range=IDé·åº¦å¿é å¨{min}å{max}ä¹é
status_flow.from_id.not_blank=èµ·å§çæIDä¸è½çºç©º
status_flow.from_id.length_range=èµ·å§çæIDé·åº¦å¿é å¨{min}å{max}ä¹é
status_flow.to_id.not_blank=ç®ççæIDä¸è½çºç©º
status_flow.to_id.length_range=ç®ççæIDé·åº¦å¿é å¨{min}å{max}ä¹é
# message
user.remove=å·²è¢«ç§»é¤
alert_others=éç¥äºº

current_user_local_config_not_exist=å½åç¨æ·æ¬å°éç½®ä¸å­å¨
current_user_local_config_exist=å½åç¨æ·æ¬å°éç½®å·²å­å¨
# user_local_config
user_local_config.id.not_blank=ç¨æ·æ¬å°éç½®IDä¸è½çºç©º
user_local_config.id.length_range=ç¨æ·æ¬å°éç½®IDé·åº¦å¿é å¨{min}å{max}ä¹é´
user_local_config.user_url.not_blank=ç¨æ·æ¬å°éç½®ç¨æ·URLä¸è½çºç©º
user_local_config.user_url.length_range=ç¨æ·æ¬å°éç½®ç¨æ·URLé·åº¦å¿é å¨{min}å{max}ä¹é´
user_local_config.type.not_blank=ç¨æ·æ¬å°éç½®ç±»åä¸è½çºç©º
user_local_config.type.length_range=ç¨æ·æ¬å°éç½®ç±»åé·åº¦å¿é å¨{min}å{max}ä¹é´
current_user_local_config_not_validate=å½åç¨æ·æ¬å°éç½®ä¸åæ³

# operation_history
operation_history.id.not_blank=è®æ´è¨é ID ä¸è½çºç©º
operation_history.project_id.not_blank=è®æ´è¨éé ç® ID ä¸è½çºç©º
operation_history.project_id.length_range=è®æ´è¨éé ç® ID é·åº¦å¿é å¨{min}å{max}ä¹é
operation_history.type.not_blank=è®æ´è¨éæä½é¡åä¸è½çºç©º
operation_history.type.length_range=è®æ´è¨éæä½é¡åé·åº¦å¿é å¨{min}å{max}ä¹é
operation_history.source_id.not_blank=è®æ´è¨éè³æº ID ä¸è½çºç©º
operation_history.version_id.not_blank=è®æ´è¨éçæ¬ ID ä¸è½çºç©º
operation_history.version_id.length_range=è®æ´è¨éçæ¬ ID é·åº¦å¿é å¨{min}å{max}ä¹é

permission.organization_task_center.name=ä»»åä¸­å¿
permission.organization_task_center.stop=åæ­¢
permission.case_task_center.read=ç³»çµ±å³æä»»å-æ¥è©¢
permission.case_task_center.exec=ç³»çµ±å³æä»»å-å·è¡/åæ­¢
permission.case_task_center.delete=ç³»çµ±å³æä»»å-åªé¤
permission.schedule_task_center.read=ç³»çµ±å¾èºä»»å-æ¥è©¢
permission.schedule_task_center.update=ç³»çµ±å¾èºä»»å-ç·¨è¼¯
permission.schedule_task_center.delete=ç³»çµ±å¾èºä»»å-åªé¤
user_open_source_max=ç³»çµ±ç¨æ¶æ¸è¶é¡ï¼{num}äººï¼ï¼ç¹¼çºæ·»å ç¨æ¶å¯ç³è«ä¼æ¥­çé©ç¨
user_dept_max=ç³»çµ±ç¨æ¶æ¸è¶é¡ï¼{num}äººï¼ï¼ç¹¼çºæ·»å ç¨æ¶å¯ç³è«ä¼æ¥­çæ´å®¹

# file_upload
file_upload.size_limit=ä¸å³æä»¶å¤§å°è¶éç³»çµ±éå¶

#æ¨¡å
model_source.name.not_blank=æ¨¡ååç¨±ä¸è½çºç©º
model_source.type.not_blank=æ¨¡åé¡åä¸è½çºç©º
model_source.provider.not_blank=æ¨¡åæä¾åä¸è½çºç©º
model_source.avatar.not_blank=æ¨¡ååæ¨ä¸è½çºç©º
model_source.permission_type.not_blank=æ¨¡åæå±¬é¡åä¸è½çºç©º
model_source.status.not_blank=æ¨¡åçæä¸è½çºç©º
model_source.owner.not_blank=æ¨¡åææèä¸è½çºç©º
model_source.owner_type.not_blank=æ¨¡åææèé¡åä¸è½çºç©º
model_source.base_name.not_blank=æ¨¡ååºç¤åç¨±ä¸è½çºç©º
model_source.app_key.not_blank=æ¨¡åæç¨Keyä¸è½çºç©º
model_source.api_url.not_blank=æ¨¡åå°åä¸è½çºç©º