excel.parse.error=Excel parse error
id.not_blank=Id must not be blank
permission.system_user.invite=Invite user
role.not.global.system=Role is not global system role
role.not.contains.member=Role not contains member
schedule.cron.error=Cron is error
user.not.login=User not login
user.not.exist=User not exist
personal.no.permission=No permission
personal.change.password=Changed password
personal.change.info=Changed information
personal.user.name=Name
personal.user.phone=Phone
personal.user.email=Email
default.module=Default module
user.not.empty=User can not empty
file_format_does_not_meet_requirements=File type error
auth_source.id.not_blank=Auth source id must not be blank
auth_source.status.length_range=Auth source status must be between {min} and {max} characters long
auth_source.status.not_blank=Auth source status must not be blank
license.id.not_blank=License id must not be blank
message_task.id.not_blank=Message task id must not be blank
message_task.type.not_blank=Message task type must not be blank
message_task.type.length_range=Message task type must be between {min} and {max} characters long
message_task.event.not_blank=Message task event must not be blank
message_task.event.length_range=Message task event must be between {min} and {max} characters long
message_task.receiver.not_blank=Message task receiver must not be blank
message_task.receiver.length_range=Message task receiver must be between {min} and {max} characters long
message_task.task_type.not_blank=Message task task type must not be blank
message_task.task_type.length_range=Message task task type must be between {min} and {max} characters long
message_task.test_id.not_blank=Message task test id must not be blank
message_task.test_id.length_range=Message task test id must be between {min} and {max} characters long
message_task.project_id.not_blank=Message task project id must not be blank
message_task.project_id.length_range=Message task project id must be between {min} and {max} characters long
message_task_blob.id.not_blank=Message task blob message task id must not be blank
notification.id.not_blank=Notification id must not be blank
notification.type.not_blank=Notification type must not be blank
notification.type.length_range=Notification type must be between {min} and {max} characters long
notification.receiver.not_blank=Notification receiver must not be blank
notification.receiver.length_range=Notification receiver must be between {min} and {max} characters long
notification.title.not_blank=Notification title must not be blank
notification.title.length_range=Notification title must be between {min} and {max} characters long
notification.status.not_blank=Notification status must not be blank
notification.status.length_range=Notification status must be between {min} and {max} characters long
notification.operator.not_blank=Notification operator must not be blank
notification.operator.length_range=Notification operator must be between {min} and {max} characters long
notification.operation.not_blank=Notification operation must not be blank
notification.operation.length_range=Notification operation must be between {min} and {max} characters long
notification.resource_id.not_blank=Notification resource id must not be blank
notification.resource_id.length_range=Notification resource id must be between {min} and {max} characters long
notification.resource_type.not_blank=Notification resource type must not be blank
notification.resource_type.length_range=Notification resource type must be between {min} and {max} characters long
notification.resource_name.not_blank=Notification resource name must not be blank
notification.resource_name.length_range=Notification resource name must be between {min} and {max} characters long
novice_statistics.id.not_blank=Novice statistics id must not be blank
novice_statistics.guide_step.not_blank=Novice statistics guide step must not be blank
novice_statistics.guide_step.length_range=Novice statistics guide step must be between {min} and {max} characters long
operation_log.id.not_blank=Operating log id must not be blank
operation_log.project_id.not_blank=Operating log project id must not be blank
operation_log.project_id.length_range=Operating log project id must be between {min} and {max} characters long
operation_log.organization_id.not_blank=Operation log organization id must not be blank
operation_log_resource.id.not_blank=Operating log resource id must not be blank
operation_log.organization_id.length_range=Operation log organization id must be between {min} and {max} characters long
operation_log.batch_id.not_blank=Operating log batch id must not be blank
operation_log_resource.operating_log_id.not_blank=Operating log resource operating log id must not be blank
operation_log_resource.operating_log_id.length_range=Operating log resource operating log id must be between {min} and {max} characters long
operation_log_resource.source_id.not_blank=Operating log resource source id must not be blank
operation_log_resource.source_id.length_range=Operating log resource source id must be between {min} and {max} characters long
plugin_blob.id.not_blank=Plugin blob plugin id must not be blank
quota.id.not_blank=Quota id must not be blank
schedule.id.not_blank=Schedule id must not be blank
schedule.type.not_blank=Schedule type must not be blank
schedule.type.length_range=Schedule type must be between {min} and {max} characters long
schedule.value.not_blank=Schedule value must not be blank
schedule.value.length_range=Schedule value must be between {min} and {max} characters long
schedule.job.not_blank=Schedule job must not be blank
schedule.job.length_range=Schedule job must be between {min} and {max} characters long
schedule.create_user.not_blank=Schedule create user must not be blank
schedule.create_user.length_range=Schedule create user must be between {min} and {max} characters long

#system
system_parameter.param_key.not_blank=System parameter param key must not be blank
system_parameter.type.not_blank=System parameter type must not be blank
system_parameter.type.length_range=System parameter type must be between {min} and {max} characters long
system_model_not_exist=Model information does not exist
system_model_not_enable=Model is not enabled
system_model_test_link_error=Model link failed, please check the configuration
system_model_test_chat_error=Model call error, error code:
system_model_name_exist=Model name
system_model_name_exist_label=has been used by another user in the system. Please change the unique name and try again

#资源池
test_resource.id.not_blank=Test resource id must not be blank
test_resource.test_resource_pool_id.not_blank=Test resource test resource pool id must not be blank
test_resource.test_resource_pool_id.length_range=Test resource test resource pool id must be between {min} and {max} characters long
test_resource.status.not_blank=Test resource status must not be blank
test_resource.status.length_range=Test resource status must be between {min} and {max} characters long
test_resource_pool.node_must_be_one=The maximum number of resource pool nodes is 1
test_resource_pool.node_must_have_one=Reserve at least one resource pool node
test_resource_pool.id.not_blank=Test resource pool id must not be blank
test_resource_pool.name.not_blank=Test resource pool name must not be blank
test_resource_pool.name.length_range=Test resource pool name must be between {min} and {max} characters long
test_resource_pool.type.not_blank=Test resource pool type must not be blank
test_resource_pool.type.length_range=Test resource pool type must be between {min} and {max} characters long
test_resource_pool.status.not_blank=Test resource pool status must not be blank
test_resource_pool.status.length_range=Test resource pool status must be between {min} and {max} characters long
user.not.delete=User can't delete
user.not.disable=User can't disable
user.id.not_blank=User id must not be blank
user.name.not_blank=Username must not be blank
user.name.length_range=Username must be between {min} and {max} characters long
user.phone.not_blank=User phone must not be blank
user.phone.error=Phone number error
user.password.error=Validate password error
user.password.not.blank=User password must not be blank
user.email.not_blank=User email must not be blank
user.email.length_range=User email must be between {min} and {max} characters long
user.email.hi=Hi
user.email.invite_ms=invited you join MeterSphere 
user.email.invite_click=Join in
user.email.invite_tips=If button can not click,please click url：
user.email.invite_limited_time=Url expires in 24 hours
user.email.repeat=Email already exists
user.email.import.in_system=Email was in system
user.reset.password=Reset password
user.delete=Delete user
user.enable=Enable user
user.disable=Disable user
user.add.project=Add project
user.add.org=Add organization
user.add.group=Add group
user.invite.email=Invite by email
register.by.invite=Register by invite. Inviter is :
user.not.invite.or.expired=User not invite or expired
user.email.invalid=User email is invalid
user.status.not_blank=User status must not be blank
user.status.length_range=User status must be between {min} and {max} characters long
user.source.not_blank=User source must not be blank
user.source.length_range=User source must be between {min} and {max} characters long
user.create_user.not_blank=User create user must not be blank
user.create_user.length_range=User create user must be between {min} and {max} characters long
user_extend.id.not_blank=User extend user id must not be blank
user_key.id.not_blank=User key id must not be blank
user_key.create_user.not_blank=User key create user must not be blank
user_key.create_user.length_range=User key create user must be between {min} and {max} characters long
user_key.access_key.not_blank=User key access key must not be blank
user_key.access_key.length_range=User key access key must be between {min} and {max} characters long
user_key.secret_key.not_blank=User key secret key must not be blank
user_key.secret_key.length_range=User key secret key must be between {min} and {max} characters long
user.info.not_empty=User info must not be blank
user.organizationId.not_blank=User organization must not be blank
user.projectId.not_blank=User project must not be blank
user_role.id.not_blank=User role id must not be blank
user_role.name.not_blank=User role name must not be blank
user_role.name.length_range=User role name must be between {min} and {max} characters long
user_role.system.not_blank=User role system must not be blank
user_role.system.length_range=User role system must be between {min} and {max} characters long
user_role.type.not_blank=User role type must not be blank
user_role.type.length_range=User role type must be between {min} and {max} characters long
user_role.create_user.not_blank=User role create user must not be blank
user_role.create_user.length_range=User role create user must be between {min} and {max} characters long
user_role.scope_id.not_blank=User role scope id must not be blank
user_role.scope_id.length_range=User role scope id must be between {min} and {max} characters long
user_role_permission.id.not_blank=User role permission id must not be blank
user_role_permission.role_id.not_blank=User role permission role id must not be blank
user_role_permission.role_id.length_range=User role permission role id must be between {min} and {max} characters long
user_role_permission.permission_id.not_blank=User role permission permission id must not be blank
user_role_permission.permission_id.length_range=User role permission permission id must be between {min} and {max} characters long
user_role_permission.module_id.not_blank=User role permission module id must not be blank
user_role_permission.module_id.length_range=User role permission module id must be between {min} and {max} characters long
user_role_relation.id.not_blank=User role relation id must not be blank
user_role_relation.user_id.not_blank=User role relation user id must not be blank
user_role_relation.user_id.length_range=User role relation user id must be between {min} and {max} characters long
user_role_relation.role_id.not_blank=User role relation role id must not be blank
user_role_relation.role_id.length_range=User role relation role id must be between {min} and {max} characters long
user_role_relation.source_id.not_blank=User role relation source id must not be blank
user_role_relation.source_id.length_range=User role relation source id must be between {min} and {max} characters long
organization.id.not_blank=Organization id must not be blank
organization.name.not_blank=Organization name must not be blank
organization.name.length_range=Organization name must be between {min} and {max} characters long
organization.create_user.not_blank=Organization create user must not be blank
organization.create_user.length_range=Organization create user must be between {min} and {max} characters long
member.id.not_empty=member cannot be empty
and_add_organization_admin=and add organization administrator
organization_add_member_ids_empty=organization add member cannot be empty
organization_not_exist=organization does not exist
organization_member_not_exist=organization member does not exist
global_user_role_permission_error=no global user role permission
global_user_role_exist_error=global user role already exists
global_user_role_relation_system_permission_error=no global user role relation system permission
global_user_role_limit_error=At least one user group is required
organization_user_role_permission_error=no organization user role permission
project_user_role_permission_error=no project user role permission
no_global_user_role_permission_error=no global user role permission
user_role_exist=User role already exists
user_role_not_exist=User role not exist
user_role_not_edit=User role can not edit
at_least_one_user_role_require=At least one user role require
org_at_least_one_user_role_require=Organization members must have at least one user group. If you need to delete members from the organization, please operate in the member list!
project_at_least_one_user_role_require=Project members must have at least one user group. If you need to delete members from the project, please operate in the member list!
default_organization_not_allow_delete=Default organization not allow delete
organization_template_permission_error=The organization template is not turned on
# plugin
plugin.id.not_blank=id cannot be empty
plugin.id.length_range=id length must be between {min} and {max}
plugin.name.not_blank=name cannot be empty
plugin.name.length_range=name length must be between {min} and {max}
plugin.plugin_id.not_blank=pluginId cannot be empty
plugin.plugin_id.length_range=pluginId length must be between {min} and {max}
plugin.file_name.not_blank=fileName cannot be empty
plugin.file_name.length_range=fileName length must be between {min} and {max}
plugin.create_user.not_blank=createUser cannot be empty
plugin.create_user.length_range=createUser length must be between {min} and {max}
plugin.scenario.not_blank=scenario cannot be empty
plugin.scenario.length_range=scenario length must be between {min} and {max}
plugin.exist=plugin name already exists
plugin.type.exist=plugin type already exists
plugin.script.exist=duplicate script id
plugin.script.format=malformed script
plugin.parse.error=plugin parsing failed, please re-upload
# serviceIntegration
service_integration.id.not_blank=id cannot be empty
service_integration.id.length_range=id length must be between {min} and {max}
service_integration.plugin_id.not_blank=pluginId cannot be empty
service_integration.plugin_id.length_range=pluginId length must be between {min} and {max}
service_integration.organization_id.not_blank=organizationId cannot be empty
service_integration.organization_id.length_range=organizationId length must be between {min} and {max}
service_integration_exist_error=Service integration configuration already exists
service_integration.configuration.not_blank=Service integration configuration cannot be empty
# customField
permission.system_custom_field.name=Custom Field
custom_field.exist=Custom Field already exists
template.exist=Template already exists
status_item.not.exist=The status entry does not exist
status_item.exist=The status item already exists
custom_field_option.field_id.not_blank=fieldId cannot be empty
custom_field_option.field_id.length_range=fieldId length must be between {min} and {max}
custom_field_option.value.not_blank=value cannot be empty
custom_field_option.value.length_range=value length must be between {min} and {max}
custom_field_option.text.not_blank=text cannot be empty
custom_field_option.text.length_range=text length must be between {min} and {max}
custom_field_option.internal.not_blank=option_value_internal_cannot_be_null
custom_field_option.pos.not_blank=option_value_pos_cannot_be_empty
# permission
permission.system_plugin.name=Plugin
permission.system_organization_project.name=Organization Project
permission.system_user.name=User
permission.system_user_role.name=User role
permission.system_test_resource_pool.name=Resource pool
permission.system_parameter_setting.name=Parameter setting
permission.system_parameter_setting_base.read=Base parameter setting read
permission.system_parameter_setting_base.update=Base parameter setting update
permission.system_parameter_setting_display.read=Display parameter setting read
permission.system_parameter_setting_display.update=Display parameter setting update
permission.system_parameter_setting_auth.read=Auth parameter setting read
permission.system_parameter_setting_auth.add=Auth parameter setting create
permission.system_parameter_setting_auth.update=Auth parameter setting update
permission.system_parameter_setting_auth.delete=Auth parameter setting delete
permission.system_parameter_setting_memory_clean.read=Memory clean parameter setting read
permission.system_parameter_setting_memory_clean.update=Memory clean parameter setting update
permission.system_parameter_setting_qrcode.read=Scan the QR code to log in-query
permission.system_parameter_setting_qrcode.update=Scan the QR code to log in-Edit
permission.system_parameter_setting_ai_model.read=AI Model setting read
permission.system_parameter_setting_ai_model.update=AI Model setting update
permission.organization_user_role.name=User group
permission.organization_member.name=User
permission.service_integration.name=Service Integration
permission.system_auth=Authorization
permission.system_organization_project_member.add=Add member
permission.system_organization_project_member.update=Update member
permission.system_organization_project_member.delete=Delete member
permission.system_operation_log.name=Operation log
permission.organization_operation_log.name=Operation log

permission.system_template_custom_field.name=模板和字段的关联关系
template_custom_field.exist=模板和字段的关联关系 already exists
template_custom_field.id.not_blank=id cannot be empty
template_custom_field.id.length_range=id length must be between {min} and {max}
template_custom_field.field_id.not_blank=fieldId cannot be empty
template_custom_field.field_id.length_range=fieldId length must be between {min} and {max}
template_custom_field.template_id.not_blank=templateId cannot be empty
template_custom_field.template_id.length_range=templateId length must be between {min} and {max}
template_custom_field.default_value.length_range=default value length must be less than{max}
permission.organization_custom_field.name=Custom Field
permission.organization_template.name=Template
permission.system_organization_template.enable=Enable project templates
permission.personal_settings=Personal settings
permission.my_settings=My settings
permission.my_settings_personal_info=Personal information
permission.api_key=APIKEY
permission.organization_project.recover=Recover
permission.organization_member.add=Add
permission.organization_member.invite=Invite Users
permission.organization_member.update=Update
permission.organization_member.delete=Remove
# 状态流
permission.status_item.name=Status item
status_item.id.not_blank=id cannot be empty
status_item.id.length_range=id length must be between {min} and {max}
status_item.name.not_blank=name cannot be empty
status_item.name.length_range=name length must be between {min} and {max}
status_item.scene.not_blank=scene cannot be empty
status_item.scene.length_range=scene length must be between {min} and {max}
status_item.scope_type.not_blank=scopeType cannot be empty
status_item.scope_type.length_range=scopeType length must be between {min} and {max}
status_item.scope_id.not_blank=scopeId cannot be empty
status_item.scope_id.length_range=scopeId length must be between {min} and {max}
status_definition.status_id.not_blank=statusId cannot be empty
status_definition.status_id.length_range=statusId length must be between {min} and {max}
status_definition.definition_id.not_blank=definitionId cannot be empty
status_definition.definition_id.length_range=definitionId length must be between {min} and {max}
status_flow.id.not_blank=id cannot be empty
status_flow.id.length_range=id length must be between {min} and {max}
status_flow.from_id.not_blank=fromId cannot be empty
status_flow.from_id.length_range=fromId length must be between {min} and {max}
status_flow.to_id.not_blank=toId cannot be empty
status_flow.to_id.length_range=toId length must be between {min} and {max}
# message
user.remove=has been removed
alert_others=Alert others

current_user_local_config_not_exist=Current user local config not exist
current_user_local_config_exist=Current user local config already exist
# user_local_config
user_local_config.id.not_blank=id cannot be empty
user_local_config.id.length_range=id length must be between {min} and {max}
user_local_config.user_url.not_blank=userUrl cannot be empty
user_local_config.user_url.length_range=userUrl length must be between {min} and {max}
user_local_config.type.not_blank=type cannot be empty
user_local_config.type.length_range=type length must be between {min} and {max}
current_user_local_config_not_validate=Current user local config not validate

# operation_history
operation_history.id.not_blank=Operating history id cannot be empty
operation_history.project_id.not_blank=Operating history project id cannot be empty
operation_history.project_id.length_range=Operating history project id length must be between {min} and {max}
operation_history.type.not_blank=Operating history type cannot be empty
operation_history.type.length_range=Operating history type length must be between {min} and {max}
operation_history.source_id.not_blank=Operating history source id cannot be empty
operation_history.version_id.not_blank=Operating history version id cannot be empty
operation_history.version_id.length_range=Operating history version id length must be between {min} and {max}
permission.organization_task_center.name=Task center
permission.organization_task_center.stop=Stop task
permission.case_task_center.read=System real time task read
permission.case_task_center.exec=System real time task exec/stop
permission.case_task_center.delete=System real time task delete
permission.schedule_task_center.read=Schedule task read
permission.schedule_task_center.update=Schedule task update
permission.schedule_task_center.delete=Schedule task delete
user_open_source_max=There are too many users({num}),please apply for the enterprise version to use
user_dept_max=There are too many users({num}),please apply for enterprise version expansion

# file_upload
file_upload.size_limit=Upload file size exceeds system limit

#模型
model_source.name.not_blank=Model name cannot be empty
model_source.type.not_blank=Model type cannot be empty
model_source.provider.not_blank=Model provider cannot be empty
model_source.avatar.not_blank=Model avatar cannot be empty
model_source.permission_type.not_blank=Model permissionType cannot be empty
model_source.status.not_blank=Model status cannot be empty
model_source.owner.not_blank=Model owner cannot be empty
model_source.owner_type.not_blank=Model ownerType cannot be empty
model_source.base_name.not_blank=Model baseName cannot be empty
model_source.app_key.not_blank=Model appKey cannot be empty
model_source.api_url.not_blank=Model apiUrl cannot be empty