# permission
permission.case_management.name=Case management
permission.functional_case.name=Functional case
permission.functional_case.minder=Mind map editing
permission.functional_case.comment=Comment
permission.case_review.name=Case review
permission.case_review.review=Review
permission.case_review.relevance=Associate/Disassociate
permission.case_review.startTime=The start time of the cycle review cannot be earlier than the current time
permission.case_review.endTime=The review cycle end time cannot be earlier than the current time

#moduleï¼FunctionalCase
functional_case.module.default.name=Unplanned case
functional_case.module.default.name.add_error=New modules are not supported under unplanned use case modules.
functional_case.module.default.name.cut_error=Unplanned use case modules cannot be moved
all.module.default.name.cut_error=All use cases cannot be moved
review.module.default.name=Unplanned review
functional_case.id.not_blank=ID cannot be empty
functional_case.num.not_blank=Business ID cannot be empty
functional_case.custom_num.length_range=The length of the custom business ID must be between 1 and 64
functional_case.custom_num.not_blank=Custom business ID cannot be empty
functional_case.module_id.length_range=Module ID length must be between 1 and 50
functional_case.module_id.not_blank=Module ID cannot be empty
functional_case.project_id.length_range=The length of the project ID must be between 1 and 50
functional_case.project_id.not_blank=Project ID cannot be empty
functional_case.template_id.not_blank=Template ID cannot be empty
functional_case.name.length_range=The name length must be between 1 and 255
functional_case.name.not_blank=Name cannot be empty
functional_case.pos.not_blank=Pos cannot be empty
functional_case.review_status.length_range=The length of the review status must be between 1 and 64
functional_case.review_status.not_blank=Review status cannot be empty
functional_case.case_edit_type.length_range=The length of the editing type must be between 1 and 64
functional_case.case_edit_type.not_blank=Edit type cannot be empty
functional_case.version_id.length_range=Version ID length must be between 1 and 50
functional_case.version_id.not_blank=Version ID cannot be empty
functional_case.ref_id.length_range=Pointing to the initial version ID must be between 1 and 50
functional_case.ref_id.not_blank=Pointing to the initial version ID cannot be empty
functional_case.last_execute_result.length_range=The length of the most recent execution result must be between 1 and 64
functional_case.last_execute_result.not_blank=The recent execution result cannot be empty
functional_case.deleted.length_range=Whether the length of the recycle bin must be between 1 and 1
functional_case.deleted.not_blank=Whether it is in the recycle bin cannot be empty
functional_case.public_case.length_range=Whether it is a public use case, the length must be between 1 and 1
functional_case.public_case.not_blank=whether it is a public use case cannot be empty
functional_case.latest.length_range=Whether it is the latest version or not. The length must be between 1 and 1
functional_case.latest.not_blank=whether it is the latest version cannot be empty
functional_case.create_user.length_range=Creator length must be between 1 and 100
functional_case.create_user.not_blank=Creator cannot be empty
functional_case.cover.not_blank=Whether to cover the original use case cannot be empty
functional_case.file_source.not_blank=Use case file source cannot be empty
#moduleï¼FunctionalCaseBlob
functional_case_blob.functional_case_id.not_blank=Function case ID cannot be empty
#moduleï¼FunctionalCaseComment
functional_case_comment.id.not_blank=ID cannot be empty
functional_case_comment.case_id.length_range=The length of the function case ID must be between 1 and 50
functional_case_comment.case_id.not_blank=Function case ID cannot be empty
functional_case_comment.create_user.length_range=The length of the reviewer must be between 1 and 50
functional_case_comment.create_user.not_blank=Reviewer cannot be empty
functional_case_comment.type.length_range=Comment type length must be between 1 and 64
functional_case_comment.type.not_blank=Comment type cannot be empty
functional_case_comment.content.not_blank=Comment content cannot be empty
functional_case_comment.event.not_blank=Comment type cannot be empty
#moduleï¼FunctionalCaseModule
functional_case_module.id.not_blank=ID cannot be empty
functional_case_module.project_id.length_range=The length of the project ID must be between 1 and 50
functional_case_module.project_id.not_blank=Project ID cannot be empty
functional_case_module.name.length_range=The name length must be between 1 and 100
functional_case_module.name.not_blank=Name cannot be empty
functional_case_module.pos.length_range=The order length under the same node must be between 1 and 10
functional_case_module.pos.not_blank=The order under the same node cannot be empty
functional_case_module.create_user.length_range=Creator length must be between 1 and 50
functional_case_module.create_user.not_blank=Creator cannot be empty
#moduleï¼FunctionalCaseAttachment
functional_case_attachment.case_id.not_blank=Case ID cannot be empty
functional_case_attachment.case_id.length_range=The length of the case ID must be between 1 and 50
functional_case_attachment.file_id.not_blank=File ID cannot be empty
functional_case_attachment.file_id.length_range=The length of the file ID must be between 1 and 50
functional_case_attachment.id.not_blank=ID cannot be empty
functional_case_attachment.id.length_range=The length of the ID must be between 1 and 50
functional_case_attachment.file_name.not_blank=File name cannot be empty
functional_case_attachment.file_name.length_range=The length of the file name must be between 1 and 255
functional_case_attachment.size.not_blank=File size cannot be empty
functional_case_attachment.association.not_blank=Association cannot be empty
#moduleï¼FunctionalCaseFollow
functional_case_follow.case_id.not_blank=Function case ID cannot be empty
functional_case_follow.follow_id.not_blank=Follow ID cannot be empty
#moduleï¼FunctionalCaseRelationshipEdge
functional_case_relationship_edge.id.not_blank=ID cannot be empty
functional_case_relationship_edge.source_id.length_range=The length of the source ID must be between 1 and 50
functional_case_relationship_edge.source_id.not_blank=Source ID cannot be empty
functional_case_relationship_edge.target_id.length_range=The length of the target ID must be between 1 and 50
functional_case_relationship_edge.target_id.not_blank=Target ID cannot be empty
functional_case_relationship_edge.graph_id.length_range=The length of the graph ID must be between 1 and 50
functional_case_relationship_edge.graph_id.not_blank=Graph ID cannot be empty
functional_case_relationship_edge.create_user.length_range=Creator length must be between 1 and 50
functional_case_relationship_edge.create_user.not_blank=Creator cannot be empty
#moduleï¼FunctionalCaseTest
functional_case_test.id.not_blank=ID cannot be empty
functional_case_test.functional_case_id.length_range=The length of the function case ID must be between 1 and 50
functional_case_test.functional_case_id.not_blank=Function case ID cannot be empty
functional_case_test.source_id.length_range=The length of the test ID must be between 1 and 50
functional_case_test.source_id.not_blank=Test ID cannot be empty
functional_case_test.source_type.length_range=The length of the test type must be between 1 and 64
functional_case_test.source_type.not_blank=Test type cannot be empty
functional_test_case.disassociate_type.not_blank=The associated use case type cannot be empty
#FunctionalCaseCustomField
functional_case_custom_field.case_id.not_blank=Case ID cannot be empty
functional_case_custom_field.field_id.not_blank=Field ID cannot be empty
#FunctionalCaseDemand
functional_case_demand.demand_platform.not_blank=Requirement platform cannot be empty
functional_case_demand.demand_name.not_blank=Requirement title cannot be empty
functional_case_demand.with_parent.not_blank=Whether it is related to the parent requirement cannot be empty
functional_case_demand.parent.not_blank=Parent requirement ID cannot be empty
functional_case_demand.case_id.not_blank=Function case ID cannot be empty
functional_case_demand.id.not_blank=Requirement ID cannot be empty
functional_case_demand.demand_name.length_range=Requirements title length must be between 1 and 255
functional_case_demand.parent.length_range=Parent requirement ID must be between 1 and 255
functional_case_demand.case_id.length_range=Functional use case ID must be between 1 and 50
functional_case_demand.id.length_range=Requirement ID must be between 1 and 50
#FunctionalCaseFollower
functional_case_follower.case_id.not_blank=Function case ID cannot be empty
functional_case_follower.user_id.not_blank=Followers cannot be empty
#FunctionalMinderExtraNode
functional_minder_extra_node.id.not_blank=Brain map nodes cannot be empty
functional_minder_extra_node.parent_id.not_blank=The parent node of the brain map cannot be empty
functional_minder_extra_node.group_id.not_blank=Project ID cannot be empty
functional_minder_extra_node.node_data.not_blank=The additional information stored in the brain map node cannot be empty
#moduleï¼MinderExtraNode
minder_extra_node.id.not_blank=ID cannot be empty
minder_extra_node.parent_id.length_range=The length of the parent node ID must be between 1 and 50
minder_extra_node.parent_id.not_blank=Parent node ID cannot be empty
minder_extra_node.group_id.length_range=The length of the group ID must be between 1 and 50
minder_extra_node.group_id.not_blank=Group ID cannot be empty
minder_extra_node.type.length_range=The length of the node type must be between 1 and 30
minder_extra_node.type.not_blank=Node type cannot be empty
minder_extra_node.case=Case
minder_extra_node.module=Module
minder_extra_node.prerequisite=Prerequisite
minder_extra_node.steps=Steps
minder_extra_node.steps_expected_result=ExpectedResult
minder_extra_node.steps_actual_result=ActualResult
minder_extra_node.text_description=TextDescription
minder_extra_node.text_expected_result=ExpectedResult
minder_extra_node.description=Description
minder_extra_node.text_node_empty=Text node name cannot be empty
minder_extra_node.case_node_empty=Case name cannot be empty
mind_import_case_name_empty=Some use case names are empty and verification failed;

#moduleï¼CaseReview
case_review.id.not_blank=ID cannot be empty
case_review.name.length_range=The name length must be between 1 and 200
case_review.name.not_blank=Name cannot be empty
case_review.module_id.not_blank=The module it belongs to cannot be empty
case_review.reviewers.not_empty=The default reviewer cannot be empty
case_review.copy_id.not_blank=The copied id of the use case review cannot be empty
case_review.case_review_id.not_blank=Use case review id cannot be empty
case_review.user_ids.not_empty=Reviewer cannot be empty
case_review_case.project_id.not_blank = The project id to which the functional use case belongs cannot be empty
case_review.move_mode.not_blank=Node movement type cannot be empty
case_review.status.length_range=The length of the review status must be between 1 and 64
case_review.status.not_blank=Review status cannot be empty
case_review.project_id.length_range=The length of the project ID must be between 1 and 50
case_review.project_id.not_blank=Project ID cannot be empty
case_review.create_user.length_range=Creator length must be between 1 and 50
case_review.create_user.not_blank=Creator cannot be empty
case_review.review_pass_rule.length_range=The length of the review rule must be between 1 and 64
case_review.review_pass_rule.not_blank=Review rule cannot be empty
#moduleï¼CaseReviewModule
case_review_module.id.not_blank=ID cannot be empty
case_review_module.project_id.not_blank=Project ID cannot be empty
case_review_module.name.not_blank=Name cannot be empty
case_review_module.parent_id.not_blank=Parent node ID cannot be empty
case_review_module.pos.not_blank=The order under the same node cannot be empty
#moduleï¼CaseReviewUser
case_review_user.review_id.not_blank=Review ID cannot be empty
case_review_user.user_id.not_blank=Reviewer ID cannot be empty
#moduleï¼CaseReviewHistory
case_review_history.id.not_blank=ID cannot be empty
case_review_history.id.length_range=ID length must be between 1 and 50
case_review_history.review_id.not_blank=Review ID cannot be empty
case_review_history.review_id.length_range=Review ID length must be between 1 and 50
case_review_history.case_id.not_blank=Function case ID cannot be empty
case_review_history.case_id.length_range=The function case ID length must be between 1 and 50
case_review_history.status.not_blank=The review result cannot be empty
case_review_history.status.length_range=The review result length must be between 1 and 64
case_review_history.deleted.not_blank=Whether the association is canceled or the review is deleted cannot be empty
case_review_history.abandoned.not_blank=Whether the review record is abandoned cannot be empty.
#moduleï¼CaseReviewFunctionalCase
case_review_functional_case.id.not_blank=ID cannot be empty
case_review_functional_case.review_id.length_range=The length of the review ID must be between 1 and 50
case_review_functional_case.review_id.not_blank=Review ID cannot be empty
case_review_functional_case.case_id.length_range=The length of the use case ID must be between 1 and 50
case_review_functional_case.case_id.not_blank=The use case ID cannot be empty
case_review_functional_case.status.length_range=The length of the review status must be between 1 and 64
case_review_functional_case.status.not_blank=Review status cannot be empty
case_review_functional_case.create_user.length_range=Creator length must be between 1 and 50
case_review_functional_case.create_user.not_blank=Creator cannot be empty
case_review_functional_case.deleted.length_range=The length of whether the associated use case is placed in the recycle bin must be between 1 and 1
case_review_functional_case.deleted.not_blank=Whether the associated use case is placed in the recycle bin cannot be empty
#moduleï¼CaseReviewFunctionalCaseUser
case_review_functional_case_user.case_id.length_range=The ID length of the functional use case and review intermediate table must be between 1 and 50
case_review_functional_case_user.case_id.not_blank=The ID of the functional use case and review intermediate table cannot be empty
case_review_functional_case_user.review_id.length_range=The length of the review ID must be between 1 and 50
case_review_functional_case_user.review_id.not_blank=Review ID cannot be empty
case_review_functional_case_user.user_id.length_range=Reviewer ID length must be between 1 and 50
case_review_functional_case_user.user_id.not_blank=Reviewer ID cannot be empty
#CaseReviewFunctionalCaseArchive
case_review_functional_case_archive.review_id.not_blank=Review ID cannot be empty
case_review_functional_case_archive.review_id.length_range=The length of the review ID must be between 1 and 50
case_review_functional_case_archive.case_id.not_blank=Function case ID cannot be empty
case_review_functional_case_archive.case_id.length_range=The function case ID length must be between 1 and 50
#moduleï¼CaseReviewFollower
case_review_follower.review_id.not_blank=Review ID cannot be empty
case_review_follower.follow_id.not_blank=follower cannot be empty
#moduleï¼CustomFieldTestCase
custom_field_test_case.resource_id.not_blank=Resource ID cannot be empty
custom_field_test_case.field_id.not_blank=Field ID cannot be empty
default_template_not_found=Default template not found
#comment
case_comment.case_is_null=Function use case does not exist
case_comment.parent_id_is_null=The comment id of the current reply is empty
case_comment.parent_case_is_null=The comment currently being replied to does not exist
case_comment.reply_user_is_null=The user who replied is empty
case_comment.id_is_null=The current comment id is empty
case_comment.user_self=You can only delete your own comments
un_follow_functional_case=unfollow functional case
follow_functional_case=followed functional case
tags_length_large_than=The number of tags cannot exceed {0}
#module
case_module.not.exist=Case module does not exist
file.transfer.failed=File transfer FAILED
#case
case.demand.not.exist=Demand does not exist
case.demand.name.not.exist=Demand name cannot be empty
case.demand.id.not.exist=Demand id cannot be empty
case_review.prepared=Prepared
case_review.underway=Underway
case_review.completed=Completed
case_review.archived=Archived
case_review.single=Single person review
case_review.multiple=Multiple reviewers
case_review.not.exist=Case review does not exist
case_review_content.not.exist = Review comments cannot be empty
case_review_history.system=System trigger
case_review.viewFlag.not_blank=View flag cannot be empty
functional_case_relationship_edge.type.not_blank=Relationship type cannot be empty
cycle_relationship=There is a circular dependency after association, please check the dependency relationship
case_review_user=You do not have review permission
#minder
case.minder.all.case=All Case

case.minder.status.success=success
case.minder.status.error=error
case.minder.status.blocked=blocked

case.review.status.un_reviewed=Not reviewed
case.review.status.under_reviewed=In the review
case.review.status.pass=Have passed
case.review.status.un_pass=Not pass
case.review.status.re_reviewed=retrial
case.execute.status.pending=Pending
case.execute.status.success=Success
case.execute.status.error=Error
case.execute.status.blocked=Block
functional_case_comment_template=[Case commentï¼%s(%s)]\n%s\n
functional_case_execute_comment_template=[Execute commentï¼%s %s(%s)]\n%s\n
functional_case_review_comment_template=[Review commentï¼%s %s(%s)]\n%s\n

#import
case.find_file_error=The file cannot be found
functional_case_xmind_template=Functional case xmind template
download_template_failed=Download template failed
functional_case=Functional case
xmind_prerequisite=Prerequisite
xmind_description=Remark
xmind_tags=Tag
xmind_textDescription=Text description
xmind_expectedResult=Expected result
xmind_step=Step
xmind_stepDescription=Step description
# case export columns
case.export.system.columns.name=Name
case.export.system.columns.id=ID
case.export.system.columns.prerequisite=Prerequisite
case.export.system.columns.module=Module
case.export.system.columns.text_description=Text description
case.export.system.columns.expected_result=Expected result
case.export.system.other.columns.last_execute_result=Last execution result
case.export.system.other.columns.review_status=Review status
case.export.system.other.columns.create_user=Create user
case.export.system.other.columns.create_time=Create time
case.export.system.other.columns.update_user=Update user
case.export.system.other.columns.update_time=Update time
case.export.columns.case_edit_type=Case edit type
case.export.system.other.columns.case_comment=Case comment
case.export.system.other.columns.execute_comment=Execute comment
case.export.system.other.columns.review_comment=Review comment

export_case_task_stop=Stop export
export_case_task_existed=Export task already exists

demand.sync.job=Demand Sync Job