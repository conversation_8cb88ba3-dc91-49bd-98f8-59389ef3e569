<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>io.metersphere</groupId>
        <artifactId>framework</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>metersphere-provider</artifactId>
    <version>${revision}</version>
    <name>metersphere-provider</name>
    <description>provider</description>
    <properties>
    </properties>
    <dependencies>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-domain</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-sdk</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations-jakarta</artifactId>
            <version>${swagger.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>


</project>