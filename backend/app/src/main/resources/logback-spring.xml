<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">
    <property resource="commons.properties"/>
    <property file="/opt/metersphere/conf/metersphere.properties"/>
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d %5p %40.40c:%4L - %m%n</pattern>
        </encoder>
    </appender>

    <appender name="debugAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <File>${logging.file.path}/debug.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${logging.file.path}/history/debug.%d{yyyyMMdd}-%i.log</FileNamePattern>
            <maxHistory>${logger.max.history:-30}</maxHistory>
            <maxFileSize>50MB</maxFileSize>
        </rollingPolicy>
        <encoder>
            <charset>UTF-8</charset>
            <Pattern>%d [%thread] %-5level %logger{36} %line - %msg%n</Pattern>
        </encoder>
    </appender>

    <appender name="infoAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <File>${logging.file.path}/info.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${logging.file.path}/history/info.%d{yyyyMMdd}-%i.log</FileNamePattern>
            <maxHistory>${logger.max.history:-30}</maxHistory>
            <maxFileSize>50MB</maxFileSize>
        </rollingPolicy>
        <encoder>
            <charset>UTF-8</charset>
            <Pattern>%d [%thread] %-5level %logger{36} %line - %msg%n</Pattern>
        </encoder>
    </appender>

    <appender name="errorAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>

        <File>${logging.file.path}/error.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${logging.file.path}/history/error.%d{yyyyMMdd}-%i.log</FileNamePattern>
            <maxHistory>${logger.max.history:-30}</maxHistory>
            <maxFileSize>50MB</maxFileSize>
        </rollingPolicy>
        <encoder>
            <charset>UTF-8</charset>
            <Pattern>%d [%thread] %-5level %logger{36} %line - %msg%n</Pattern>
        </encoder>
    </appender>

    <appender name="warnAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <File>${logging.file.path}/warn.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${logging.file.path}/history/warn.%d{yyyyMMdd}-%i.log</FileNamePattern>
            <maxHistory>${logger.max.history:-30}</maxHistory>
            <maxFileSize>50MB</maxFileSize>
        </rollingPolicy>
        <encoder>
            <charset>UTF-8</charset>
            <Pattern>%d [%thread] %-5level %logger{36} %line - %msg%n</Pattern>
        </encoder>
    </appender>

    <appender name="consoleAsyncAppender" class="ch.qos.logback.classic.AsyncAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <queueSize>10000</queueSize>
        <appender-ref ref="console"/>
    </appender>

    <appender name="debugAsyncAppender" class="ch.qos.logback.classic.AsyncAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <queueSize>10000</queueSize>
        <appender-ref ref="debugAppender"/>
    </appender>

    <appender name="infoAsyncAppender" class="ch.qos.logback.classic.AsyncAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <queueSize>10000</queueSize>
        <appender-ref ref="infoAppender"/>
    </appender>

    <appender name="errorAsyncAppender" class="ch.qos.logback.classic.AsyncAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <queueSize>10000</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="errorAppender"/>
    </appender>

    <appender name="warnAsyncAppender" class="ch.qos.logback.classic.AsyncAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
        <queueSize>10000</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="warnAppender"/>
    </appender>

    <root level="INFO">
        <appender-ref ref="infoAsyncAppender"/>
        <appender-ref ref="console"/>
    </root>

    <logger name="io.metersphere" additivity="false" level="${logger.level:INFO}">
        <appender-ref ref="infoAsyncAppender"/>
        <appender-ref ref="warnAsyncAppender"/>
        <appender-ref ref="errorAsyncAppender"/>
    </logger>

    <!--  包名不支持通配符 * ，这里需要每个模块固定好mapper  -->
    <logger name="io.metersphere.sdk.mapper" level="${logger.sql.level}">
        <appender-ref ref="console"/>
    </logger>
    <logger name="io.metersphere.system.mapper" level="${logger.sql.level}">
        <appender-ref ref="console"/>
    </logger>
    <logger name="io.metersphere.project.mapper" level="${logger.sql.level}">
        <appender-ref ref="console"/>
    </logger>
    <logger name="io.metersphere.api.mapper" level="${logger.sql.level}">
        <appender-ref ref="console"/>
    </logger>
    <logger name="io.metersphere.load.mapper" level="${logger.sql.level}">
        <appender-ref ref="console"/>
    </logger>
    <logger name="io.metersphere.ui.mapper" level="${logger.sql.level}">
        <appender-ref ref="console"/>
    </logger>
    <logger name="io.metersphere.plan.mapper" level="${logger.sql.level}">
        <appender-ref ref="console"/>
    </logger>
    <logger name="io.metersphere.workstation.mapper" level="${logger.sql.level}">
        <appender-ref ref="console"/>
    </logger>
    <logger name="io.metersphere.bug.mapper" level="${logger.sql.level}">
        <appender-ref ref="console"/>
    </logger>
    <logger name="io.metersphere.functional.mapper" level="${logger.sql.level}">
        <appender-ref ref="console"/>
    </logger>

    <logger name="io.metersphere.Application" additivity="false" level="${logger.level:INFO}">
        <appender-ref ref="infoAsyncAppender"/>
    </logger>

</configuration>
