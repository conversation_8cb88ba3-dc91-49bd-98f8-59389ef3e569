#---------------------------------------------------------
#         SAVESERVICE PROPERTIES - JMETER INTERNAL USE ONLY
#---------------------------------------------------------
##   Licensed to the Apache Software Foundation (ASF) under one or more
##   contributor license agreements.  See the NOTICE file distributed with
##   this work for additional information regarding copyright ownership.
##   The ASF licenses this file to You under the Apache License, Version 2.0
##   (the "License"); you may not use this file except in compliance with
##   the License.  You may obtain a copy of the License at
##
##       http://www.apache.org/licenses/LICENSE-2.0
##
##   Unless required by applicable law or agreed to in writing, software
##   distributed under the License is distributed on an "AS IS" BASIS,
##   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
##   See the License for the specific language governing permissions and
##   limitations under the License.
# This file is used to define how XStream (de-)serializes classnames
# in JMX test plan files.
#      FOR JMETER INTERNAL USE ONLY
#---------------------------------------------------------
# N.B. To ensure backward compatibility, please do NOT change or delete any entries
# New entries can be added as necessary.
#
# Note that keys starting with an underscore are special,
# and are not used as aliases.
#
# Please keep the entries in alphabetical order within the sections
# to reduce the likelihood of duplicates
#
# version number of this file is now computed by a sha1 sum, so no need for
# an explicit _file_version property anymore.
#
# For this sha1 sum we ignore every newline character. It can be computed
# by the following command:
#
# cat bin/saveservice.properties | perl -ne 'chomp; print' | sha1sum
#
# Be aware, that every change in this file will change the sha1 sum!
#
# Conversion version (for JMX output files)
# Must be updated if the file has been changed since the previous release
# Format is:
# Save service version=JMeter version at which change occurred
# 1.7 = 2.1.1
# 1.8 = 2.1.2
# (Some version updates were missed here...)
# 2.0 = 2.3.1
# 2.1 = 2.3.2
# (Some version updates were missed here...)
# 2.2 = 2.6
# 2.3 = 2.7
# 2.4 = 2.9
# 2.5 = 2.10
# 2.6 = 2.11
# 2.7 = 2.12
# 2.8 = 2.13
# 2.9 = 2.14
# 3.1 = 3.1
# 3.2 = 3.2
# 4.0 = 4.0
# 5.0 = 5.0
_version=5.0
#
#
# Character set encoding used to read and write JMeter XML files and CSV results
#
_file_encoding=UTF-8
#
#---------------------------------------------------------
#
# The following properties are used to create aliases
# [Must all start with capital letter]
#
AccessLogSampler=org.apache.jmeter.protocol.http.sampler.AccessLogSampler
AjpSampler=org.apache.jmeter.protocol.http.sampler.AjpSampler
AjpSamplerGui=org.apache.jmeter.protocol.http.control.gui.AjpSamplerGui
AnchorModifier=org.apache.jmeter.protocol.http.modifier.AnchorModifier
AnchorModifierGui=org.apache.jmeter.protocol.http.modifier.gui.AnchorModifierGui
Argument=org.apache.jmeter.config.Argument
Arguments=org.apache.jmeter.config.Arguments
ArgumentsPanel=org.apache.jmeter.config.gui.ArgumentsPanel
AssertionGui=org.apache.jmeter.assertions.gui.AssertionGui
AssertionVisualizer=org.apache.jmeter.visualizers.AssertionVisualizer
AuthManager=org.apache.jmeter.protocol.http.control.AuthManager
Authorization=org.apache.jmeter.protocol.http.control.Authorization
AuthPanel=org.apache.jmeter.protocol.http.gui.AuthPanel
BackendListener=org.apache.jmeter.visualizers.backend.BackendListener
BackendListenerGui=org.apache.jmeter.visualizers.backend.BackendListenerGui
BeanShellAssertion=org.apache.jmeter.assertions.BeanShellAssertion
BeanShellAssertionGui=org.apache.jmeter.assertions.gui.BeanShellAssertionGui
BeanShellListener=org.apache.jmeter.visualizers.BeanShellListener
BeanShellPostProcessor=org.apache.jmeter.extractor.BeanShellPostProcessor
BeanShellPreProcessor=org.apache.jmeter.modifiers.BeanShellPreProcessor
BeanShellSampler=org.apache.jmeter.protocol.java.sampler.BeanShellSampler
BeanShellSamplerGui=org.apache.jmeter.protocol.java.control.gui.BeanShellSamplerGui
BeanShellTimer=org.apache.jmeter.timers.BeanShellTimer
BoundaryExtractor=org.apache.jmeter.extractor.BoundaryExtractor
BoundaryExtractorGui=org.apache.jmeter.extractor.gui.BoundaryExtractorGui
BSFAssertion=org.apache.jmeter.assertions.BSFAssertion
BSFListener=org.apache.jmeter.visualizers.BSFListener
BSFPreProcessor=org.apache.jmeter.modifiers.BSFPreProcessor
BSFPostProcessor=org.apache.jmeter.extractor.BSFPostProcessor
BSFSampler=org.apache.jmeter.protocol.java.sampler.BSFSampler
BSFSamplerGui=org.apache.jmeter.protocol.java.control.gui.BSFSamplerGui
BSFTimer=org.apache.jmeter.timers.BSFTimer
CacheManager=org.apache.jmeter.protocol.http.control.CacheManager
CacheManagerGui=org.apache.jmeter.protocol.http.gui.CacheManagerGui
CompareAssertion=org.apache.jmeter.assertions.CompareAssertion
ComparisonVisualizer=org.apache.jmeter.visualizers.ComparisonVisualizer
ConfigTestElement=org.apache.jmeter.config.ConfigTestElement
ConstantThroughputTimer=org.apache.jmeter.timers.ConstantThroughputTimer
ConstantTimer=org.apache.jmeter.timers.ConstantTimer
ConstantTimerGui=org.apache.jmeter.timers.gui.ConstantTimerGui
Cookie=org.apache.jmeter.protocol.http.control.Cookie
CookieManager=org.apache.jmeter.protocol.http.control.CookieManager
CookiePanel=org.apache.jmeter.protocol.http.gui.CookiePanel
CounterConfig=org.apache.jmeter.modifiers.CounterConfig
CriticalSectionController=org.apache.jmeter.control.CriticalSectionController
CriticalSectionControllerGui=org.apache.jmeter.control.gui.CriticalSectionControllerGui
CounterConfigGui=org.apache.jmeter.modifiers.gui.CounterConfigGui
CSVDataSet=org.apache.jmeter.config.CSVDataSet
DebugPostProcessor=org.apache.jmeter.extractor.DebugPostProcessor
DebugSampler=org.apache.jmeter.sampler.DebugSampler
# removed in 3.1, class was deleted in r1763837
DistributionGraphVisualizer=org.apache.jmeter.visualizers.DistributionGraphVisualizer
DNSCacheManager=org.apache.jmeter.protocol.http.control.DNSCacheManager
DNSCachePanel=org.apache.jmeter.protocol.http.gui.DNSCachePanel
DubboSample=io.github.ningyu.jmeter.plugin.dubbo.sample.DubboSample
DubboSampleGui=io.github.ningyu.jmeter.plugin.dubbo.gui.DubboSampleGui
DubboDefaultConfigGui=io.github.ningyu.jmeter.plugin.dubbo.gui.DubboDefaultConfigGui
DurationAssertion=org.apache.jmeter.assertions.DurationAssertion
DurationAssertionGui=org.apache.jmeter.assertions.gui.DurationAssertionGui
PreciseThroughputTimer=org.apache.jmeter.timers.poissonarrivals.PreciseThroughputTimer
# Should really have been defined as floatProp to agree with other properties
# No point changing this now
FloatProperty=org.apache.jmeter.testelement.property.FloatProperty
ForeachController=org.apache.jmeter.control.ForeachController
ForeachControlPanel=org.apache.jmeter.control.gui.ForeachControlPanel
FtpConfigGui=org.apache.jmeter.protocol.ftp.config.gui.FtpConfigGui
FTPSampler=org.apache.jmeter.protocol.ftp.sampler.FTPSampler
FtpTestSamplerGui=org.apache.jmeter.protocol.ftp.control.gui.FtpTestSamplerGui
GaussianRandomTimer=org.apache.jmeter.timers.GaussianRandomTimer
GaussianRandomTimerGui=org.apache.jmeter.timers.gui.GaussianRandomTimerGui
GenericController=org.apache.jmeter.control.GenericController
GraphAccumVisualizer=org.apache.jmeter.visualizers.GraphAccumVisualizer
GraphVisualizer=org.apache.jmeter.visualizers.GraphVisualizer
Header=org.apache.jmeter.protocol.http.control.Header
HeaderManager=org.apache.jmeter.protocol.http.control.HeaderManager
HeaderPanel=org.apache.jmeter.protocol.http.gui.HeaderPanel
HTMLAssertion=org.apache.jmeter.assertions.HTMLAssertion
HTMLAssertionGui=org.apache.jmeter.assertions.gui.HTMLAssertionGui
HTTPArgument=org.apache.jmeter.protocol.http.util.HTTPArgument
HTTPArgumentsPanel=org.apache.jmeter.protocol.http.gui.HTTPArgumentsPanel
HTTPFileArg=org.apache.jmeter.protocol.http.util.HTTPFileArg
HTTPFileArgs=org.apache.jmeter.protocol.http.util.HTTPFileArgs
HttpDefaultsGui=org.apache.jmeter.protocol.http.config.gui.HttpDefaultsGui
HtmlExtractor=org.apache.jmeter.extractor.HtmlExtractor
HtmlExtractorGui=org.apache.jmeter.extractor.gui.HtmlExtractorGui
# removed in r1039684, probably not released. Not present in r322831 or since.
#HttpGenericSampler=org.apache.jmeter.protocol.http.sampler.HttpGenericSampler
# removed in r1039684, probably not released. Not present in r322831 or since.
#HttpGenericSamplerGui=org.apache.jmeter.protocol.http.control.gui.HttpGenericSamplerGui
HttpMirrorControl=org.apache.jmeter.protocol.http.control.HttpMirrorControl
HttpMirrorControlGui=org.apache.jmeter.protocol.http.control.gui.HttpMirrorControlGui
# r397955 - removed test class. Keep as commented entry for info only.
#HTTPNullSampler=org.apache.jmeter.protocol.http.sampler.HTTPNullSampler
# Merge previous 2 HTTP samplers into one
HTTPSampler_=org.apache.jmeter.protocol.http.sampler.HTTPSampler
HTTPSampler2_=org.apache.jmeter.protocol.http.sampler.HTTPSampler2
HTTPSamplerProxy,HTTPSampler,HTTPSampler2=org.apache.jmeter.protocol.http.sampler.HTTPSamplerProxy
# Merge GUIs
HttpTestSampleGui,HttpTestSampleGui2=org.apache.jmeter.protocol.http.control.gui.HttpTestSampleGui
#HttpTestSampleGui2=org.apache.jmeter.protocol.http.control.gui.HttpTestSampleGui2
IfController=org.apache.jmeter.control.IfController
IfControllerPanel=org.apache.jmeter.control.gui.IfControllerPanel
IncludeController=org.apache.jmeter.control.IncludeController
IncludeControllerGui=org.apache.jmeter.control.gui.IncludeControllerGui
InterleaveControl=org.apache.jmeter.control.InterleaveControl
InterleaveControlGui=org.apache.jmeter.control.gui.InterleaveControlGui
JavaConfig=org.apache.jmeter.protocol.java.config.JavaConfig
JavaConfigGui=org.apache.jmeter.protocol.java.config.gui.JavaConfigGui
JavaSampler=org.apache.jmeter.protocol.java.sampler.JavaSampler
JavaTest=org.apache.jmeter.protocol.java.test.JavaTest
JavaTestSamplerGui=org.apache.jmeter.protocol.java.control.gui.JavaTestSamplerGui
JDBCDataSource=org.apache.jmeter.protocol.jdbc.config.DataSourceElement
JDBCPostProcessor=org.apache.jmeter.protocol.jdbc.processor.JDBCPostProcessor
JDBCPreProcessor=org.apache.jmeter.protocol.jdbc.processor.JDBCPreProcessor
JDBCSampler=org.apache.jmeter.protocol.jdbc.sampler.JDBCSampler
JMESPathAssertion=org.apache.jmeter.assertions.jmespath.JMESPathAssertion
JMESPathAssertionGui=org.apache.jmeter.assertions.jmespath.gui.JMESPathAssertionGui
JMESPathExtractor=org.apache.jmeter.extractor.json.jmespath.JMESPathExtractor
JMESPathExtractorGui=org.apache.jmeter.extractor.json.jmespath.gui.JMESPathExtractorGui
# Renamed to JMSSamplerGui; keep original entry for backwards compatibility
JMSConfigGui=org.apache.jmeter.protocol.jms.control.gui.JMSConfigGui
JMSProperties=org.apache.jmeter.protocol.jms.sampler.JMSProperties
JMSProperty=org.apache.jmeter.protocol.jms.sampler.JMSProperty
JMSPublisherGui=org.apache.jmeter.protocol.jms.control.gui.JMSPublisherGui
JMSSampler=org.apache.jmeter.protocol.jms.sampler.JMSSampler
JMSSamplerGui=org.apache.jmeter.protocol.jms.control.gui.JMSSamplerGui
JMSSubscriberGui=org.apache.jmeter.protocol.jms.control.gui.JMSSubscriberGui
JSONPathAssertion=org.apache.jmeter.assertions.JSONPathAssertion
JSONPathAssertionGui=org.apache.jmeter.assertions.gui.JSONPathAssertionGui
JSONPostProcessor=org.apache.jmeter.extractor.json.jsonpath.JSONPostProcessor
JSONPostProcessorGui=org.apache.jmeter.extractor.json.jsonpath.gui.JSONPostProcessorGui
# Removed in r545311 as Jndi no longer present; keep for compat.
JndiDefaultsGui=org.apache.jmeter.protocol.jms.control.gui.JndiDefaultsGui
JSR223Assertion=org.apache.jmeter.assertions.JSR223Assertion
JSR223Listener=org.apache.jmeter.visualizers.JSR223Listener
JSR223PostProcessor=org.apache.jmeter.extractor.JSR223PostProcessor
JSR223PreProcessor=org.apache.jmeter.modifiers.JSR223PreProcessor
JSR223Sampler=org.apache.jmeter.protocol.java.sampler.JSR223Sampler
JSR223Timer=org.apache.jmeter.timers.JSR223Timer
JUnitSampler=org.apache.jmeter.protocol.java.sampler.JUnitSampler
JUnitTestSamplerGui=org.apache.jmeter.protocol.java.control.gui.JUnitTestSamplerGui
KeystoreConfig=org.apache.jmeter.config.KeystoreConfig
LDAPArgument=org.apache.jmeter.protocol.ldap.config.gui.LDAPArgument
LDAPArguments=org.apache.jmeter.protocol.ldap.config.gui.LDAPArguments
LDAPArgumentsPanel=org.apache.jmeter.protocol.ldap.config.gui.LDAPArgumentsPanel
LdapConfigGui=org.apache.jmeter.protocol.ldap.config.gui.LdapConfigGui
LdapExtConfigGui=org.apache.jmeter.protocol.ldap.config.gui.LdapExtConfigGui
LDAPExtSampler=org.apache.jmeter.protocol.ldap.sampler.LDAPExtSampler
LdapExtTestSamplerGui=org.apache.jmeter.protocol.ldap.control.gui.LdapExtTestSamplerGui
LDAPSampler=org.apache.jmeter.protocol.ldap.sampler.LDAPSampler
LdapTestSamplerGui=org.apache.jmeter.protocol.ldap.control.gui.LdapTestSamplerGui
LogicControllerGui=org.apache.jmeter.control.gui.LogicControllerGui
LoginConfig=org.apache.jmeter.config.LoginConfig
LoginConfigGui=org.apache.jmeter.config.gui.LoginConfigGui
LoopController=org.apache.jmeter.control.LoopController
LoopControlPanel=org.apache.jmeter.control.gui.LoopControlPanel
MailerModel=org.apache.jmeter.reporters.MailerModel
MailerResultCollector=org.apache.jmeter.reporters.MailerResultCollector
MailerVisualizer=org.apache.jmeter.visualizers.MailerVisualizer
MailReaderSampler=org.apache.jmeter.protocol.mail.sampler.MailReaderSampler
MailReaderSamplerGui=org.apache.jmeter.protocol.mail.sampler.gui.MailReaderSamplerGui
MD5HexAssertion=org.apache.jmeter.assertions.MD5HexAssertion
MD5HexAssertionGUI=org.apache.jmeter.assertions.gui.MD5HexAssertionGUI
ModuleController=org.apache.jmeter.control.ModuleController
ModuleControllerGui=org.apache.jmeter.control.gui.ModuleControllerGui
MongoScriptSampler=org.apache.jmeter.protocol.mongodb.sampler.MongoScriptSampler
MongoSourceElement=org.apache.jmeter.protocol.mongodb.config.MongoSourceElement
# removed in 3.2, class was deleted in r
MonitorHealthVisualizer=org.apache.jmeter.visualizers.MonitorHealthVisualizer
NamePanel=org.apache.jmeter.gui.NamePanel
BoltSampler=org.apache.jmeter.protocol.bolt.sampler.BoltSampler
BoltConnectionElement=org.apache.jmeter.protocol.bolt.config.BoltConnectionElement
ObsoleteGui=org.apache.jmeter.config.gui.ObsoleteGui
OnceOnlyController=org.apache.jmeter.control.OnceOnlyController
OnceOnlyControllerGui=org.apache.jmeter.control.gui.OnceOnlyControllerGui
# removed in 3.0, class was deleted in r1722962
ParamMask=org.apache.jmeter.protocol.http.modifier.ParamMask
# removed in 3.0, class was deleted in r1722757
ParamModifier=org.apache.jmeter.protocol.http.modifier.ParamModifier
# removed in 3.0, class was deleted in r1722757
ParamModifierGui=org.apache.jmeter.protocol.http.modifier.gui.ParamModifierGui
PoissonRandomTimer=org.apache.jmeter.timers.PoissonRandomTimer
PoissonRandomTimerGui=org.apache.jmeter.timers.gui.PoissonRandomTimerGui
PropertyControlGui=org.apache.jmeter.visualizers.PropertyControlGui
ProxyControl=org.apache.jmeter.protocol.http.proxy.ProxyControl
ProxyControlGui=org.apache.jmeter.protocol.http.proxy.gui.ProxyControlGui
PublisherSampler=org.apache.jmeter.protocol.jms.sampler.PublisherSampler
RandomControlGui=org.apache.jmeter.control.gui.RandomControlGui
RandomController=org.apache.jmeter.control.RandomController
RandomOrderController=org.apache.jmeter.control.RandomOrderController
RandomOrderControllerGui=org.apache.jmeter.control.gui.RandomOrderControllerGui
RandomVariableConfig=org.apache.jmeter.config.RandomVariableConfig
RecordController=org.apache.jmeter.protocol.http.control.gui.RecordController
RecordingController=org.apache.jmeter.protocol.http.control.RecordingController
# removed in r1039684, class was deleted in r580452
ReflectionThreadGroup=org.apache.jmeter.threads.ReflectionThreadGroup
RegexExtractor=org.apache.jmeter.extractor.RegexExtractor
RegexExtractorGui=org.apache.jmeter.extractor.gui.RegexExtractorGui
RegExUserParameters=org.apache.jmeter.protocol.http.modifier.RegExUserParameters
RegExUserParametersGui=org.apache.jmeter.protocol.http.modifier.gui.RegExUserParametersGui
RemoteListenerWrapper=org.apache.jmeter.samplers.RemoteListenerWrapper
RemoteSampleListenerWrapper=org.apache.jmeter.samplers.RemoteSampleListenerWrapper
RemoteTestListenerWrapper=org.apache.jmeter.samplers.RemoteTestListenerWrapper
RemoteThreadsListenerWrapper=org.apache.jmeter.threads.RemoteThreadsListenerWrapper
ResponseAssertion=org.apache.jmeter.assertions.ResponseAssertion
RespTimeGraphVisualizer=org.apache.jmeter.visualizers.RespTimeGraphVisualizer
ResultAction=org.apache.jmeter.reporters.ResultAction
ResultActionGui=org.apache.jmeter.reporters.gui.ResultActionGui
ResultCollector=org.apache.jmeter.reporters.ResultCollector
ResultSaver=org.apache.jmeter.reporters.ResultSaver
ResultSaverGui=org.apache.jmeter.reporters.gui.ResultSaverGui
RunTime=org.apache.jmeter.control.RunTime
RunTimeGui=org.apache.jmeter.control.gui.RunTimeGui
SampleSaveConfiguration=org.apache.jmeter.samplers.SampleSaveConfiguration
SampleTimeout=org.apache.jmeter.modifiers.SampleTimeout
SampleTimeoutGui=org.apache.jmeter.modifiers.gui.SampleTimeoutGui
SimpleConfigGui=org.apache.jmeter.config.gui.SimpleConfigGui
SimpleDataWriter=org.apache.jmeter.visualizers.SimpleDataWriter
SizeAssertion=org.apache.jmeter.assertions.SizeAssertion
SizeAssertionGui=org.apache.jmeter.assertions.gui.SizeAssertionGui
SMIMEAssertion=org.apache.jmeter.assertions.SMIMEAssertionTestElement
SMIMEAssertionGui=org.apache.jmeter.assertions.gui.SMIMEAssertionGui
SmtpSampler=org.apache.jmeter.protocol.smtp.sampler.SmtpSampler
SmtpSamplerGui=org.apache.jmeter.protocol.smtp.sampler.gui.SmtpSamplerGui
# removed in 3.2, class was deleted in r
SoapSampler=org.apache.jmeter.protocol.http.sampler.SoapSampler
# removed in 3.2, class was deleted in r
SoapSamplerGui=org.apache.jmeter.protocol.http.control.gui.SoapSamplerGui
# removed in 3.1, class was deleted in r1763837
SplineVisualizer=org.apache.jmeter.visualizers.SplineVisualizer
# Originally deleted in r397955 as class is obsolete; needed for compat.
SqlConfigGui=org.apache.jmeter.protocol.jdbc.config.gui.SqlConfigGui
StaticHost=org.apache.jmeter.protocol.http.control.StaticHost
StatGraphVisualizer=org.apache.jmeter.visualizers.StatGraphVisualizer
StatVisualizer=org.apache.jmeter.visualizers.StatVisualizer
SubscriberSampler=org.apache.jmeter.protocol.jms.sampler.SubscriberSampler
SubstitutionElement=org.apache.jmeter.assertions.SubstitutionElement
Summariser=org.apache.jmeter.reporters.Summariser
SummariserGui=org.apache.jmeter.reporters.gui.SummariserGui
SummaryReport=org.apache.jmeter.visualizers.SummaryReport
SwitchController=org.apache.jmeter.control.SwitchController
SwitchControllerGui=org.apache.jmeter.control.gui.SwitchControllerGui
SyncTimer=org.apache.jmeter.timers.SyncTimer
SystemSampler=org.apache.jmeter.protocol.system.SystemSampler
SystemSamplerGui=org.apache.jmeter.protocol.system.gui.SystemSamplerGui
TableVisualizer=org.apache.jmeter.visualizers.TableVisualizer
TCPConfigGui=org.apache.jmeter.protocol.tcp.config.gui.TCPConfigGui
TCPSampler=org.apache.jmeter.protocol.tcp.sampler.TCPSampler
TCPSamplerGui=org.apache.jmeter.protocol.tcp.control.gui.TCPSamplerGui
TestAction=org.apache.jmeter.sampler.TestAction
TestActionGui=org.apache.jmeter.sampler.gui.TestActionGui
TestBeanGUI=org.apache.jmeter.testbeans.gui.TestBeanGUI
TestFragmentController=org.apache.jmeter.control.TestFragmentController
TestFragmentControllerGui=org.apache.jmeter.control.gui.TestFragmentControllerGui
TestPlan=org.apache.jmeter.testelement.TestPlan
TestPlanGui=org.apache.jmeter.control.gui.TestPlanGui
ThreadGroup=org.apache.jmeter.threads.ThreadGroup
ThreadGroupGui=org.apache.jmeter.threads.gui.ThreadGroupGui
PostThreadGroup=org.apache.jmeter.threads.PostThreadGroup
PostThreadGroupGui=org.apache.jmeter.threads.gui.PostThreadGroupGui
SetupThreadGroup=org.apache.jmeter.threads.SetupThreadGroup
SetupThreadGroupGui=org.apache.jmeter.threads.gui.SetupThreadGroupGui
ThroughputController=org.apache.jmeter.control.ThroughputController
ThroughputControllerGui=org.apache.jmeter.control.gui.ThroughputControllerGui
TransactionController=org.apache.jmeter.control.TransactionController
TransactionControllerGui=org.apache.jmeter.control.gui.TransactionControllerGui
TransactionSampler=org.apache.jmeter.control.TransactionSampler
UniformRandomTimer=org.apache.jmeter.timers.UniformRandomTimer
UniformRandomTimerGui=org.apache.jmeter.timers.gui.UniformRandomTimerGui
URLRewritingModifier=org.apache.jmeter.protocol.http.modifier.URLRewritingModifier
URLRewritingModifierGui=org.apache.jmeter.protocol.http.modifier.gui.URLRewritingModifierGui
UserParameterModifier=org.apache.jmeter.protocol.http.modifier.UserParameterModifier
UserParameterModifierGui=org.apache.jmeter.protocol.http.modifier.gui.UserParameterModifierGui
UserParameters=org.apache.jmeter.modifiers.UserParameters
UserParametersGui=org.apache.jmeter.modifiers.gui.UserParametersGui
ViewResultsFullVisualizer=org.apache.jmeter.visualizers.ViewResultsFullVisualizer
# removed in 3.0, class was deleted in r1722757
WebServiceSampler=org.apache.jmeter.protocol.http.sampler.WebServiceSampler
# removed in 3.0, class was deleted in r1722757
WebServiceSamplerGui=org.apache.jmeter.protocol.http.control.gui.WebServiceSamplerGui
WhileController=org.apache.jmeter.control.WhileController
WhileControllerGui=org.apache.jmeter.control.gui.WhileControllerGui
WorkBench=org.apache.jmeter.testelement.WorkBench
WorkBenchGui=org.apache.jmeter.control.gui.WorkBenchGui
XMLAssertion=org.apache.jmeter.assertions.XMLAssertion
XMLAssertionGui=org.apache.jmeter.assertions.gui.XMLAssertionGui
XMLSchemaAssertion=org.apache.jmeter.assertions.XMLSchemaAssertion
XMLSchemaAssertionGUI=org.apache.jmeter.assertions.gui.XMLSchemaAssertionGUI
XPathAssertion=org.apache.jmeter.assertions.XPathAssertion
XPathAssertionGui=org.apache.jmeter.assertions.gui.XPathAssertionGui
XPath2Assertion=org.apache.jmeter.assertions.XPath2Assertion
XPath2AssertionGui=org.apache.jmeter.assertions.gui.XPath2AssertionGui
XPathExtractor=org.apache.jmeter.extractor.XPathExtractor
XPathExtractorGui=org.apache.jmeter.extractor.gui.XPathExtractorGui
XPath2Extractor=org.apache.jmeter.extractor.XPath2Extractor
XPath2ExtractorGui=org.apache.jmeter.extractor.gui.XPath2ExtractorGui
# Properties - all start with lower case letter and end with Prop
#
boolProp=org.apache.jmeter.testelement.property.BooleanProperty
collectionProp=org.apache.jmeter.testelement.property.CollectionProperty
doubleProp=org.apache.jmeter.testelement.property.DoubleProperty
elementProp=org.apache.jmeter.testelement.property.TestElementProperty
# see above - already defined as FloatProperty
#floatProp=org.apache.jmeter.testelement.property.FloatProperty
intProp=org.apache.jmeter.testelement.property.IntegerProperty
longProp=org.apache.jmeter.testelement.property.LongProperty
mapProp=org.apache.jmeter.testelement.property.MapProperty
objProp=org.apache.jmeter.testelement.property.ObjectProperty
stringProp=org.apache.jmeter.testelement.property.StringProperty
#
# Other - must start with a lower case letter (and not end with Prop)
# (otherwise they could clash with the initial set of aliases)
#
hashTree=org.apache.jorphan.collections.ListedHashTree
jmeterTestPlan=org.apache.jmeter.save.ScriptWrapper
sample=org.apache.jmeter.samplers.SampleResult
httpSample=org.apache.jmeter.protocol.http.sampler.HTTPSampleResult
statSample=org.apache.jmeter.samplers.StatisticalSampleResult
testResults=org.apache.jmeter.save.TestResultWrapper
assertionResult=org.apache.jmeter.assertions.AssertionResult
# removed in 3.2, class was deleted in r
monitorStats=org.apache.jmeter.visualizers.MonitorStats
sampleEvent=org.apache.jmeter.samplers.SampleEvent
#
# Converters to register.  Must start line with '_'
# If the converter is a collection of subitems, set equal to "collection"
# If the converter needs to know the class mappings but is not a collection of
#      subitems, set it equal to "mapping"
_org.apache.jmeter.protocol.http.sampler.HTTPSamplerBaseConverter=collection
_org.apache.jmeter.protocol.http.util.HTTPResultConverter=collection
_org.apache.jmeter.save.converters.BooleanPropertyConverter=
_org.apache.jmeter.save.converters.IntegerPropertyConverter=
_org.apache.jmeter.save.converters.LongPropertyConverter=
_org.apache.jmeter.save.converters.MultiPropertyConverter=collection
_org.apache.jmeter.save.converters.SampleEventConverter=
_org.apache.jmeter.save.converters.SampleResultConverter=collection
_org.apache.jmeter.save.converters.SampleSaveConfigurationConverter=collection
_org.apache.jmeter.save.converters.StringPropertyConverter=
_org.apache.jmeter.save.converters.HashTreeConverter=collection
_org.apache.jmeter.save.converters.TestElementConverter=collection
_org.apache.jmeter.save.converters.TestElementPropertyConverter=collection
_org.apache.jmeter.save.converters.TestResultWrapperConverter=collection
_org.apache.jmeter.save.ScriptWrapperConverter=mapping
#
#	Remember to update the _version entry
#