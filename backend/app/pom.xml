<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.metersphere</groupId>
        <artifactId>backend</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>app</artifactId>
    <version>${revision}</version>
    <name>app</name>
    <properties>
    </properties>
    <dependencies>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-sdk</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-api-test</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-project-management</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-system-setting</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-test-plan</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-case-management</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-bug-management</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-dashboard</artifactId>
            <version>${revision}</version>
        </dependency>

    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.json</include>
                    <include>**/*.tpl</include>
                    <include>**/*.js</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                    <loaderImplementation>CLASSIC</loaderImplementation>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-clean-plugin</artifactId>
                <configuration>
                    <filesets>
                        <fileset>
                            <directory>src/main/resources/static</directory>
                            <includes>
                                <include>**</include>
                            </includes>
                            <followSymlinks>false</followSymlinks>
                        </fileset>
                    </filesets>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>main-class-placement</id>
                        <phase>generate-resources</phase>
                        <configuration>
                            <skip>${skipAntRunForJenkins}</skip>
                            <target>
                                <copy todir="src/main/resources/static">
                                    <fileset dir="../../frontend/src/assets"/>
                                    <fileset dir="../../frontend/public"/>
                                    <fileset dir="../../frontend/dist"/>
                                </copy>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
