<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.metersphere</groupId>
        <artifactId>services</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>metersphere-api-test</artifactId>
    <version>${revision}</version>
    <name>api-test</name>
    <properties>
    </properties>
    <dependencies>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-sdk</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-ai-engine</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-provider</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-system-setting</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-system-setting</artifactId>
            <version>${revision}</version>
            <classifier>tests</classifier>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-project-management</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.jmeter</groupId>
            <artifactId>ApacheJMeter_components</artifactId>
            <version>${jmeter.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>mail</artifactId>
                    <groupId>javax.mail</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.jmeter</groupId>
            <artifactId>ApacheJMeter_http</artifactId>
            <version>${jmeter.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.jmeter</groupId>
            <artifactId>ApacheJMeter_java</artifactId>
            <version>${jmeter.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.jmeter</groupId>
            <artifactId>ApacheJMeter_jdbc</artifactId>
            <version>${jmeter.version}</version>
        </dependency>
        <!-- 自定义jmeter 断言插件 -->
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-jmeter-assertions</artifactId>
            <version>${metersphere-jmeter-assertions.version}</version>
        </dependency>
        <!-- swagger 解析 -->
        <dependency>
            <groupId>io.swagger.parser.v3</groupId>
            <artifactId>swagger-parser</artifactId>
            <version>${swagger-parser.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>swagger-core</artifactId>
                    <groupId>io.swagger.core.v3</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>swagger-models</artifactId>
                    <groupId>io.swagger.core.v3</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.7</version>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                    <configurationFile>src/main/resources/apiGeneratorConfig.xml</configurationFile>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>com.mysql</groupId>
                        <artifactId>mysql-connector-j</artifactId>
                        <version>${mysql-connector-java.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>com.itfsw</groupId>
                        <artifactId>mybatis-generator-plugin</artifactId>
                        <version>1.3.10</version>
                    </dependency>
                    <dependency>
                        <groupId>io.metersphere</groupId>
                        <artifactId>mybatis-tools</artifactId>
                        <version>3.0.0</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

</project>
