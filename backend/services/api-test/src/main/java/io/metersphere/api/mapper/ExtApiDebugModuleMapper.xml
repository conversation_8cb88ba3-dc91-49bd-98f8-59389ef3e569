<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.api.mapper.ExtApiDebugModuleMapper">
    <select id="selectBaseByProtocolAndUser" resultType="io.metersphere.system.dto.sdk.BaseTreeNode">
        SELECT id,
               NAME,
               parent_id AS parentId,
               'MODULE' AS type
        FROM api_debug_module
        WHERE create_user = #{userId}
        ORDER BY pos
    </select>
    <select id="selectIdAndParentIdByProtocolAndUserId" resultType="io.metersphere.system.dto.sdk.BaseTreeNode">
        SELECT id,
               parent_id AS parentId
        FROM api_debug_module
        WHERE create_user = #{userId}
    </select>
    <select id="selectChildrenIdsByParentIds" resultType="java.lang.String">
        SELECT id
        FROM api_debug_module
        WHERE parent_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <delete id="deleteByIds">
        DELETE
        FROM api_debug_module
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="getMaxPosByParentId" resultType="java.lang.Long">
        SELECT max(pos)
        FROM api_debug_module
        WHERE parent_id = #{0}
    </select>
    <select id="selectChildrenIdsSortByPos" resultType="java.lang.String">
        SELECT id
        FROM api_debug_module
        WHERE parent_id = #{0}

        ORDER BY pos ASC
    </select>
    <select id="selectApiDebugByProtocolAndUser" resultType="io.metersphere.api.dto.debug.ApiTreeNode">
        SELECT id,
               NAME,
               module_id AS parentId,
               'API' AS type,
               method,
                protocol
        FROM api_debug
        WHERE create_user = #{userId}

        ORDER BY pos DESC
    </select>
    <select id="countModuleIdByKeywordAndProtocol" resultType="io.metersphere.project.dto.ModuleCountDTO">
        SELECT f.module_id AS moduleId, count(f.id) AS dataCount
        FROM api_debug f
        <include refid="debug_page_request"/>
        GROUP BY f.module_id
    </select>
    <select id="selectBaseModuleById" resultType="io.metersphere.system.dto.sdk.BaseModule">
        SELECT id, name, pos, project_Id, parent_id
        FROM api_debug_module
        WHERE id = #{0}
    </select>

    <select id="selectModuleByParentIdAndPosOperator"
            parameterType="io.metersphere.project.dto.NodeSortQueryParam"
            resultType="io.metersphere.system.dto.sdk.BaseModule">
        SELECT id, name, pos, project_Id, parent_id
        FROM api_debug_module
        WHERE parent_id = #{parentId}
        <if test="operator == 'moreThan'">
            AND pos &gt; #{pos}
        </if>
        <if test="operator == 'lessThan'">
            AND pos &lt; #{pos}
        </if>
        ORDER BY pos
        <if test="operator == 'lessThan' or operator == 'latest'">
            DESC
        </if>
        LIMIT 1
    </select>
    <select id="selectBaseNodeByIds" resultType="io.metersphere.system.dto.sdk.BaseTreeNode">
        SELECT id,
        NAME,
        parent_id AS parentId,
        'MODULE' AS type
        FROM api_debug_module
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <sql id="debug_page_request">
        <where>
            f.create_user = #{userId}
            <if test="request.keyword != null and request.keyword != ''">
                AND f.name like CONCAT('%', #{request.keyword},'%')
            </if>
            <if test="request.moduleIds != null and request.moduleIds.size() != 0">
                AND f.module_id IN
                <foreach collection="request.moduleIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        </where>
    </sql>
</mapper>