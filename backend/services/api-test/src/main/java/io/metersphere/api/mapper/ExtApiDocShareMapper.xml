<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.metersphere.api.mapper.ExtApiDocShareMapper">

    <select id="list" resultType="io.metersphere.api.dto.definition.ApiDocShareDTO">
        select  id, name, is_private isPrivate, create_user createUser, create_time createTime,
                update_user updateUser, update_time updateTime, allow_export allowExport,
                api_range apiRange, range_match_symbol rangeMatchSymbol, range_match_val rangeMatchVal,
                invalid_time invalidTime, project_id projectId, password
        from api_doc_share
        <include refid="queryWhereCondition"/>
    </select>

    <sql id="queryWhereCondition">
        <where>
            <if test="request.projectId != null and request.projectId != ''">
                and project_id = #{request.projectId}
            </if>
            <if test="request.keyword != null and request.keyword != ''">
                and name like concat('%', #{request.keyword},'%')
            </if>
            <include refid="filter"/>
        </where>
    </sql>

    <sql id="filter">
        <if test="request.filter != null and request.filter.size() > 0">
            <foreach collection="request.filter.entrySet()" index="key" item="values">
                <if test="values != null and values.size() > 0">
                    <choose>
                        <!-- 创建人 -->
                        <when test="key == 'updateUser'">
                            and update_user in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                        </when>
                    </choose>
                </if>
            </foreach>
        </if>
    </sql>
</mapper>