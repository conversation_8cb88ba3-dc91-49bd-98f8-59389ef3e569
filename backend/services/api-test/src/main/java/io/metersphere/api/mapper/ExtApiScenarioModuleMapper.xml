<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.api.mapper.ExtApiScenarioModuleMapper">
    <select id="selectIdAndParentIdByRequest" resultType="io.metersphere.system.dto.sdk.BaseTreeNode">
        SELECT m.id,
        m.parent_id AS parentId
        FROM api_scenario_module m
        <include refid="module_request"/>
        ORDER BY pos
    </select>
    <select id="selectChildrenIdsByParentIds" resultType="java.lang.String">
        SELECT id
        FROM api_scenario_module
        WHERE parent_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <delete id="deleteByIds">
        DELETE
        FROM api_scenario_module
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <update id="deleteScenarioToGc">
        update api_scenario
        set delete_user = #{userId},delete_time = #{time}, deleted = 1 , module_id = 'root'
        where ref_id in
        <foreach collection="ids" item="v" separator="," open="(" close=")">
            #{v}
        </foreach>
    </update>
    <select id="getMaxPosByParentId" resultType="java.lang.Long">
        SELECT max(pos)
        FROM api_scenario_module
        WHERE parent_id = #{0}
    </select>
    <select id="selectChildrenIdsSortByPos" resultType="java.lang.String">
        SELECT id
        FROM api_scenario_module
        WHERE parent_id = #{0}

        ORDER BY pos ASC
    </select>
    <select id="selectBaseModuleById" resultType="io.metersphere.system.dto.sdk.BaseModule">
        SELECT id, name, pos, project_Id, parent_id
        FROM api_scenario_module
        WHERE id = #{0}
    </select>
    <select id="selectModuleByParentIdAndPosOperator"
            parameterType="io.metersphere.project.dto.NodeSortQueryParam"
            resultType="io.metersphere.system.dto.sdk.BaseModule">
        SELECT id, name, pos, project_Id, parent_id
        FROM api_scenario_module
        WHERE parent_id = #{parentId}
        <if test="operator == 'moreThan'">
            AND pos &gt; #{pos}
        </if>
        <if test="operator == 'lessThan'">
            AND pos &lt; #{pos}
        </if>
        ORDER BY pos
        <if test="operator == 'lessThan' or operator == 'latest'">
            DESC
        </if>
        LIMIT 1
    </select>
    <select id="selectBaseByRequest" resultType="io.metersphere.system.dto.sdk.BaseTreeNode">
        SELECT m.id,
        m.parent_id AS parentId,
        m.name,
        m.pos,
        m.project_id,
        'MODULE' AS type
        FROM api_scenario_module m
        <include refid="module_request"/>
        ORDER BY pos
    </select>
    <select id="selectNodeByIds" resultType="io.metersphere.system.dto.sdk.BaseTreeNode">
        SELECT m.id,
        m.parent_id AS parentId,
        m.name,
        m.pos,
        m.project_id,
        'MODULE' AS type
        FROM api_scenario_module m
        WHERE m.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY pos
    </select>


    <select id="selectBaseByIds" resultType="io.metersphere.system.dto.sdk.BaseTreeNode">
        SELECT id, name, parent_id AS parentId, 'module' AS type
        FROM api_scenario_module
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY pos
    </select>

    <sql id="queryVersionCondition">
        <if test="request.versionId != null and request.versionId != ''">
            and ${versionTable}.version_id = #{request.versionId}
        </if>
        <if test="request.refId != null and request.refId != ''">
            and ${versionTable}.ref_id = #{request.refId}
        </if>
        <if test="request.versionId == null and request.refId == null">
            AND ${versionTable}.latest = 1
        </if>
    </sql>
    <sql id="combine">
        <if test='${condition}.name != null and (${name} == null or ${name} == "")'>
            and api_scenario.name
            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                <property name="object" value="${condition}.name"/>
            </include>
        </if>

        <if test='${condition}.id != null'>
            and api_scenario.num
            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                <property name="object" value="${condition}.id"/>
            </include>
        </if>

        <if test="${condition}.updateTime != null">
            and api_scenario.update_time
            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                <property name="object" value="${condition}.updateTime"/>
            </include>
        </if>

        <if test="${condition}.createTime != null">
            and api_scenario.create_time
            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                <property name="object" value="${condition}.createTime"/>
            </include>
        </if>

        <if test="${condition}.status != null">
            and api_scenario.status
            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                <property name="object" value="${condition}.status"/>
            </include>
        </if>

        <if test='${condition}.tags != null and ${ObjectTags}.operator == "not like"'>
            and (api_scenario.tags is null or api_scenario.tags
            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                <property name="object" value="${condition}.tags"/>
            </include>
            )
        </if>

        <if test='${condition}.tags != null and ${ObjectTags}.operator == "like"'>
            and api_scenario.tags
            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                <property name="object" value="${condition}.tags"/>
            </include>
        </if>


    </sql>


    <sql id="module_request">
        <where>
            <if test="request.projectId != null and request.projectId != ''">
                AND m.project_id = #{request.projectId}
            </if>
            <if test="request.keyword != null and request.keyword != ''">
                AND m.name like CONCAT('%', #{request.keyword},'%')
            </if>
            <if test="request.moduleIds != null and request.moduleIds.size() != 0">
                AND m.id IN
                <foreach collection="request.moduleIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        </where>
    </sql>

</mapper>