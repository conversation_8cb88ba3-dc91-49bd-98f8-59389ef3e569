spring.application.name=metersphere
api.server.port=10001
server.port=8081
# gzip
server.compression.enabled=true
server.compression.mime-types=application/json,application/xml,text/html,text/xml,text/plain,application/javascript,text/css,text/javascript,image/jpeg
server.compression.min-response-size=2048
#
quartz.enabled=true
quartz.scheduler-name=msScheduler
quartz.thread-count=10
quartz.properties.org.quartz.jobStore.acquireTriggersWithinLock=true
#
logging.file.path=/opt/metersphere/logs/metersphere/api
# Hikari
spring.datasource.url=jdbc:mysql://${embedded.mysql.host}:${embedded.mysql.port}/test?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8
spring.datasource.username=${embedded.mysql.user}
spring.datasource.password=${embedded.mysql.password}
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.maximum-pool-size=100
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.pool-name=DatebookHikariCP
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1
# quartz datasource
spring.datasource.quartz.url=${spring.datasource.url}
spring.datasource.quartz.username=${spring.datasource.username}
spring.datasource.quartz.password=${spring.datasource.password}
spring.datasource.quartz.hikari.maximum-pool-size=50
spring.datasource.quartz.hikari.minimum-idle=10
spring.datasource.quartz.hikari.idle-timeout=300000
spring.datasource.quartz.hikari.auto-commit=true
spring.datasource.quartz.hikari.pool-name=DatebookHikariCP
spring.datasource.quartz.hikari.max-lifetime=1800000
spring.datasource.quartz.hikari.connection-timeout=30000
spring.datasource.quartz.hikari.connection-test-query=SELECT 1
# \u5355\u5143\u6D4B\u8BD5\u521D\u59CB\u5316\u6743\u9650 sql
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath*:dml/init_permission_test.sql
#
# spring.kafka
spring.kafka.bootstrap-servers=${embedded.kafka.brokerList}
spring.kafka.consumer.group-id=metersphere_group_id
spring.kafka.listener.missing-topics-fatal=false
spring.kafka.producer.properties.max.request.size=32428800
spring.kafka.producer.batch-size=16384
spring.kafka.consumer.properties.max.partition.fetch.bytes=52428800
spring.kafka.consumer.properties.decompression.type=gzip
spring.kafka.producer.properties.compression.type=gzip
spring.kafka.producer.properties.compression.threshold=100

# mybatis
mybatis.configuration.cache-enabled=true
mybatis.configuration.lazy-loading-enabled=false
mybatis.configuration.aggressive-lazy-loading=true
mybatis.configuration.multiple-result-sets-enabled=true
mybatis.configuration.use-column-label=true
mybatis.configuration.auto-mapping-behavior=full
mybatis.configuration.default-statement-timeout=25000
mybatis.configuration.map-underscore-to-camel-case=true
# virtual thread
spring.threads.virtual.enabled=true
# flyway enable
spring.flyway.enabled=true
spring.flyway.baseline-on-migrate=true
spring.flyway.locations=classpath:/migration
spring.flyway.table=metersphere_version
spring.flyway.baseline-version=0
spring.flyway.encoding=UTF-8
spring.flyway.validate-on-migrate=false
# jmeter
jmeter.home=/opt/jmeter
# file upload
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB
# i18n
spring.messages.basename=i18n/commons,i18n/api,i18n/bug,i18n/case,i18n/plan,i18n/project,i18n/system
spring.messages.default-locale=zh_CN

# actuator
management.endpoints.web.exposure.include=*
management.endpoints.enabled-by-default=false
# redis
spring.session.timeout=43200s
spring.data.redis.host=${embedded.redis.host}
spring.data.redis.password=${embedded.redis.password}
spring.data.redis.port=${embedded.redis.port}
spring.session.redis.repository-type=indexed
#
spring.freemarker.check-template-location=false
spring.groovy.template.check-template-location=false
#
minio.endpoint=http://${embedded.minio.host}:${embedded.minio.port}
minio.access-key=${embedded.minio.accessKey}
minio.secret-key=${embedded.minio.secretKey}

logging.level.org.springframework.jdbc.core=info
logging.level.io.metersphere.sdk.mapper=info
logging.level.io.metersphere.project.mapper=info

spring.ai.chat.memory.repository.jdbc.initialize-schema=never
