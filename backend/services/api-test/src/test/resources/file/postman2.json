{"info": {"_postman_id": "069b5a16-ea3b-4ef6-957c-5e1531515627", "name": "Swagger Petstore - OpenAPI 3.0", "description": "This is a sample Pet Store Server based on the OpenAPI 3.0 specification.  You can find out more about\nSwagger at [https://swagger.io](https://swagger.io). In the third iteration of the pet store, we've switched to the design first approach!\nYou can now help us improve the API whether it's by making changes to the definition itself or to the code.\nThat way, with time, we can improve the API in general, and expose some of the new features in OAS3.\n\n_If you're looking for the Swagger 2.0/OAS 2.0 version of Petstore, then click [here](https://editor.swagger.io/?url=https://petstore.swagger.io/v2/swagger.yaml). Alternatively, you can load via the `Edit > Load Petstore OAS 2.0` menu option!_\n\nSome useful links:\n- [The Pet Store repository](https://github.com/swagger-api/swagger-petstore)\n- [The source API definition for the Pet Store](https://github.com/swagger-api/swagger-petstore/blob/master/src/main/resources/openapi.yaml)\n\nContact Support:\n Email: <EMAIL>", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json", "_exporter_id": "8135699"}, "item": [{"name": "pet", "item": [{"name": "findByStatus", "item": [{"name": "Finds Pets by status", "request": {"auth": {"type": "basic", "basic": {"password": "312321", "username": "21321"}}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "description": "2232132", "disabled": true}], "url": {"raw": "{{baseUrl}}/pet/findByStatus/?aaa=112", "host": ["{{baseUrl}}"], "path": ["pet", "findByStatus", ""], "query": [{"key": "status", "value": "available", "description": "Status values that need to be considered for filter", "disabled": true}, {"key": "aaa", "value": "112"}]}, "description": "Multiple status values can be provided with comma separated strings"}, "response": [{"name": "successful operation", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: oauth2", "key": "Authorization", "value": "<token>"}], "url": {"raw": "{{baseUrl}}/pet/findByStatus?status=available", "host": ["{{baseUrl}}"], "path": ["pet", "findByStatus"], "query": [{"key": "status", "value": "available", "description": "Status values that need to be considered for filter"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"name\": \"<string>\",\n    \"photoUrls\": [\n      \"<string>\",\n      \"<string>\"\n    ],\n    \"id\": \"<long>\",\n    \"category\": {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\"\n    },\n    \"tags\": [\n      {\n        \"id\": \"<long>\",\n        \"name\": \"<string>\"\n      },\n      {\n        \"id\": \"<long>\",\n        \"name\": \"<string>\"\n      }\n    ],\n    \"status\": \"sold\"\n  },\n  {\n    \"name\": \"<string>\",\n    \"photoUrls\": [\n      \"<string>\",\n      \"<string>\"\n    ],\n    \"id\": \"<long>\",\n    \"category\": {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\"\n    },\n    \"tags\": [\n      {\n        \"id\": \"<long>\",\n        \"name\": \"<string>\"\n      },\n      {\n        \"id\": \"<long>\",\n        \"name\": \"<string>\"\n      }\n    ],\n    \"status\": \"sold\"\n  }\n]"}, {"name": "Invalid status value", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: oauth2", "key": "Authorization", "value": "<token>"}], "url": {"raw": "{{baseUrl}}/pet/findByStatus?status=available", "host": ["{{baseUrl}}"], "path": ["pet", "findByStatus"], "query": [{"key": "status", "value": "available", "description": "Status values that need to be considered for filter"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}]}, {"name": "findByTags", "item": [{"name": "Finds Pets by tags", "protocolProfileBehavior": {"strictSSL": false, "followOriginalHttpMethod": false, "followAuthorizationHeader": false, "removeRefererHeaderOnRedirect": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/pet/findByTags?tags=<string>&tags=<string>", "host": ["{{baseUrl}}"], "path": ["pet", "findByTags"], "query": [{"key": "tags", "value": "<string>", "description": "Tags to filter by"}, {"key": "tags", "value": "<string>", "description": "Tags to filter by"}]}, "description": "Multiple tags can be provided with comma separated strings. Use tag1, tag2, tag3 for testing."}, "response": [{"name": "successful operation", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: oauth2", "key": "Authorization", "value": "<token>"}], "url": {"raw": "{{baseUrl}}/pet/findByTags?tags=<string>", "host": ["{{baseUrl}}"], "path": ["pet", "findByTags"], "query": [{"key": "tags", "value": "<string>", "description": "Tags to filter by"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"name\": \"<string>\",\n    \"photoUrls\": [\n      \"<string>\",\n      \"<string>\"\n    ],\n    \"id\": \"<long>\",\n    \"category\": {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\"\n    },\n    \"tags\": [\n      {\n        \"id\": \"<long>\",\n        \"name\": \"<string>\"\n      },\n      {\n        \"id\": \"<long>\",\n        \"name\": \"<string>\"\n      }\n    ],\n    \"status\": \"sold\"\n  },\n  {\n    \"name\": \"<string>\",\n    \"photoUrls\": [\n      \"<string>\",\n      \"<string>\"\n    ],\n    \"id\": \"<long>\",\n    \"category\": {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\"\n    },\n    \"tags\": [\n      {\n        \"id\": \"<long>\",\n        \"name\": \"<string>\"\n      },\n      {\n        \"id\": \"<long>\",\n        \"name\": \"<string>\"\n      }\n    ],\n    \"status\": \"sold\"\n  }\n]"}, {"name": "Invalid tag value", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: oauth2", "key": "Authorization", "value": "<token>"}], "url": {"raw": "{{baseUrl}}/pet/findByTags?tags=<string>", "host": ["{{baseUrl}}"], "path": ["pet", "findByTags"], "query": [{"key": "tags", "value": "<string>", "description": "Tags to filter by"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}]}, {"name": "{petId}", "item": [{"name": "uploadImage", "item": [{"name": "uploads an image", "request": {"auth": {"type": "digest", "digest": {"algorithm": "MD5"}}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/octet-stream"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "file", "file": {}}, "url": {"raw": "{{baseUrl}}/pet/:petId/uploadImage?additionalMetadata=<string>", "host": ["{{baseUrl}}"], "path": ["pet", ":petId", "uploadImage"], "query": [{"key": "additionalMetadata", "value": "<string>", "description": "Additional Metadata"}], "variable": [{"key": "petId", "value": "<long>", "description": "(Required) ID of pet to update"}]}}, "response": [{"name": "successful operation", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/octet-stream"}, {"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: oauth2", "key": "Authorization", "value": "<token>"}], "body": {"mode": "file", "file": {}}, "url": {"raw": "{{baseUrl}}/pet/:petId/uploadImage?additionalMetadata=<string>", "host": ["{{baseUrl}}"], "path": ["pet", ":petId", "uploadImage"], "query": [{"key": "additionalMetadata", "value": "<string>", "description": "Additional Metadata"}], "variable": [{"key": "petId"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"code\": \"<integer>\",\n  \"type\": \"<string>\",\n  \"message\": \"<string>\"\n}"}]}]}, {"name": "Find pet by ID", "request": {"auth": {"type": "apikey", "apikey": {"key": "api_key", "value": "{{a<PERSON><PERSON><PERSON>}}", "in": "header"}}, "method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/pet/:petId", "host": ["{{baseUrl}}"], "path": ["pet", ":petId"], "variable": [{"key": "petId", "value": "<long>", "description": "(Required) ID of pet to return"}]}, "description": "Returns a single pet"}, "response": [{"name": "successful operation", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "api_key", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/pet/:petId", "host": ["{{baseUrl}}"], "path": ["pet", ":petId"], "variable": [{"key": "petId"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"name\": \"<string>\",\n  \"photoUrls\": [\n    \"<string>\",\n    \"<string>\"\n  ],\n  \"id\": \"<long>\",\n  \"category\": {\n    \"id\": \"<long>\",\n    \"name\": \"<string>\"\n  },\n  \"tags\": [\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\"\n    },\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\"\n    }\n  ],\n  \"status\": \"sold\"\n}"}, {"name": "Invalid ID supplied", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: apikey", "key": "api_key", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/pet/:petId", "host": ["{{baseUrl}}"], "path": ["pet", ":petId"], "variable": [{"key": "petId"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}, {"name": "Pet not found", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: apikey", "key": "api_key", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/pet/:petId", "host": ["{{baseUrl}}"], "path": ["pet", ":petId"], "variable": [{"key": "petId"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}, {"name": "Updates a pet in the store with form data", "request": {"auth": {"type": "oauth2", "oauth2": {"scope": "write:pets read:pets", "authUrl": "https://petstore3.swagger.io/oauth/authorize", "grant_type": "implicit"}}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/pet/:petId?name=<string>&status=<string>", "host": ["{{baseUrl}}"], "path": ["pet", ":petId"], "query": [{"key": "name", "value": "<string>", "description": "Name of pet that needs to be updated"}, {"key": "status", "value": "<string>", "description": "Status of pet that needs to be updated"}], "variable": [{"key": "petId", "value": "<long>", "description": "(Required) ID of pet that needs to be updated"}]}}, "response": [{"name": "Invalid input", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: oauth2", "key": "Authorization", "value": "<token>"}], "url": {"raw": "{{baseUrl}}/pet/:petId?name=<string>&status=<string>", "host": ["{{baseUrl}}"], "path": ["pet", ":petId"], "query": [{"key": "name", "value": "<string>", "description": "Name of pet that needs to be updated"}, {"key": "status", "value": "<string>", "description": "Status of pet that needs to be updated"}], "variable": [{"key": "petId"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}, {"name": "Deletes a pet", "request": {"auth": {"type": "oauth2", "oauth2": {"scope": "write:pets read:pets", "authUrl": "https://petstore3.swagger.io/oauth/authorize", "grant_type": "implicit"}}, "method": "DELETE", "header": [{"key": "api_key", "value": "<string>"}], "url": {"raw": "{{baseUrl}}/pet/:petId", "host": ["{{baseUrl}}"], "path": ["pet", ":petId"], "variable": [{"key": "petId", "value": "<long>", "description": "(Required) Pet id to delete"}]}, "description": "delete a pet"}, "response": [{"name": "Invalid pet value", "originalRequest": {"method": "DELETE", "header": [{"key": "api_key", "value": "<string>"}, {"description": "Added as a part of security scheme: oauth2", "key": "Authorization", "value": "<token>"}], "url": {"raw": "{{baseUrl}}/pet/:petId", "host": ["{{baseUrl}}"], "path": ["pet", ":petId"], "variable": [{"key": "petId"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}]}, {"name": "Update an existing pet", "request": {"auth": {"type": "oauth2", "oauth2": {"scope": "write:pets read:pets", "authUrl": "https://petstore3.swagger.io/oauth/authorize", "grant_type": "implicit"}}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "name", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "id", "value": "<long>"}, {"key": "id", "value": "<long>"}, {"key": "name", "value": "<string>"}, {"key": "tags", "value": "[object Object]"}, {"key": "tags", "value": "[object Object]"}, {"key": "status", "value": "sold", "description": "pet status in the store"}]}, "url": {"raw": "{{baseUrl}}/pet?qqq=qwwew", "host": ["{{baseUrl}}"], "path": ["pet"], "query": [{"key": "qqq", "value": "qwwew"}]}, "description": "Update an existing pet by Id"}, "response": [{"name": "Successful operation", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: oauth2", "key": "Authorization", "value": "<token>"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "name", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "id", "value": "<long>"}, {"key": "id", "value": "<long>"}, {"key": "name", "value": "<string>"}, {"key": "tags", "value": "[object Object]"}, {"key": "tags", "value": "[object Object]"}, {"key": "status", "value": "sold", "description": "pet status in the store"}]}, "url": "{{baseUrl}}/pet"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"name\": \"<string>\",\n  \"photoUrls\": [\n    \"<string>\",\n    \"<string>\"\n  ],\n  \"id\": \"<long>\",\n  \"category\": {\n    \"id\": \"<long>\",\n    \"name\": \"<string>\"\n  },\n  \"tags\": [\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\"\n    },\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\"\n    }\n  ],\n  \"status\": \"sold\"\n}"}, {"name": "Invalid ID supplied", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"description": "Added as a part of security scheme: oauth2", "key": "Authorization", "value": "<token>"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "name", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "id", "value": "<long>"}, {"key": "id", "value": "<long>"}, {"key": "name", "value": "<string>"}, {"key": "tags", "value": "[object Object]"}, {"key": "tags", "value": "[object Object]"}, {"key": "status", "value": "sold", "description": "pet status in the store"}]}, "url": "{{baseUrl}}/pet"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}, {"name": "Pet not found", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"description": "Added as a part of security scheme: oauth2", "key": "Authorization", "value": "<token>"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "name", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "id", "value": "<long>"}, {"key": "id", "value": "<long>"}, {"key": "name", "value": "<string>"}, {"key": "tags", "value": "[object Object]"}, {"key": "tags", "value": "[object Object]"}, {"key": "status", "value": "sold", "description": "pet status in the store"}]}, "url": "{{baseUrl}}/pet"}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}, {"name": "Validation exception", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"description": "Added as a part of security scheme: oauth2", "key": "Authorization", "value": "<token>"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "name", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "id", "value": "<long>"}, {"key": "id", "value": "<long>"}, {"key": "name", "value": "<string>"}, {"key": "tags", "value": "[object Object]"}, {"key": "tags", "value": "[object Object]"}, {"key": "status", "value": "sold", "description": "pet status in the store"}]}, "url": "{{baseUrl}}/pet"}, "status": "Unprocessable Entity (WebDAV) (RFC 4918)", "code": 422, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}, {"name": "Add a new pet to the store", "request": {"auth": {"type": "oauth2", "oauth2": {"scope": "write:pets read:pets", "authUrl": "https://petstore3.swagger.io/oauth/authorize", "grant_type": "implicit"}}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "name", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "id", "value": "<long>"}, {"key": "id", "value": "<long>"}, {"key": "name", "value": "<string>"}, {"key": "tags", "value": "[object Object]"}, {"key": "tags", "value": "[object Object]"}, {"key": "status", "value": "sold", "description": "pet status in the store"}]}, "url": "{{baseUrl}}/pet", "description": "Add a new pet to the store"}, "response": [{"name": "Successful operation", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: oauth2", "key": "Authorization", "value": "<token>"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "name", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "id", "value": "<long>"}, {"key": "id", "value": "<long>"}, {"key": "name", "value": "<string>"}, {"key": "tags", "value": "[object Object]"}, {"key": "tags", "value": "[object Object]"}, {"key": "status", "value": "sold", "description": "pet status in the store"}]}, "url": "{{baseUrl}}/pet"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"name\": \"<string>\",\n  \"photoUrls\": [\n    \"<string>\",\n    \"<string>\"\n  ],\n  \"id\": \"<long>\",\n  \"category\": {\n    \"id\": \"<long>\",\n    \"name\": \"<string>\"\n  },\n  \"tags\": [\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\"\n    },\n    {\n      \"id\": \"<long>\",\n      \"name\": \"<string>\"\n    }\n  ],\n  \"status\": \"sold\"\n}"}, {"name": "Invalid input", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"description": "Added as a part of security scheme: oauth2", "key": "Authorization", "value": "<token>"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "name", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "id", "value": "<long>"}, {"key": "id", "value": "<long>"}, {"key": "name", "value": "<string>"}, {"key": "tags", "value": "[object Object]"}, {"key": "tags", "value": "[object Object]"}, {"key": "status", "value": "sold", "description": "pet status in the store"}]}, "url": "{{baseUrl}}/pet"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}, {"name": "Validation exception", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"description": "Added as a part of security scheme: oauth2", "key": "Authorization", "value": "<token>"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "name", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "photoUrls", "value": "<string>", "description": "(Required) "}, {"key": "id", "value": "<long>"}, {"key": "id", "value": "<long>"}, {"key": "name", "value": "<string>"}, {"key": "tags", "value": "[object Object]"}, {"key": "tags", "value": "[object Object]"}, {"key": "status", "value": "sold", "description": "pet status in the store"}]}, "url": "{{baseUrl}}/pet"}, "status": "Unprocessable Entity (WebDAV) (RFC 4918)", "code": 422, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}]}, {"name": "store", "item": [{"name": "inventory", "item": [{"name": "Returns pet inventories by status", "request": {"auth": {"type": "apikey", "apikey": {"key": "api_key", "value": "{{a<PERSON><PERSON><PERSON>}}", "in": "header"}}, "method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": "{{baseUrl}}/store/inventory", "description": "Returns a map of status codes to quantities"}, "response": [{"name": "successful operation", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "api_key", "value": "<API Key>"}], "url": "{{baseUrl}}/store/inventory"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"do6\": \"<integer>\",\n  \"culpac37\": \"<integer>\",\n  \"ad_611\": \"<integer>\",\n  \"laboris_1\": \"<integer>\"\n}"}]}]}, {"name": "order", "item": [{"name": "{orderId}", "item": [{"name": "Find purchase order by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/store/order/:orderId", "host": ["{{baseUrl}}"], "path": ["store", "order", ":orderId"], "variable": [{"key": "orderId", "value": "<long>", "description": "(Required) ID of order that needs to be fetched"}]}, "description": "For valid response try integer IDs with value <= 5 or > 10. Other values will generate exceptions."}, "response": [{"name": "successful operation", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/store/order/:orderId", "host": ["{{baseUrl}}"], "path": ["store", "order", ":orderId"], "variable": [{"key": "orderId"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<long>\",\n  \"petId\": \"<long>\",\n  \"quantity\": \"<integer>\",\n  \"shipDate\": \"<dateTime>\",\n  \"status\": \"placed\",\n  \"complete\": \"<boolean>\"\n}"}, {"name": "Invalid ID supplied", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/store/order/:orderId", "host": ["{{baseUrl}}"], "path": ["store", "order", ":orderId"], "variable": [{"key": "orderId"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}, {"name": "Order not found", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/store/order/:orderId", "host": ["{{baseUrl}}"], "path": ["store", "order", ":orderId"], "variable": [{"key": "orderId"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}, {"name": "Delete purchase order by ID", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/store/order/:orderId", "host": ["{{baseUrl}}"], "path": ["store", "order", ":orderId"], "variable": [{"key": "orderId", "value": "<long>", "description": "(Required) ID of the order that needs to be deleted"}]}, "description": "For valid response try integer IDs with value < 1000. Anything above 1000 or nonintegers will generate API errors"}, "response": [{"name": "Invalid ID supplied", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/store/order/:orderId", "host": ["{{baseUrl}}"], "path": ["store", "order", ":orderId"], "variable": [{"key": "orderId"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}, {"name": "Order not found", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/store/order/:orderId", "host": ["{{baseUrl}}"], "path": ["store", "order", ":orderId"], "variable": [{"key": "orderId"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}]}, {"name": "Place an order for a pet", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "id", "value": "<long>"}, {"key": "petId", "value": "<long>"}, {"key": "quantity", "value": "<integer>"}, {"key": "shipDate", "value": "<dateTime>"}, {"key": "status", "value": "placed", "description": "Order Status"}, {"key": "complete", "value": "<boolean>"}]}, "url": "{{baseUrl}}/store/order", "description": "Place a new order in the store"}, "response": [{"name": "successful operation", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "id", "value": "<long>"}, {"key": "petId", "value": "<long>"}, {"key": "quantity", "value": "<integer>"}, {"key": "shipDate", "value": "<dateTime>"}, {"key": "status", "value": "placed", "description": "Order Status"}, {"key": "complete", "value": "<boolean>"}]}, "url": "{{baseUrl}}/store/order"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<long>\",\n  \"petId\": \"<long>\",\n  \"quantity\": \"<integer>\",\n  \"shipDate\": \"<dateTime>\",\n  \"status\": \"placed\",\n  \"complete\": \"<boolean>\"\n}"}, {"name": "Invalid input", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "id", "value": "<long>"}, {"key": "petId", "value": "<long>"}, {"key": "quantity", "value": "<integer>"}, {"key": "shipDate", "value": "<dateTime>"}, {"key": "status", "value": "placed", "description": "Order Status"}, {"key": "complete", "value": "<boolean>"}]}, "url": "{{baseUrl}}/store/order"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}, {"name": "Validation exception", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "id", "value": "<long>"}, {"key": "petId", "value": "<long>"}, {"key": "quantity", "value": "<integer>"}, {"key": "shipDate", "value": "<dateTime>"}, {"key": "status", "value": "placed", "description": "Order Status"}, {"key": "complete", "value": "<boolean>"}]}, "url": "{{baseUrl}}/store/order"}, "status": "Unprocessable Entity (WebDAV) (RFC 4918)", "code": 422, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}]}]}, {"name": "user", "item": [{"name": "createWithList", "item": [{"name": "Creates list of users with given input array", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"id\": \"<long>\",\n    \"username\": \"<string>\",\n    \"firstName\": \"<string>\",\n    \"lastName\": \"<string>\",\n    \"email\": \"<string>\",\n    \"password\": \"<string>\",\n    \"phone\": \"<string>\",\n    \"userStatus\": \"<integer>\"\n  },\n  {\n    \"id\": \"<long>\",\n    \"username\": \"<string>\",\n    \"firstName\": \"<string>\",\n    \"lastName\": \"<string>\",\n    \"email\": \"<string>\",\n    \"password\": \"<string>\",\n    \"phone\": \"<string>\",\n    \"userStatus\": \"<integer>\"\n  }\n]", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/user/createWithList", "description": "Creates list of users with given input array"}, "response": [{"name": "Successful operation", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"id\": \"<long>\",\n    \"username\": \"<string>\",\n    \"firstName\": \"<string>\",\n    \"lastName\": \"<string>\",\n    \"email\": \"<string>\",\n    \"password\": \"<string>\",\n    \"phone\": \"<string>\",\n    \"userStatus\": \"<integer>\"\n  },\n  {\n    \"id\": \"<long>\",\n    \"username\": \"<string>\",\n    \"firstName\": \"<string>\",\n    \"lastName\": \"<string>\",\n    \"email\": \"<string>\",\n    \"password\": \"<string>\",\n    \"phone\": \"<string>\",\n    \"userStatus\": \"<integer>\"\n  }\n]", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/user/createWithList"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<long>\",\n  \"username\": \"<string>\",\n  \"firstName\": \"<string>\",\n  \"lastName\": \"<string>\",\n  \"email\": \"<string>\",\n  \"password\": \"<string>\",\n  \"phone\": \"<string>\",\n  \"userStatus\": \"<integer>\"\n}"}, {"name": "successful operation", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"id\": \"<long>\",\n    \"username\": \"<string>\",\n    \"firstName\": \"<string>\",\n    \"lastName\": \"<string>\",\n    \"email\": \"<string>\",\n    \"password\": \"<string>\",\n    \"phone\": \"<string>\",\n    \"userStatus\": \"<integer>\"\n  },\n  {\n    \"id\": \"<long>\",\n    \"username\": \"<string>\",\n    \"firstName\": \"<string>\",\n    \"lastName\": \"<string>\",\n    \"email\": \"<string>\",\n    \"password\": \"<string>\",\n    \"phone\": \"<string>\",\n    \"userStatus\": \"<integer>\"\n  }\n]", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": "{{baseUrl}}/user/createWithList"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}]}, {"name": "login", "item": [{"name": "Logs user into the system", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/xml"}], "url": {"raw": "{{baseUrl}}/user/login?username=<string>&password=<string>", "host": ["{{baseUrl}}"], "path": ["user", "login"], "query": [{"key": "username", "value": "<string>", "description": "The user name for login"}, {"key": "password", "value": "<string>", "description": "The password for login in clear text"}]}}, "response": [{"name": "successful operation", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/xml"}], "url": {"raw": "{{baseUrl}}/user/login?username=<string>&password=<string>", "host": ["{{baseUrl}}"], "path": ["user", "login"], "query": [{"key": "username", "value": "<string>", "description": "The user name for login"}, {"key": "password", "value": "<string>", "description": "The password for login in clear text"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Rate-Limit", "value": "<integer>", "description": {"content": "calls per hour allowed by the user", "type": "text/plain"}}, {"key": "X-Expires-After", "value": "<dateTime>", "description": {"content": "date in UTC when token expires", "type": "text/plain"}}], "cookie": [], "body": "<string>"}, {"name": "Invalid username/password supplied", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/user/login?username=<string>&password=<string>", "host": ["{{baseUrl}}"], "path": ["user", "login"], "query": [{"key": "username", "value": "<string>", "description": "The user name for login"}, {"key": "password", "value": "<string>", "description": "The password for login in clear text"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}]}, {"name": "logout", "item": [{"name": "Logs out current logged in user session", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/user/logout"}, "response": [{"name": "successful operation", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/user/logout?aa", "host": ["{{baseUrl}}"], "path": ["user", "logout"], "query": [{"key": "aa", "value": null}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}]}, {"name": "{username}", "item": [{"name": "Get user by user name", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username", "value": "<string>", "description": "(Required) The name that needs to be fetched. Use user1 for testing. "}]}}, "response": [{"name": "successful operation", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<long>\",\n  \"username\": \"<string>\",\n  \"firstName\": \"<string>\",\n  \"lastName\": \"<string>\",\n  \"email\": \"<string>\",\n  \"password\": \"<string>\",\n  \"phone\": \"<string>\",\n  \"userStatus\": \"<integer>\"\n}"}, {"name": "Invalid username supplied", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}, {"name": "User not found", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}, {"name": "Update user", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "id", "value": "<long>"}, {"key": "username", "value": "<string>"}, {"key": "firstName", "value": "<string>"}, {"key": "lastName", "value": "<string>"}, {"key": "email", "value": "<string>"}, {"key": "password", "value": "<string>"}, {"key": "phone", "value": "<string>"}, {"key": "userStatus", "value": "<integer>", "description": "User Status"}]}, "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username", "value": "<string>", "description": "(Required) name that need to be deleted"}]}, "description": "This can only be done by the logged in user."}, "response": [{"name": "successful operation", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "id", "value": "<long>"}, {"key": "username", "value": "<string>"}, {"key": "firstName", "value": "<string>"}, {"key": "lastName", "value": "<string>"}, {"key": "email", "value": "<string>"}, {"key": "password", "value": "<string>"}, {"key": "phone", "value": "<string>"}, {"key": "userStatus", "value": "<integer>", "description": "User Status"}]}, "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}, {"name": "Delete user", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username", "value": "<string>", "description": "(Required) The name that needs to be deleted"}]}, "description": "This can only be done by the logged in user."}, "response": [{"name": "Invalid username supplied", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}, {"name": "User not found", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}]}, {"name": "Create user", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "id", "value": "<long>"}, {"key": "username", "value": "<string>"}, {"key": "firstName", "value": "<string>"}, {"key": "lastName", "value": "<string>"}, {"key": "email", "value": "<string>"}, {"key": "password", "value": "<string>"}, {"key": "phone", "value": "<string>"}, {"key": "userStatus", "value": "<integer>", "description": "User Status"}]}, "url": "{{baseUrl}}/user", "description": "This can only be done by the logged in user."}, "response": [{"name": "successful operation", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "id", "value": "<long>"}, {"key": "username", "value": "<string>"}, {"key": "firstName", "value": "<string>"}, {"key": "lastName", "value": "<string>"}, {"key": "email", "value": "<string>"}, {"key": "password", "value": "<string>"}, {"key": "phone", "value": "<string>"}, {"key": "userStatus", "value": "<integer>", "description": "User Status"}]}, "url": "{{baseUrl}}/user"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<long>\",\n  \"username\": \"<string>\",\n  \"firstName\": \"<string>\",\n  \"lastName\": \"<string>\",\n  \"email\": \"<string>\",\n  \"password\": \"<string>\",\n  \"phone\": \"<string>\",\n  \"userStatus\": \"<integer>\"\n}"}]}]}], "variable": [{"key": "baseUrl", "value": "https://petstore3.swagger.io/api/v3"}]}