<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="5.0" jmeter="5.4.1">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="测试计划" enabled="true">
      <stringProp name="TestPlan.comments"></stringProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <boolProp name="TestPlan.tearDown_on_shutdown">true</boolProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="用户定义的变量" enabled="true">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
      <stringProp name="TestPlan.user_define_classpath"></stringProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="线程组" enabled="true">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="循环控制器" enabled="true">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">1</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">1</stringProp>
        <stringProp name="ThreadGroup.ramp_time">1</stringProp>
        <boolProp name="ThreadGroup.scheduler">false</boolProp>
        <stringProp name="ThreadGroup.duration"></stringProp>
        <stringProp name="ThreadGroup.delay"></stringProp>
        <boolProp name="ThreadGroup.same_user_on_next_iteration">true</boolProp>
      </ThreadGroup>
      <hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="post-for-page" enabled="true">
          <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{&quot;current&quot;:1,&quot;pageSize&quot;:18,&quot;sort&quot;:{},&quot;keyword&quot;:&quot;&quot;,&quot;combine&quot;:{},&quot;searchMode&quot;:&quot;AND&quot;,&quot;projectId&quot;:&quot;718255970852864&quot;,&quot;moduleIds&quot;:[],&quot;filter&quot;:{}}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain">*************</stringProp>
          <stringProp name="HTTPSampler.port">8081</stringProp>
          <stringProp name="HTTPSampler.protocol">http</stringProp>
          <stringProp name="HTTPSampler.contentEncoding"></stringProp>
          <stringProp name="HTTPSampler.path">/api/scenario/page</stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">false</boolProp>
          <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
          <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
          <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
          <stringProp name="HTTPSampler.connect_timeout"></stringProp>
          <stringProp name="HTTPSampler.response_timeout"></stringProp>
        </HTTPSamplerProxy>
        <hashTree>
          <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP信息头管理器" enabled="true">
            <collectionProp name="HeaderManager.headers">
              <elementProp name="Accept" elementType="Header">
                <stringProp name="Header.name">Accept</stringProp>
                <stringProp name="Header.value">application/json, text/plain, */*</stringProp>
              </elementProp>
              <elementProp name="Accept-Encoding" elementType="Header">
                <stringProp name="Header.name">Accept-Encoding</stringProp>
                <stringProp name="Header.value">gzip, deflate</stringProp>
              </elementProp>
              <elementProp name="Accept-Language" elementType="Header">
                <stringProp name="Header.name">Accept-Language</stringProp>
                <stringProp name="Header.value">zh-CN</stringProp>
              </elementProp>
              <elementProp name="CSRF-TOKEN" elementType="Header">
                <stringProp name="Header.name">CSRF-TOKEN</stringProp>
                <stringProp name="Header.value">WOtvvhbURKiZae0Sdo8uOP/S0my8bcKKpKvsozjSzjfbjP92v4dZr9ncnu2aR8/7yaJXYvXvYn4PEvSkW96zDG+b7nHz0OddH6/xpDu4kIk=</stringProp>
              </elementProp>
              <elementProp name="Connection" elementType="Header">
                <stringProp name="Header.name">Connection</stringProp>
                <stringProp name="Header.value">keep-alive</stringProp>
              </elementProp>
              <elementProp name="Content-Length" elementType="Header">
                <stringProp name="Header.name">Content-Length</stringProp>
                <stringProp name="Header.value">139</stringProp>
              </elementProp>
              <elementProp name="Content-Type" elementType="Header">
                <stringProp name="Header.name">Content-Type</stringProp>
                <stringProp name="Header.value">application/json;charset=UTF-8</stringProp>
              </elementProp>
              <elementProp name="Host" elementType="Header">
                <stringProp name="Header.name">Host</stringProp>
                <stringProp name="Header.value">*************:8081</stringProp>
              </elementProp>
              <elementProp name="ORGANIZATION" elementType="Header">
                <stringProp name="Header.name">ORGANIZATION</stringProp>
                <stringProp name="Header.value">717345437786112</stringProp>
              </elementProp>
              <elementProp name="Origin" elementType="Header">
                <stringProp name="Header.name">Origin</stringProp>
                <stringProp name="Header.value">http://*************:8081</stringProp>
              </elementProp>
              <elementProp name="PROJECT" elementType="Header">
                <stringProp name="Header.name">PROJECT</stringProp>
                <stringProp name="Header.value">718255970852864</stringProp>
              </elementProp>
              <elementProp name="Referer" elementType="Header">
                <stringProp name="Header.name">Referer</stringProp>
                <stringProp name="Header.value">http://*************:8081/</stringProp>
              </elementProp>
              <elementProp name="User-Agent" elementType="Header">
                <stringProp name="Header.name">User-Agent</stringProp>
                <stringProp name="Header.value">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36</stringProp>
              </elementProp>
              <elementProp name="X-AUTH-TOKEN" elementType="Header">
                <stringProp name="Header.name">X-AUTH-TOKEN</stringProp>
                <stringProp name="Header.value">61afde41-15d2-47c1-b1d1-751a4bed99f9</stringProp>
              </elementProp>
            </collectionProp>
          </HeaderManager>
          <hashTree/>
          <JSR223PreProcessor guiclass="TestBeanGUI" testclass="JSR223PreProcessor" testname="JSR223 预处理程序" enabled="true">
            <stringProp name="cacheKey">true</stringProp>
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <stringProp name="script">log.info(&quot;sty ----&gt; no 1&quot;);</stringProp>
            <stringProp name="scriptLanguage">groovy</stringProp>
          </JSR223PreProcessor>
          <hashTree/>
          <ConstantTimer guiclass="ConstantTimerGui" testclass="ConstantTimer" testname="固定定时器" enabled="true">
            <stringProp name="ConstantTimer.delay">3300</stringProp>
          </ConstantTimer>
          <hashTree/>
          <JDBCDataSource guiclass="TestBeanGUI" testclass="JDBCDataSource" testname="JDBC Connection Configuration" enabled="true">
            <boolProp name="autocommit">true</boolProp>
            <stringProp name="checkQuery"></stringProp>
            <stringProp name="connectionAge">5000</stringProp>
            <stringProp name="connectionProperties"></stringProp>
            <stringProp name="dataSource">18数据库</stringProp>
            <stringProp name="dbUrl">*****************************************************************************************************************************************************************************************************</stringProp>
            <stringProp name="driver">com.mysql.jdbc.Driver</stringProp>
            <stringProp name="initQuery"></stringProp>
            <boolProp name="keepAlive">true</boolProp>
            <stringProp name="password">Password123@mysql</stringProp>
            <stringProp name="poolMax">0</stringProp>
            <boolProp name="preinit">false</boolProp>
            <stringProp name="timeout">10000</stringProp>
            <stringProp name="transactionIsolation">DEFAULT</stringProp>
            <stringProp name="trimInterval">60000</stringProp>
            <stringProp name="username">root</stringProp>
          </JDBCDataSource>
          <hashTree/>
          <JDBCPreProcessor guiclass="TestBeanGUI" testclass="JDBCPreProcessor" testname="JDBC 预处理程序" enabled="true">
            <stringProp name="dataSource">id</stringProp>
            <stringProp name="query">select id,name from user</stringProp>
            <stringProp name="queryArguments">id</stringProp>
            <stringProp name="queryArgumentsTypes">string</stringProp>
            <stringProp name="queryTimeout">1000</stringProp>
            <stringProp name="queryType">Select Statement</stringProp>
            <stringProp name="resultSetHandler">Store as String</stringProp>
            <stringProp name="resultSetMaxRows">20</stringProp>
            <stringProp name="resultVariable">username</stringProp>
            <stringProp name="variableNames">userid</stringProp>
          </JDBCPreProcessor>
          <hashTree/>
          <BeanShellPreProcessor guiclass="TestBeanGUI" testclass="BeanShellPreProcessor" testname="BeanShell 预处理程序" enabled="true">
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <boolProp name="resetInterpreter">false</boolProp>
            <stringProp name="script">log.info(&quot;print jdbc&quot;);
</stringProp>
          </BeanShellPreProcessor>
          <hashTree/>
          <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="JSON提取器" enabled="true">
            <stringProp name="TestPlan.comments">aaaaaaa</stringProp>
            <stringProp name="JSONPostProcessor.referenceNames">namesofcreated</stringProp>
            <stringProp name="JSONPostProcessor.jsonPathExprs">jsonpathexpre</stringProp>
            <stringProp name="JSONPostProcessor.match_numbers">0</stringProp>
            <boolProp name="JSONPostProcessor.compute_concat">true</boolProp>
            <stringProp name="JSONPostProcessor.defaultValues">zzzzz</stringProp>
            <stringProp name="Sample.scope">all</stringProp>
          </JSONPostProcessor>
          <hashTree/>
          <JSR223PostProcessor guiclass="TestBeanGUI" testclass="JSR223PostProcessor" testname="JSR223 后置处理程序" enabled="true">
            <stringProp name="cacheKey">true</stringProp>
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <stringProp name="script">log.info(&quot;sty &lt;---- no 1&quot;);</stringProp>
            <stringProp name="scriptLanguage">groovy</stringProp>
          </JSR223PostProcessor>
          <hashTree/>
          <BeanShellPostProcessor guiclass="TestBeanGUI" testclass="BeanShellPostProcessor" testname="BeanShell 后置处理程序" enabled="true">
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <boolProp name="resetInterpreter">false</boolProp>
            <stringProp name="script"></stringProp>
          </BeanShellPostProcessor>
          <hashTree/>
          <JDBCPostProcessor guiclass="TestBeanGUI" testclass="JDBCPostProcessor" testname="JDBC 后置处理程序" enabled="true">
            <stringProp name="dataSource">JDBC Connection Configuration</stringProp>
            <stringProp name="query">select name from user</stringProp>
            <stringProp name="queryArguments">name</stringProp>
            <stringProp name="queryArgumentsTypes">name</stringProp>
            <stringProp name="queryTimeout">6000</stringProp>
            <stringProp name="queryType">Select Statement</stringProp>
            <stringProp name="resultSetHandler">Store as String</stringProp>
            <stringProp name="resultSetMaxRows">2</stringProp>
            <stringProp name="resultVariable">name</stringProp>
            <stringProp name="variableNames">name</stringProp>
          </JDBCPostProcessor>
          <hashTree/>
          <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="正则表达式提取器" enabled="true">
            <stringProp name="RegexExtractor.useHeaders">message</stringProp>
            <stringProp name="RegexExtractor.refname">zhengzemingcheng</stringProp>
            <stringProp name="RegexExtractor.regex">zhengzebiaodashi</stringProp>
            <stringProp name="RegexExtractor.template">moban</stringProp>
            <stringProp name="RegexExtractor.default"></stringProp>
            <stringProp name="RegexExtractor.match_number">7</stringProp>
          </RegexExtractor>
          <hashTree/>
          <ResponseAssertion guiclass="AssertionGui" testclass="ResponseAssertion" testname="响应断言" enabled="true">
            <collectionProp name="Asserion.test_strings"/>
            <stringProp name="Assertion.custom_message"></stringProp>
            <stringProp name="Assertion.test_field">Assertion.response_data</stringProp>
            <boolProp name="Assertion.assume_success">false</boolProp>
            <intProp name="Assertion.test_type">16</intProp>
          </ResponseAssertion>
          <hashTree/>
          <XPathExtractor guiclass="XPathExtractorGui" testclass="XPathExtractor" testname="XPath提取器" enabled="true">
            <stringProp name="TestPlan.comments">xxxdes</stringProp>
            <stringProp name="XPathExtractor.default">-11</stringProp>
            <stringProp name="XPathExtractor.refname">2312312</stringProp>
            <stringProp name="XPathExtractor.matchNumber">-1</stringProp>
            <stringProp name="XPathExtractor.xpathQuery">xpathquery</stringProp>
            <boolProp name="XPathExtractor.validate">true</boolProp>
            <boolProp name="XPathExtractor.tolerant">false</boolProp>
            <boolProp name="XPathExtractor.namespace">true</boolProp>
            <boolProp name="XPathExtractor.whitespace">true</boolProp>
            <boolProp name="XPathExtractor.download_dtds">true</boolProp>
          </XPathExtractor>
          <hashTree/>
          <XPath2Extractor guiclass="XPath2ExtractorGui" testclass="XPath2Extractor" testname="XPath2 Extractor" enabled="true">
            <stringProp name="TestPlan.comments">xpathquery注释</stringProp>
            <stringProp name="XPathExtractor2.default">-5</stringProp>
            <stringProp name="XPathExtractor2.refname">asaaa</stringProp>
            <stringProp name="XPathExtractor2.matchNumber">1</stringProp>
            <stringProp name="XPathExtractor2.xpathQuery">xpathquery</stringProp>
            <stringProp name="XPathExtractor2.namespaces"></stringProp>
            <stringProp name="Sample.scope">children</stringProp>
          </XPath2Extractor>
          <hashTree/>
        </hashTree>
        <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="察看结果树" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="post-for-page2" enabled="true">
          <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{&quot;current&quot;:1,&quot;pageSize&quot;:18,&quot;sort&quot;:{},&quot;keyword&quot;:&quot;&quot;,&quot;combine&quot;:{},&quot;searchMode&quot;:&quot;AND&quot;,&quot;projectId&quot;:&quot;718255970852864&quot;,&quot;moduleIds&quot;:[],&quot;filter&quot;:{}}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain">*************</stringProp>
          <stringProp name="HTTPSampler.port">8081</stringProp>
          <stringProp name="HTTPSampler.protocol">http</stringProp>
          <stringProp name="HTTPSampler.contentEncoding"></stringProp>
          <stringProp name="HTTPSampler.path">/api/scenario/page</stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">false</boolProp>
          <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
          <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
          <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
          <stringProp name="HTTPSampler.connect_timeout"></stringProp>
          <stringProp name="HTTPSampler.response_timeout"></stringProp>
        </HTTPSamplerProxy>
        <hashTree>
          <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP信息头管理器" enabled="true">
            <collectionProp name="HeaderManager.headers">
              <elementProp name="Accept" elementType="Header">
                <stringProp name="Header.name">Accept</stringProp>
                <stringProp name="Header.value">application/json, text/plain, */*</stringProp>
              </elementProp>
              <elementProp name="Accept-Encoding" elementType="Header">
                <stringProp name="Header.name">Accept-Encoding</stringProp>
                <stringProp name="Header.value">gzip, deflate</stringProp>
              </elementProp>
              <elementProp name="Accept-Language" elementType="Header">
                <stringProp name="Header.name">Accept-Language</stringProp>
                <stringProp name="Header.value">zh-CN</stringProp>
              </elementProp>
              <elementProp name="CSRF-TOKEN" elementType="Header">
                <stringProp name="Header.name">CSRF-TOKEN</stringProp>
                <stringProp name="Header.value">WOtvvhbURKiZae0Sdo8uOP/S0my8bcKKpKvsozjSzjfbjP92v4dZr9ncnu2aR8/7yaJXYvXvYn4PEvSkW96zDG+b7nHz0OddH6/xpDu4kIk=</stringProp>
              </elementProp>
              <elementProp name="Connection" elementType="Header">
                <stringProp name="Header.name">Connection</stringProp>
                <stringProp name="Header.value">keep-alive</stringProp>
              </elementProp>
              <elementProp name="Content-Length" elementType="Header">
                <stringProp name="Header.name">Content-Length</stringProp>
                <stringProp name="Header.value">139</stringProp>
              </elementProp>
              <elementProp name="Content-Type" elementType="Header">
                <stringProp name="Header.name">Content-Type</stringProp>
                <stringProp name="Header.value">application/json;charset=UTF-8</stringProp>
              </elementProp>
              <elementProp name="Host" elementType="Header">
                <stringProp name="Header.name">Host</stringProp>
                <stringProp name="Header.value">*************:8081</stringProp>
              </elementProp>
              <elementProp name="ORGANIZATION" elementType="Header">
                <stringProp name="Header.name">ORGANIZATION</stringProp>
                <stringProp name="Header.value">717345437786112</stringProp>
              </elementProp>
              <elementProp name="Origin" elementType="Header">
                <stringProp name="Header.name">Origin</stringProp>
                <stringProp name="Header.value">http://*************:8081</stringProp>
              </elementProp>
              <elementProp name="PROJECT" elementType="Header">
                <stringProp name="Header.name">PROJECT</stringProp>
                <stringProp name="Header.value">718255970852864</stringProp>
              </elementProp>
              <elementProp name="Referer" elementType="Header">
                <stringProp name="Header.name">Referer</stringProp>
                <stringProp name="Header.value">http://*************:8081/</stringProp>
              </elementProp>
              <elementProp name="User-Agent" elementType="Header">
                <stringProp name="Header.name">User-Agent</stringProp>
                <stringProp name="Header.value">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36</stringProp>
              </elementProp>
              <elementProp name="X-AUTH-TOKEN" elementType="Header">
                <stringProp name="Header.name">X-AUTH-TOKEN</stringProp>
                <stringProp name="Header.value">61afde41-15d2-47c1-b1d1-751a4bed99f9</stringProp>
              </elementProp>
            </collectionProp>
          </HeaderManager>
          <hashTree/>
          <JSR223PreProcessor guiclass="TestBeanGUI" testclass="JSR223PreProcessor" testname="JSR223 预处理程序" enabled="true">
            <stringProp name="cacheKey">true</stringProp>
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <stringProp name="script">log.info(&quot;sty ----&gt; no 1&quot;);</stringProp>
            <stringProp name="scriptLanguage">groovy</stringProp>
          </JSR223PreProcessor>
          <hashTree/>
          <ConstantTimer guiclass="ConstantTimerGui" testclass="ConstantTimer" testname="固定定时器" enabled="true">
            <stringProp name="ConstantTimer.delay">3300</stringProp>
          </ConstantTimer>
          <hashTree/>
          <JDBCDataSource guiclass="TestBeanGUI" testclass="JDBCDataSource" testname="JDBC Connection Configuration" enabled="true">
            <boolProp name="autocommit">true</boolProp>
            <stringProp name="checkQuery"></stringProp>
            <stringProp name="connectionAge">5000</stringProp>
            <stringProp name="connectionProperties"></stringProp>
            <stringProp name="dataSource">18数据库</stringProp>
            <stringProp name="dbUrl">*****************************************************************************************************************************************************************************************************</stringProp>
            <stringProp name="driver">com.mysql.jdbc.Driver</stringProp>
            <stringProp name="initQuery"></stringProp>
            <boolProp name="keepAlive">true</boolProp>
            <stringProp name="password">Password123@mysql</stringProp>
            <stringProp name="poolMax">0</stringProp>
            <boolProp name="preinit">false</boolProp>
            <stringProp name="timeout">10000</stringProp>
            <stringProp name="transactionIsolation">DEFAULT</stringProp>
            <stringProp name="trimInterval">60000</stringProp>
            <stringProp name="username">root</stringProp>
          </JDBCDataSource>
          <hashTree/>
          <JDBCPreProcessor guiclass="TestBeanGUI" testclass="JDBCPreProcessor" testname="JDBC 预处理程序" enabled="true">
            <stringProp name="dataSource">id</stringProp>
            <stringProp name="query">select id,name from user</stringProp>
            <stringProp name="queryArguments">id</stringProp>
            <stringProp name="queryArgumentsTypes">string</stringProp>
            <stringProp name="queryTimeout">1000</stringProp>
            <stringProp name="queryType">Select Statement</stringProp>
            <stringProp name="resultSetHandler">Store as String</stringProp>
            <stringProp name="resultSetMaxRows">20</stringProp>
            <stringProp name="resultVariable">username</stringProp>
            <stringProp name="variableNames">userid</stringProp>
          </JDBCPreProcessor>
          <hashTree/>
          <BeanShellPreProcessor guiclass="TestBeanGUI" testclass="BeanShellPreProcessor" testname="BeanShell 预处理程序" enabled="true">
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <boolProp name="resetInterpreter">false</boolProp>
            <stringProp name="script">log.info(&quot;print jdbc&quot;);
</stringProp>
          </BeanShellPreProcessor>
          <hashTree/>
          <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="JSON提取器" enabled="true">
            <stringProp name="TestPlan.comments">aaaaaaa</stringProp>
            <stringProp name="JSONPostProcessor.referenceNames">namesofcreated</stringProp>
            <stringProp name="JSONPostProcessor.jsonPathExprs">jsonpathexpre</stringProp>
            <stringProp name="JSONPostProcessor.match_numbers">0</stringProp>
            <boolProp name="JSONPostProcessor.compute_concat">true</boolProp>
            <stringProp name="JSONPostProcessor.defaultValues">zzzzz</stringProp>
            <stringProp name="Sample.scope">all</stringProp>
          </JSONPostProcessor>
          <hashTree/>
          <JSR223PostProcessor guiclass="TestBeanGUI" testclass="JSR223PostProcessor" testname="JSR223 后置处理程序" enabled="true">
            <stringProp name="cacheKey">true</stringProp>
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <stringProp name="script">log.info(&quot;sty &lt;---- no 1&quot;);</stringProp>
            <stringProp name="scriptLanguage">groovy</stringProp>
          </JSR223PostProcessor>
          <hashTree/>
          <BeanShellPostProcessor guiclass="TestBeanGUI" testclass="BeanShellPostProcessor" testname="BeanShell 后置处理程序" enabled="true">
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <boolProp name="resetInterpreter">false</boolProp>
            <stringProp name="script"></stringProp>
          </BeanShellPostProcessor>
          <hashTree/>
          <JDBCPostProcessor guiclass="TestBeanGUI" testclass="JDBCPostProcessor" testname="JDBC 后置处理程序" enabled="true">
            <stringProp name="dataSource">JDBC Connection Configuration</stringProp>
            <stringProp name="query">select name from user</stringProp>
            <stringProp name="queryArguments">name</stringProp>
            <stringProp name="queryArgumentsTypes">name</stringProp>
            <stringProp name="queryTimeout">6000</stringProp>
            <stringProp name="queryType">Select Statement</stringProp>
            <stringProp name="resultSetHandler">Store as String</stringProp>
            <stringProp name="resultSetMaxRows">2</stringProp>
            <stringProp name="resultVariable">name</stringProp>
            <stringProp name="variableNames">name</stringProp>
          </JDBCPostProcessor>
          <hashTree/>
          <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="正则表达式提取器" enabled="true">
            <stringProp name="RegexExtractor.useHeaders">message</stringProp>
            <stringProp name="RegexExtractor.refname">zhengzemingcheng</stringProp>
            <stringProp name="RegexExtractor.regex">zhengzebiaodashi</stringProp>
            <stringProp name="RegexExtractor.template">moban</stringProp>
            <stringProp name="RegexExtractor.default"></stringProp>
            <stringProp name="RegexExtractor.match_number">7</stringProp>
          </RegexExtractor>
          <hashTree/>
          <ResponseAssertion guiclass="AssertionGui" testclass="ResponseAssertion" testname="响应断言" enabled="true">
            <collectionProp name="Asserion.test_strings"/>
            <stringProp name="Assertion.custom_message"></stringProp>
            <stringProp name="Assertion.test_field">Assertion.response_data</stringProp>
            <boolProp name="Assertion.assume_success">false</boolProp>
            <intProp name="Assertion.test_type">16</intProp>
          </ResponseAssertion>
          <hashTree/>
          <XPathExtractor guiclass="XPathExtractorGui" testclass="XPathExtractor" testname="XPath提取器" enabled="true">
            <stringProp name="TestPlan.comments">xxxdes</stringProp>
            <stringProp name="XPathExtractor.default">-11</stringProp>
            <stringProp name="XPathExtractor.refname">2312312</stringProp>
            <stringProp name="XPathExtractor.matchNumber">-1</stringProp>
            <stringProp name="XPathExtractor.xpathQuery">xpathquery</stringProp>
            <boolProp name="XPathExtractor.validate">true</boolProp>
            <boolProp name="XPathExtractor.tolerant">false</boolProp>
            <boolProp name="XPathExtractor.namespace">true</boolProp>
            <boolProp name="XPathExtractor.whitespace">true</boolProp>
            <boolProp name="XPathExtractor.download_dtds">true</boolProp>
          </XPathExtractor>
          <hashTree/>
          <XPath2Extractor guiclass="XPath2ExtractorGui" testclass="XPath2Extractor" testname="XPath2 Extractor" enabled="true">
            <stringProp name="TestPlan.comments">xpathquery注释</stringProp>
            <stringProp name="XPathExtractor2.default">-5</stringProp>
            <stringProp name="XPathExtractor2.refname">asaaa</stringProp>
            <stringProp name="XPathExtractor2.matchNumber">1</stringProp>
            <stringProp name="XPathExtractor2.xpathQuery">xpathquery</stringProp>
            <stringProp name="XPathExtractor2.namespaces"></stringProp>
            <stringProp name="Sample.scope">children</stringProp>
          </XPath2Extractor>
          <hashTree/>
        </hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="post-for-page3" enabled="true">
          <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{&quot;current&quot;:1,&quot;pageSize&quot;:18,&quot;sort&quot;:{},&quot;keyword&quot;:&quot;&quot;,&quot;combine&quot;:{},&quot;searchMode&quot;:&quot;AND&quot;,&quot;projectId&quot;:&quot;718255970852864&quot;,&quot;moduleIds&quot;:[],&quot;filter&quot;:{}}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain">*************</stringProp>
          <stringProp name="HTTPSampler.port">8081</stringProp>
          <stringProp name="HTTPSampler.protocol">http</stringProp>
          <stringProp name="HTTPSampler.contentEncoding"></stringProp>
          <stringProp name="HTTPSampler.path">/api/scenario/page</stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">false</boolProp>
          <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
          <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
          <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
          <stringProp name="HTTPSampler.connect_timeout"></stringProp>
          <stringProp name="HTTPSampler.response_timeout"></stringProp>
        </HTTPSamplerProxy>
        <hashTree>
          <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP信息头管理器" enabled="true">
            <collectionProp name="HeaderManager.headers">
              <elementProp name="Accept" elementType="Header">
                <stringProp name="Header.name">Accept</stringProp>
                <stringProp name="Header.value">application/json, text/plain, */*</stringProp>
              </elementProp>
              <elementProp name="Accept-Encoding" elementType="Header">
                <stringProp name="Header.name">Accept-Encoding</stringProp>
                <stringProp name="Header.value">gzip, deflate</stringProp>
              </elementProp>
              <elementProp name="Accept-Language" elementType="Header">
                <stringProp name="Header.name">Accept-Language</stringProp>
                <stringProp name="Header.value">zh-CN</stringProp>
              </elementProp>
              <elementProp name="CSRF-TOKEN" elementType="Header">
                <stringProp name="Header.name">CSRF-TOKEN</stringProp>
                <stringProp name="Header.value">WOtvvhbURKiZae0Sdo8uOP/S0my8bcKKpKvsozjSzjfbjP92v4dZr9ncnu2aR8/7yaJXYvXvYn4PEvSkW96zDG+b7nHz0OddH6/xpDu4kIk=</stringProp>
              </elementProp>
              <elementProp name="Connection" elementType="Header">
                <stringProp name="Header.name">Connection</stringProp>
                <stringProp name="Header.value">keep-alive</stringProp>
              </elementProp>
              <elementProp name="Content-Length" elementType="Header">
                <stringProp name="Header.name">Content-Length</stringProp>
                <stringProp name="Header.value">139</stringProp>
              </elementProp>
              <elementProp name="Content-Type" elementType="Header">
                <stringProp name="Header.name">Content-Type</stringProp>
                <stringProp name="Header.value">application/json;charset=UTF-8</stringProp>
              </elementProp>
              <elementProp name="Host" elementType="Header">
                <stringProp name="Header.name">Host</stringProp>
                <stringProp name="Header.value">*************:8081</stringProp>
              </elementProp>
              <elementProp name="ORGANIZATION" elementType="Header">
                <stringProp name="Header.name">ORGANIZATION</stringProp>
                <stringProp name="Header.value">717345437786112</stringProp>
              </elementProp>
              <elementProp name="Origin" elementType="Header">
                <stringProp name="Header.name">Origin</stringProp>
                <stringProp name="Header.value">http://*************:8081</stringProp>
              </elementProp>
              <elementProp name="PROJECT" elementType="Header">
                <stringProp name="Header.name">PROJECT</stringProp>
                <stringProp name="Header.value">718255970852864</stringProp>
              </elementProp>
              <elementProp name="Referer" elementType="Header">
                <stringProp name="Header.name">Referer</stringProp>
                <stringProp name="Header.value">http://*************:8081/</stringProp>
              </elementProp>
              <elementProp name="User-Agent" elementType="Header">
                <stringProp name="Header.name">User-Agent</stringProp>
                <stringProp name="Header.value">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36</stringProp>
              </elementProp>
              <elementProp name="X-AUTH-TOKEN" elementType="Header">
                <stringProp name="Header.name">X-AUTH-TOKEN</stringProp>
                <stringProp name="Header.value">61afde41-15d2-47c1-b1d1-751a4bed99f9</stringProp>
              </elementProp>
            </collectionProp>
          </HeaderManager>
          <hashTree/>
          <JSR223PreProcessor guiclass="TestBeanGUI" testclass="JSR223PreProcessor" testname="JSR223 预处理程序" enabled="true">
            <stringProp name="cacheKey">true</stringProp>
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <stringProp name="script">log.info(&quot;sty ----&gt; no 1&quot;);</stringProp>
            <stringProp name="scriptLanguage">groovy</stringProp>
          </JSR223PreProcessor>
          <hashTree/>
          <ConstantTimer guiclass="ConstantTimerGui" testclass="ConstantTimer" testname="固定定时器" enabled="true">
            <stringProp name="ConstantTimer.delay">3300</stringProp>
          </ConstantTimer>
          <hashTree/>
          <JDBCDataSource guiclass="TestBeanGUI" testclass="JDBCDataSource" testname="JDBC Connection Configuration" enabled="true">
            <boolProp name="autocommit">true</boolProp>
            <stringProp name="checkQuery"></stringProp>
            <stringProp name="connectionAge">5000</stringProp>
            <stringProp name="connectionProperties"></stringProp>
            <stringProp name="dataSource">18数据库</stringProp>
            <stringProp name="dbUrl">*****************************************************************************************************************************************************************************************************</stringProp>
            <stringProp name="driver">com.mysql.jdbc.Driver</stringProp>
            <stringProp name="initQuery"></stringProp>
            <boolProp name="keepAlive">true</boolProp>
            <stringProp name="password">Password123@mysql</stringProp>
            <stringProp name="poolMax">0</stringProp>
            <boolProp name="preinit">false</boolProp>
            <stringProp name="timeout">10000</stringProp>
            <stringProp name="transactionIsolation">DEFAULT</stringProp>
            <stringProp name="trimInterval">60000</stringProp>
            <stringProp name="username">root</stringProp>
          </JDBCDataSource>
          <hashTree/>
          <JDBCPreProcessor guiclass="TestBeanGUI" testclass="JDBCPreProcessor" testname="JDBC 预处理程序" enabled="true">
            <stringProp name="dataSource">id</stringProp>
            <stringProp name="query">select id,name from user</stringProp>
            <stringProp name="queryArguments">id</stringProp>
            <stringProp name="queryArgumentsTypes">string</stringProp>
            <stringProp name="queryTimeout">1000</stringProp>
            <stringProp name="queryType">Select Statement</stringProp>
            <stringProp name="resultSetHandler">Store as String</stringProp>
            <stringProp name="resultSetMaxRows">20</stringProp>
            <stringProp name="resultVariable">username</stringProp>
            <stringProp name="variableNames">userid</stringProp>
          </JDBCPreProcessor>
          <hashTree/>
          <BeanShellPreProcessor guiclass="TestBeanGUI" testclass="BeanShellPreProcessor" testname="BeanShell 预处理程序" enabled="true">
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <boolProp name="resetInterpreter">false</boolProp>
            <stringProp name="script">log.info(&quot;print jdbc&quot;);
</stringProp>
          </BeanShellPreProcessor>
          <hashTree/>
          <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="JSON提取器" enabled="true">
            <stringProp name="TestPlan.comments">aaaaaaa</stringProp>
            <stringProp name="JSONPostProcessor.referenceNames">namesofcreated</stringProp>
            <stringProp name="JSONPostProcessor.jsonPathExprs">jsonpathexpre</stringProp>
            <stringProp name="JSONPostProcessor.match_numbers">0</stringProp>
            <boolProp name="JSONPostProcessor.compute_concat">true</boolProp>
            <stringProp name="JSONPostProcessor.defaultValues">zzzzz</stringProp>
            <stringProp name="Sample.scope">all</stringProp>
          </JSONPostProcessor>
          <hashTree/>
          <JSR223PostProcessor guiclass="TestBeanGUI" testclass="JSR223PostProcessor" testname="JSR223 后置处理程序" enabled="true">
            <stringProp name="cacheKey">true</stringProp>
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <stringProp name="script">log.info(&quot;sty &lt;---- no 1&quot;);</stringProp>
            <stringProp name="scriptLanguage">groovy</stringProp>
          </JSR223PostProcessor>
          <hashTree/>
          <BeanShellPostProcessor guiclass="TestBeanGUI" testclass="BeanShellPostProcessor" testname="BeanShell 后置处理程序" enabled="true">
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <boolProp name="resetInterpreter">false</boolProp>
            <stringProp name="script"></stringProp>
          </BeanShellPostProcessor>
          <hashTree/>
          <JDBCPostProcessor guiclass="TestBeanGUI" testclass="JDBCPostProcessor" testname="JDBC 后置处理程序" enabled="true">
            <stringProp name="dataSource">JDBC Connection Configuration</stringProp>
            <stringProp name="query">select name from user</stringProp>
            <stringProp name="queryArguments">name</stringProp>
            <stringProp name="queryArgumentsTypes">name</stringProp>
            <stringProp name="queryTimeout">6000</stringProp>
            <stringProp name="queryType">Select Statement</stringProp>
            <stringProp name="resultSetHandler">Store as String</stringProp>
            <stringProp name="resultSetMaxRows">2</stringProp>
            <stringProp name="resultVariable">name</stringProp>
            <stringProp name="variableNames">name</stringProp>
          </JDBCPostProcessor>
          <hashTree/>
          <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="正则表达式提取器" enabled="true">
            <stringProp name="RegexExtractor.useHeaders">message</stringProp>
            <stringProp name="RegexExtractor.refname">zhengzemingcheng</stringProp>
            <stringProp name="RegexExtractor.regex">zhengzebiaodashi</stringProp>
            <stringProp name="RegexExtractor.template">moban</stringProp>
            <stringProp name="RegexExtractor.default"></stringProp>
            <stringProp name="RegexExtractor.match_number">7</stringProp>
          </RegexExtractor>
          <hashTree/>
          <ResponseAssertion guiclass="AssertionGui" testclass="ResponseAssertion" testname="响应断言" enabled="true">
            <collectionProp name="Asserion.test_strings"/>
            <stringProp name="Assertion.custom_message"></stringProp>
            <stringProp name="Assertion.test_field">Assertion.response_data</stringProp>
            <boolProp name="Assertion.assume_success">false</boolProp>
            <intProp name="Assertion.test_type">16</intProp>
          </ResponseAssertion>
          <hashTree/>
          <XPathExtractor guiclass="XPathExtractorGui" testclass="XPathExtractor" testname="XPath提取器" enabled="true">
            <stringProp name="TestPlan.comments">xxxdes</stringProp>
            <stringProp name="XPathExtractor.default">-11</stringProp>
            <stringProp name="XPathExtractor.refname">2312312</stringProp>
            <stringProp name="XPathExtractor.matchNumber">-1</stringProp>
            <stringProp name="XPathExtractor.xpathQuery">xpathquery</stringProp>
            <boolProp name="XPathExtractor.validate">true</boolProp>
            <boolProp name="XPathExtractor.tolerant">false</boolProp>
            <boolProp name="XPathExtractor.namespace">true</boolProp>
            <boolProp name="XPathExtractor.whitespace">true</boolProp>
            <boolProp name="XPathExtractor.download_dtds">true</boolProp>
          </XPathExtractor>
          <hashTree/>
          <XPath2Extractor guiclass="XPath2ExtractorGui" testclass="XPath2Extractor" testname="XPath2 Extractor" enabled="true">
            <stringProp name="TestPlan.comments">xpathquery注释</stringProp>
            <stringProp name="XPathExtractor2.default">-5</stringProp>
            <stringProp name="XPathExtractor2.refname">asaaa</stringProp>
            <stringProp name="XPathExtractor2.matchNumber">1</stringProp>
            <stringProp name="XPathExtractor2.xpathQuery">xpathquery</stringProp>
            <stringProp name="XPathExtractor2.namespaces"></stringProp>
            <stringProp name="Sample.scope">children</stringProp>
          </XPath2Extractor>
          <hashTree/>
        </hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="post-for-page4" enabled="true">
          <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{&quot;current&quot;:1,&quot;pageSize&quot;:18,&quot;sort&quot;:{},&quot;keyword&quot;:&quot;&quot;,&quot;combine&quot;:{},&quot;searchMode&quot;:&quot;AND&quot;,&quot;projectId&quot;:&quot;718255970852864&quot;,&quot;moduleIds&quot;:[],&quot;filter&quot;:{}}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain">*************</stringProp>
          <stringProp name="HTTPSampler.port">8081</stringProp>
          <stringProp name="HTTPSampler.protocol">http</stringProp>
          <stringProp name="HTTPSampler.contentEncoding"></stringProp>
          <stringProp name="HTTPSampler.path">/api/scenario/page</stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">false</boolProp>
          <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
          <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
          <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
          <stringProp name="HTTPSampler.connect_timeout"></stringProp>
          <stringProp name="HTTPSampler.response_timeout"></stringProp>
        </HTTPSamplerProxy>
        <hashTree>
          <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP信息头管理器" enabled="true">
            <collectionProp name="HeaderManager.headers">
              <elementProp name="Accept" elementType="Header">
                <stringProp name="Header.name">Accept</stringProp>
                <stringProp name="Header.value">application/json, text/plain, */*</stringProp>
              </elementProp>
              <elementProp name="Accept-Encoding" elementType="Header">
                <stringProp name="Header.name">Accept-Encoding</stringProp>
                <stringProp name="Header.value">gzip, deflate</stringProp>
              </elementProp>
              <elementProp name="Accept-Language" elementType="Header">
                <stringProp name="Header.name">Accept-Language</stringProp>
                <stringProp name="Header.value">zh-CN</stringProp>
              </elementProp>
              <elementProp name="CSRF-TOKEN" elementType="Header">
                <stringProp name="Header.name">CSRF-TOKEN</stringProp>
                <stringProp name="Header.value">WOtvvhbURKiZae0Sdo8uOP/S0my8bcKKpKvsozjSzjfbjP92v4dZr9ncnu2aR8/7yaJXYvXvYn4PEvSkW96zDG+b7nHz0OddH6/xpDu4kIk=</stringProp>
              </elementProp>
              <elementProp name="Connection" elementType="Header">
                <stringProp name="Header.name">Connection</stringProp>
                <stringProp name="Header.value">keep-alive</stringProp>
              </elementProp>
              <elementProp name="Content-Length" elementType="Header">
                <stringProp name="Header.name">Content-Length</stringProp>
                <stringProp name="Header.value">139</stringProp>
              </elementProp>
              <elementProp name="Content-Type" elementType="Header">
                <stringProp name="Header.name">Content-Type</stringProp>
                <stringProp name="Header.value">application/json;charset=UTF-8</stringProp>
              </elementProp>
              <elementProp name="Host" elementType="Header">
                <stringProp name="Header.name">Host</stringProp>
                <stringProp name="Header.value">*************:8081</stringProp>
              </elementProp>
              <elementProp name="ORGANIZATION" elementType="Header">
                <stringProp name="Header.name">ORGANIZATION</stringProp>
                <stringProp name="Header.value">717345437786112</stringProp>
              </elementProp>
              <elementProp name="Origin" elementType="Header">
                <stringProp name="Header.name">Origin</stringProp>
                <stringProp name="Header.value">http://*************:8081</stringProp>
              </elementProp>
              <elementProp name="PROJECT" elementType="Header">
                <stringProp name="Header.name">PROJECT</stringProp>
                <stringProp name="Header.value">718255970852864</stringProp>
              </elementProp>
              <elementProp name="Referer" elementType="Header">
                <stringProp name="Header.name">Referer</stringProp>
                <stringProp name="Header.value">http://*************:8081/</stringProp>
              </elementProp>
              <elementProp name="User-Agent" elementType="Header">
                <stringProp name="Header.name">User-Agent</stringProp>
                <stringProp name="Header.value">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36</stringProp>
              </elementProp>
              <elementProp name="X-AUTH-TOKEN" elementType="Header">
                <stringProp name="Header.name">X-AUTH-TOKEN</stringProp>
                <stringProp name="Header.value">61afde41-15d2-47c1-b1d1-751a4bed99f9</stringProp>
              </elementProp>
            </collectionProp>
          </HeaderManager>
          <hashTree/>
          <JSR223PreProcessor guiclass="TestBeanGUI" testclass="JSR223PreProcessor" testname="JSR223 预处理程序" enabled="true">
            <stringProp name="cacheKey">true</stringProp>
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <stringProp name="script">log.info(&quot;sty ----&gt; no 1&quot;);</stringProp>
            <stringProp name="scriptLanguage">groovy</stringProp>
          </JSR223PreProcessor>
          <hashTree/>
          <ConstantTimer guiclass="ConstantTimerGui" testclass="ConstantTimer" testname="固定定时器" enabled="true">
            <stringProp name="ConstantTimer.delay">3300</stringProp>
          </ConstantTimer>
          <hashTree/>
          <JDBCDataSource guiclass="TestBeanGUI" testclass="JDBCDataSource" testname="JDBC Connection Configuration" enabled="true">
            <boolProp name="autocommit">true</boolProp>
            <stringProp name="checkQuery"></stringProp>
            <stringProp name="connectionAge">5000</stringProp>
            <stringProp name="connectionProperties"></stringProp>
            <stringProp name="dataSource">18数据库</stringProp>
            <stringProp name="dbUrl">*****************************************************************************************************************************************************************************************************</stringProp>
            <stringProp name="driver">com.mysql.jdbc.Driver</stringProp>
            <stringProp name="initQuery"></stringProp>
            <boolProp name="keepAlive">true</boolProp>
            <stringProp name="password">Password123@mysql</stringProp>
            <stringProp name="poolMax">0</stringProp>
            <boolProp name="preinit">false</boolProp>
            <stringProp name="timeout">10000</stringProp>
            <stringProp name="transactionIsolation">DEFAULT</stringProp>
            <stringProp name="trimInterval">60000</stringProp>
            <stringProp name="username">root</stringProp>
          </JDBCDataSource>
          <hashTree/>
          <JDBCPreProcessor guiclass="TestBeanGUI" testclass="JDBCPreProcessor" testname="JDBC 预处理程序" enabled="true">
            <stringProp name="dataSource">id</stringProp>
            <stringProp name="query">select id,name from user</stringProp>
            <stringProp name="queryArguments">id</stringProp>
            <stringProp name="queryArgumentsTypes">string</stringProp>
            <stringProp name="queryTimeout">1000</stringProp>
            <stringProp name="queryType">Select Statement</stringProp>
            <stringProp name="resultSetHandler">Store as String</stringProp>
            <stringProp name="resultSetMaxRows">20</stringProp>
            <stringProp name="resultVariable">username</stringProp>
            <stringProp name="variableNames">userid</stringProp>
          </JDBCPreProcessor>
          <hashTree/>
          <BeanShellPreProcessor guiclass="TestBeanGUI" testclass="BeanShellPreProcessor" testname="BeanShell 预处理程序" enabled="true">
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <boolProp name="resetInterpreter">false</boolProp>
            <stringProp name="script">log.info(&quot;print jdbc&quot;);
</stringProp>
          </BeanShellPreProcessor>
          <hashTree/>
          <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="JSON提取器" enabled="true">
            <stringProp name="TestPlan.comments">aaaaaaa</stringProp>
            <stringProp name="JSONPostProcessor.referenceNames">namesofcreated</stringProp>
            <stringProp name="JSONPostProcessor.jsonPathExprs">jsonpathexpre</stringProp>
            <stringProp name="JSONPostProcessor.match_numbers">0</stringProp>
            <boolProp name="JSONPostProcessor.compute_concat">true</boolProp>
            <stringProp name="JSONPostProcessor.defaultValues">zzzzz</stringProp>
            <stringProp name="Sample.scope">all</stringProp>
          </JSONPostProcessor>
          <hashTree/>
          <JSR223PostProcessor guiclass="TestBeanGUI" testclass="JSR223PostProcessor" testname="JSR223 后置处理程序" enabled="true">
            <stringProp name="cacheKey">true</stringProp>
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <stringProp name="script">log.info(&quot;sty &lt;---- no 1&quot;);</stringProp>
            <stringProp name="scriptLanguage">groovy</stringProp>
          </JSR223PostProcessor>
          <hashTree/>
          <BeanShellPostProcessor guiclass="TestBeanGUI" testclass="BeanShellPostProcessor" testname="BeanShell 后置处理程序" enabled="true">
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <boolProp name="resetInterpreter">false</boolProp>
            <stringProp name="script"></stringProp>
          </BeanShellPostProcessor>
          <hashTree/>
          <JDBCPostProcessor guiclass="TestBeanGUI" testclass="JDBCPostProcessor" testname="JDBC 后置处理程序" enabled="true">
            <stringProp name="dataSource">JDBC Connection Configuration</stringProp>
            <stringProp name="query">select name from user</stringProp>
            <stringProp name="queryArguments">name</stringProp>
            <stringProp name="queryArgumentsTypes">name</stringProp>
            <stringProp name="queryTimeout">6000</stringProp>
            <stringProp name="queryType">Select Statement</stringProp>
            <stringProp name="resultSetHandler">Store as String</stringProp>
            <stringProp name="resultSetMaxRows">2</stringProp>
            <stringProp name="resultVariable">name</stringProp>
            <stringProp name="variableNames">name</stringProp>
          </JDBCPostProcessor>
          <hashTree/>
          <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="正则表达式提取器" enabled="true">
            <stringProp name="RegexExtractor.useHeaders">message</stringProp>
            <stringProp name="RegexExtractor.refname">zhengzemingcheng</stringProp>
            <stringProp name="RegexExtractor.regex">zhengzebiaodashi</stringProp>
            <stringProp name="RegexExtractor.template">moban</stringProp>
            <stringProp name="RegexExtractor.default"></stringProp>
            <stringProp name="RegexExtractor.match_number">7</stringProp>
          </RegexExtractor>
          <hashTree/>
          <ResponseAssertion guiclass="AssertionGui" testclass="ResponseAssertion" testname="响应断言" enabled="true">
            <collectionProp name="Asserion.test_strings"/>
            <stringProp name="Assertion.custom_message"></stringProp>
            <stringProp name="Assertion.test_field">Assertion.response_data</stringProp>
            <boolProp name="Assertion.assume_success">false</boolProp>
            <intProp name="Assertion.test_type">16</intProp>
          </ResponseAssertion>
          <hashTree/>
          <XPathExtractor guiclass="XPathExtractorGui" testclass="XPathExtractor" testname="XPath提取器" enabled="true">
            <stringProp name="TestPlan.comments">xxxdes</stringProp>
            <stringProp name="XPathExtractor.default">-11</stringProp>
            <stringProp name="XPathExtractor.refname">2312312</stringProp>
            <stringProp name="XPathExtractor.matchNumber">-1</stringProp>
            <stringProp name="XPathExtractor.xpathQuery">xpathquery</stringProp>
            <boolProp name="XPathExtractor.validate">true</boolProp>
            <boolProp name="XPathExtractor.tolerant">false</boolProp>
            <boolProp name="XPathExtractor.namespace">true</boolProp>
            <boolProp name="XPathExtractor.whitespace">true</boolProp>
            <boolProp name="XPathExtractor.download_dtds">true</boolProp>
          </XPathExtractor>
          <hashTree/>
          <XPath2Extractor guiclass="XPath2ExtractorGui" testclass="XPath2Extractor" testname="XPath2 Extractor" enabled="true">
            <stringProp name="TestPlan.comments">xpathquery注释</stringProp>
            <stringProp name="XPathExtractor2.default">-5</stringProp>
            <stringProp name="XPathExtractor2.refname">asaaa</stringProp>
            <stringProp name="XPathExtractor2.matchNumber">1</stringProp>
            <stringProp name="XPathExtractor2.xpathQuery">xpathquery</stringProp>
            <stringProp name="XPathExtractor2.namespaces"></stringProp>
            <stringProp name="Sample.scope">children</stringProp>
          </XPath2Extractor>
          <hashTree/>
        </hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="post-for-page5" enabled="true">
          <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{&quot;current&quot;:1,&quot;pageSize&quot;:18,&quot;sort&quot;:{},&quot;keyword&quot;:&quot;&quot;,&quot;combine&quot;:{},&quot;searchMode&quot;:&quot;AND&quot;,&quot;projectId&quot;:&quot;718255970852864&quot;,&quot;moduleIds&quot;:[],&quot;filter&quot;:{}}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain">*************</stringProp>
          <stringProp name="HTTPSampler.port">8081</stringProp>
          <stringProp name="HTTPSampler.protocol">http</stringProp>
          <stringProp name="HTTPSampler.contentEncoding"></stringProp>
          <stringProp name="HTTPSampler.path">/api/scenario/page2</stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">false</boolProp>
          <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
          <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
          <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
          <stringProp name="HTTPSampler.connect_timeout"></stringProp>
          <stringProp name="HTTPSampler.response_timeout"></stringProp>
        </HTTPSamplerProxy>
        <hashTree>
          <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP信息头管理器" enabled="true">
            <collectionProp name="HeaderManager.headers">
              <elementProp name="Accept" elementType="Header">
                <stringProp name="Header.name">Accept</stringProp>
                <stringProp name="Header.value">application/json, text/plain, */*</stringProp>
              </elementProp>
              <elementProp name="Accept-Encoding" elementType="Header">
                <stringProp name="Header.name">Accept-Encoding</stringProp>
                <stringProp name="Header.value">gzip, deflate</stringProp>
              </elementProp>
              <elementProp name="Accept-Language" elementType="Header">
                <stringProp name="Header.name">Accept-Language</stringProp>
                <stringProp name="Header.value">zh-CN</stringProp>
              </elementProp>
              <elementProp name="CSRF-TOKEN" elementType="Header">
                <stringProp name="Header.name">CSRF-TOKEN</stringProp>
                <stringProp name="Header.value">WOtvvhbURKiZae0Sdo8uOP/S0my8bcKKpKvsozjSzjfbjP92v4dZr9ncnu2aR8/7yaJXYvXvYn4PEvSkW96zDG+b7nHz0OddH6/xpDu4kIk=</stringProp>
              </elementProp>
              <elementProp name="Connection" elementType="Header">
                <stringProp name="Header.name">Connection</stringProp>
                <stringProp name="Header.value">keep-alive</stringProp>
              </elementProp>
              <elementProp name="Content-Length" elementType="Header">
                <stringProp name="Header.name">Content-Length</stringProp>
                <stringProp name="Header.value">139</stringProp>
              </elementProp>
              <elementProp name="Content-Type" elementType="Header">
                <stringProp name="Header.name">Content-Type</stringProp>
                <stringProp name="Header.value">application/json;charset=UTF-8</stringProp>
              </elementProp>
              <elementProp name="Host" elementType="Header">
                <stringProp name="Header.name">Host</stringProp>
                <stringProp name="Header.value">*************:8081</stringProp>
              </elementProp>
              <elementProp name="ORGANIZATION" elementType="Header">
                <stringProp name="Header.name">ORGANIZATION</stringProp>
                <stringProp name="Header.value">717345437786112</stringProp>
              </elementProp>
              <elementProp name="Origin" elementType="Header">
                <stringProp name="Header.name">Origin</stringProp>
                <stringProp name="Header.value">http://*************:8081</stringProp>
              </elementProp>
              <elementProp name="PROJECT" elementType="Header">
                <stringProp name="Header.name">PROJECT</stringProp>
                <stringProp name="Header.value">718255970852864</stringProp>
              </elementProp>
              <elementProp name="Referer" elementType="Header">
                <stringProp name="Header.name">Referer</stringProp>
                <stringProp name="Header.value">http://*************:8081/</stringProp>
              </elementProp>
              <elementProp name="User-Agent" elementType="Header">
                <stringProp name="Header.name">User-Agent</stringProp>
                <stringProp name="Header.value">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36</stringProp>
              </elementProp>
              <elementProp name="X-AUTH-TOKEN" elementType="Header">
                <stringProp name="Header.name">X-AUTH-TOKEN</stringProp>
                <stringProp name="Header.value">61afde41-15d2-47c1-b1d1-751a4bed99f9</stringProp>
              </elementProp>
            </collectionProp>
          </HeaderManager>
          <hashTree/>
          <JSR223PreProcessor guiclass="TestBeanGUI" testclass="JSR223PreProcessor" testname="JSR223 预处理程序" enabled="true">
            <stringProp name="cacheKey">true</stringProp>
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <stringProp name="script">log.info(&quot;sty ----&gt; no 1&quot;);</stringProp>
            <stringProp name="scriptLanguage">groovy</stringProp>
          </JSR223PreProcessor>
          <hashTree/>
          <ConstantTimer guiclass="ConstantTimerGui" testclass="ConstantTimer" testname="固定定时器" enabled="true">
            <stringProp name="ConstantTimer.delay">3300</stringProp>
          </ConstantTimer>
          <hashTree/>
          <JDBCDataSource guiclass="TestBeanGUI" testclass="JDBCDataSource" testname="JDBC Connection Configuration" enabled="true">
            <boolProp name="autocommit">true</boolProp>
            <stringProp name="checkQuery"></stringProp>
            <stringProp name="connectionAge">5000</stringProp>
            <stringProp name="connectionProperties"></stringProp>
            <stringProp name="dataSource">18数据库</stringProp>
            <stringProp name="dbUrl">*****************************************************************************************************************************************************************************************************</stringProp>
            <stringProp name="driver">com.mysql.jdbc.Driver</stringProp>
            <stringProp name="initQuery"></stringProp>
            <boolProp name="keepAlive">true</boolProp>
            <stringProp name="password">Password123@mysql</stringProp>
            <stringProp name="poolMax">0</stringProp>
            <boolProp name="preinit">false</boolProp>
            <stringProp name="timeout">10000</stringProp>
            <stringProp name="transactionIsolation">DEFAULT</stringProp>
            <stringProp name="trimInterval">60000</stringProp>
            <stringProp name="username">root</stringProp>
          </JDBCDataSource>
          <hashTree/>
          <JDBCPreProcessor guiclass="TestBeanGUI" testclass="JDBCPreProcessor" testname="JDBC 预处理程序" enabled="true">
            <stringProp name="dataSource">id</stringProp>
            <stringProp name="query">select id,name from user</stringProp>
            <stringProp name="queryArguments">id</stringProp>
            <stringProp name="queryArgumentsTypes">string</stringProp>
            <stringProp name="queryTimeout">1000</stringProp>
            <stringProp name="queryType">Select Statement</stringProp>
            <stringProp name="resultSetHandler">Store as String</stringProp>
            <stringProp name="resultSetMaxRows">20</stringProp>
            <stringProp name="resultVariable">username</stringProp>
            <stringProp name="variableNames">userid</stringProp>
          </JDBCPreProcessor>
          <hashTree/>
          <BeanShellPreProcessor guiclass="TestBeanGUI" testclass="BeanShellPreProcessor" testname="BeanShell 预处理程序" enabled="true">
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <boolProp name="resetInterpreter">false</boolProp>
            <stringProp name="script">log.info(&quot;print jdbc&quot;);
</stringProp>
          </BeanShellPreProcessor>
          <hashTree/>
          <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="JSON提取器" enabled="true">
            <stringProp name="TestPlan.comments">aaaaaaa</stringProp>
            <stringProp name="JSONPostProcessor.referenceNames">namesofcreated</stringProp>
            <stringProp name="JSONPostProcessor.jsonPathExprs">jsonpathexpre</stringProp>
            <stringProp name="JSONPostProcessor.match_numbers">0</stringProp>
            <boolProp name="JSONPostProcessor.compute_concat">true</boolProp>
            <stringProp name="JSONPostProcessor.defaultValues">zzzzz</stringProp>
            <stringProp name="Sample.scope">all</stringProp>
          </JSONPostProcessor>
          <hashTree/>
          <JSR223PostProcessor guiclass="TestBeanGUI" testclass="JSR223PostProcessor" testname="JSR223 后置处理程序" enabled="true">
            <stringProp name="cacheKey">true</stringProp>
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <stringProp name="script">log.info(&quot;sty &lt;---- no 1&quot;);</stringProp>
            <stringProp name="scriptLanguage">groovy</stringProp>
          </JSR223PostProcessor>
          <hashTree/>
          <BeanShellPostProcessor guiclass="TestBeanGUI" testclass="BeanShellPostProcessor" testname="BeanShell 后置处理程序" enabled="true">
            <stringProp name="filename"></stringProp>
            <stringProp name="parameters"></stringProp>
            <boolProp name="resetInterpreter">false</boolProp>
            <stringProp name="script"></stringProp>
          </BeanShellPostProcessor>
          <hashTree/>
          <JDBCPostProcessor guiclass="TestBeanGUI" testclass="JDBCPostProcessor" testname="JDBC 后置处理程序" enabled="true">
            <stringProp name="dataSource">JDBC Connection Configuration</stringProp>
            <stringProp name="query">select name from user</stringProp>
            <stringProp name="queryArguments">name</stringProp>
            <stringProp name="queryArgumentsTypes">name</stringProp>
            <stringProp name="queryTimeout">6000</stringProp>
            <stringProp name="queryType">Select Statement</stringProp>
            <stringProp name="resultSetHandler">Store as String</stringProp>
            <stringProp name="resultSetMaxRows">2</stringProp>
            <stringProp name="resultVariable">name</stringProp>
            <stringProp name="variableNames">name</stringProp>
          </JDBCPostProcessor>
          <hashTree/>
          <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="正则表达式提取器" enabled="true">
            <stringProp name="RegexExtractor.useHeaders">message</stringProp>
            <stringProp name="RegexExtractor.refname">zhengzemingcheng</stringProp>
            <stringProp name="RegexExtractor.regex">zhengzebiaodashi</stringProp>
            <stringProp name="RegexExtractor.template">moban</stringProp>
            <stringProp name="RegexExtractor.default"></stringProp>
            <stringProp name="RegexExtractor.match_number">7</stringProp>
          </RegexExtractor>
          <hashTree/>
          <ResponseAssertion guiclass="AssertionGui" testclass="ResponseAssertion" testname="响应断言" enabled="true">
            <collectionProp name="Asserion.test_strings"/>
            <stringProp name="Assertion.custom_message"></stringProp>
            <stringProp name="Assertion.test_field">Assertion.response_data</stringProp>
            <boolProp name="Assertion.assume_success">false</boolProp>
            <intProp name="Assertion.test_type">16</intProp>
          </ResponseAssertion>
          <hashTree/>
          <XPathExtractor guiclass="XPathExtractorGui" testclass="XPathExtractor" testname="XPath提取器" enabled="true">
            <stringProp name="TestPlan.comments">xxxdes</stringProp>
            <stringProp name="XPathExtractor.default">-11</stringProp>
            <stringProp name="XPathExtractor.refname">2312312</stringProp>
            <stringProp name="XPathExtractor.matchNumber">-1</stringProp>
            <stringProp name="XPathExtractor.xpathQuery">xpathquery</stringProp>
            <boolProp name="XPathExtractor.validate">true</boolProp>
            <boolProp name="XPathExtractor.tolerant">false</boolProp>
            <boolProp name="XPathExtractor.namespace">true</boolProp>
            <boolProp name="XPathExtractor.whitespace">true</boolProp>
            <boolProp name="XPathExtractor.download_dtds">true</boolProp>
          </XPathExtractor>
          <hashTree/>
          <XPath2Extractor guiclass="XPath2ExtractorGui" testclass="XPath2Extractor" testname="XPath2 Extractor" enabled="true">
            <stringProp name="TestPlan.comments">xpathquery注释</stringProp>
            <stringProp name="XPathExtractor2.default">-5</stringProp>
            <stringProp name="XPathExtractor2.refname">asaaa</stringProp>
            <stringProp name="XPathExtractor2.matchNumber">1</stringProp>
            <stringProp name="XPathExtractor2.xpathQuery">xpathquery</stringProp>
            <stringProp name="XPathExtractor2.namespaces"></stringProp>
            <stringProp name="Sample.scope">children</stringProp>
          </XPath2Extractor>
          <hashTree/>
        </hashTree>
      </hashTree>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
