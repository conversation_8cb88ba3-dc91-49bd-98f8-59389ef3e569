{"log": {"version": "1.2", "creator": {"name": "WebInspector", "version": "537.36"}, "pages": [], "entries": [{"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "", "scriptId": "9", "url": "http://*************:8081/assets/index-33SgBhcr.js", "lineNumber": 1, "columnNumber": 597486}, {"functionName": "xhr", "scriptId": "9", "url": "http://*************:8081/assets/index-33SgBhcr.js", "lineNumber": 1, "columnNumber": 595339}, {"functionName": "dispatchRequest", "scriptId": "9", "url": "http://*************:8081/assets/index-33SgBhcr.js", "lineNumber": 1, "columnNumber": 603441}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "_request", "scriptId": "9", "url": "http://*************:8081/assets/index-33SgBhcr.js", "lineNumber": 1, "columnNumber": 606591}, {"functionName": "request", "scriptId": "9", "url": "http://*************:8081/assets/index-33SgBhcr.js", "lineNumber": 1, "columnNumber": 605135}, {"functionName": "", "scriptId": "9", "url": "http://*************:8081/assets/index-33SgBhcr.js", "lineNumber": 1, "columnNumber": 570981}, {"functionName": "", "scriptId": "9", "url": "http://*************:8081/assets/index-33SgBhcr.js", "lineNumber": 19, "columnNumber": 40121}, {"functionName": "uploadFile", "scriptId": "9", "url": "http://*************:8081/assets/index-33SgBhcr.js", "lineNumber": 19, "columnNumber": 40082}, {"functionName": "x", "scriptId": "160", "url": "http://*************:8081/assets/fileManagement-BwbfbuXX.js", "lineNumber": 0, "columnNumber": 1108}, {"functionName": "uploadFileFromQueue", "scriptId": "163", "url": "http://*************:8081/assets/fileList-BCu6BLjh.js", "lineNumber": 0, "columnNumber": 3648}, {"functionName": "", "scriptId": "11", "url": "http://*************:8081/assets/vue-CKjJUztO.js", "lineNumber": 48, "columnNumber": 2244}, {"functionName": "startUpload", "scriptId": "163", "url": "http://*************:8081/assets/fileList-BCu6BLjh.js", "lineNumber": 0, "columnNumber": 4116}, {"functionName": "", "scriptId": "11", "url": "http://*************:8081/assets/vue-CKjJUztO.js", "lineNumber": 48, "columnNumber": 2244}, {"functionName": "Q", "scriptId": "163", "url": "http://*************:8081/assets/fileList-BCu6BLjh.js", "lineNumber": 0, "columnNumber": 6637}, {"functionName": "cl", "scriptId": "158", "url": "http://*************:8081/assets/index-DVkmz8YF.js", "lineNumber": 0, "columnNumber": 34186}, {"functionName": "jt", "scriptId": "11", "url": "http://*************:8081/assets/vue-CKjJUztO.js", "lineNumber": 12, "columnNumber": 2794}, {"functionName": "St", "scriptId": "11", "url": "http://*************:8081/assets/vue-CKjJUztO.js", "lineNumber": 12, "columnNumber": 2865}, {"functionName": "xm", "scriptId": "11", "url": "http://*************:8081/assets/vue-CKjJUztO.js", "lineNumber": 12, "columnNumber": 5396}, {"functionName": "handleClick", "scriptId": "12", "url": "http://*************:8081/assets/arco-g73YkF1H.js", "lineNumber": 0, "columnNumber": 51176}, {"functionName": "e.href.B.onClick.t.<computed>.t.<computed>", "scriptId": "12", "url": "http://*************:8081/assets/arco-g73YkF1H.js", "lineNumber": 0, "columnNumber": 51921}, {"functionName": "jt", "scriptId": "11", "url": "http://*************:8081/assets/vue-CKjJUztO.js", "lineNumber": 12, "columnNumber": 2794}, {"functionName": "St", "scriptId": "11", "url": "http://*************:8081/assets/vue-CKjJUztO.js", "lineNumber": 12, "columnNumber": 2865}, {"functionName": "n", "scriptId": "11", "url": "http://*************:8081/assets/vue-CKjJUztO.js", "lineNumber": 16, "columnNumber": 8410}]}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "1124737", "request": {"method": "POST", "url": "http://*************:8081/project/file/upload", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "zh-CN"}, {"name": "CSRF-TOKEN", "value": "WOtvvhbURKiZae0Sdo8uOD28+DK1/M+gXEdZkuu8iUBub/ak4c06ys1okPHqSfEKUiDI4HjdnqBddZeYuF2g0p+oRDeGoUHUtrM1wiGylvQ="}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "54729"}, {"name": "Content-Type", "value": "multipart/form-data; boundary=----WebKitFormBoundaryNkMIhRd5puIb9Jo6"}, {"name": "Host", "value": "*************:8081"}, {"name": "ORGANIZATION", "value": "717345437786112"}, {"name": "Origin", "value": "http://*************:8081"}, {"name": "PROJECT", "value": "997050905108480"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://*************:8081/"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-AUTH-TOKEN", "value": "61afde41-15d2-47c1-b1d1-751a4bed99f9"}], "queryString": [], "cookies": [], "headersSize": 726, "bodySize": 406, "postData": {"mimeType": "multipart/form-data; boundary=----WebKitFormBoundaryNkMIhRd5puIb9Jo6", "text": "------WebKitFormBoundaryNkMIhRd5puIb9Jo6\r\nContent-Disposition: form-data; name=\"file\"; filename=\"IMG_5695.PNG\"\r\nContent-Type: image/png\r\n\r\n\r\n------WebKitFormBoundaryNkMIhRd5puIb9Jo6\r\nContent-Disposition: form-data; name=\"request\"; filename=\"blob\"\r\nContent-Type: application/json;charset=utf-8\r\n\r\n{\"projectId\":\"997050905108480\",\"moduleId\":\"root\",\"enable\":false}\r\n------WebKitFormBoundaryNkMIhRd5puIb9Jo6--\r\n", "params": [{"name": "file", "value": "(binary)"}, {"name": "request", "value": "(binary)"}]}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Length", "value": "77"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Date", "value": "Thu, 08 Aug 2024 03:33:47 GMT"}, {"name": "Vary", "value": "Accept-Encoding"}], "cookies": [], "content": {"size": 77, "mimeType": "application/json", "compression": 0, "text": "{\"code\":100200,\"message\":null,\"messageDetail\":null,\"data\":\"2199504316596224\"}"}, "redirectURL": "", "headersSize": 131, "bodySize": 77, "_transferSize": 208, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2024-08-08T03:33:47.873Z", "time": 691.4930000063777, "timings": {"blocked": 1.7159999903719871, "dns": 0.17, "ssl": -1, "connect": 0.701, "send": 1.067, "wait": 687.6599999917876, "receive": 0.17900002421811223, "_blocked_queueing": 1.5619999903719872, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}]}}