{"log": {"version": "1.2", "creator": {"name": "WebInspector", "version": "537.36"}, "pages": [], "entries": [{"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 145648}, {"functionName": "xhr", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 143691}, {"functionName": "Qt", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 150609}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "_request", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 153327}, {"functionName": "request", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 151983}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 121631}, {"functionName": "V", "scriptId": "25", "url": "http://************:8081/js/setting-app.31dcaa0a.js", "lineNumber": 0, "columnNumber": 361292}, {"functionName": "c", "scriptId": "47", "url": "http://************:8081/js/setting-8711.e5990e2c.js", "lineNumber": 0, "columnNumber": 19924}, {"functionName": "search", "scriptId": "47", "url": "http://************:8081/js/setting-8711.e5990e2c.js", "lineNumber": 0, "columnNumber": 16984}, {"functionName": "mounted", "scriptId": "47", "url": "http://************:8081/js/setting-8711.e5990e2c.js", "lineNumber": 0, "columnNumber": 18346}, {"functionName": "zn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 24950}, {"functionName": "zi", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 33922}, {"functionName": "insert", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 37374}, {"functionName": "A", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 58002}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 59327}, {"functionName": "Pi.e._update", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 31510}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 32273}, {"functionName": "e.get", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 28492}, {"functionName": "e.run", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 29229}, {"functionName": "Qi", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 34489}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 26031}, {"functionName": "Yn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 25426}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "Hn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 25517}, {"functionName": "Qn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 26096}, {"functionName": "ir", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 35002}, {"functionName": "e.update", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 29165}, {"functionName": "e.notify", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 6804}, {"functionName": "set", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 8662}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 56623}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 56594}, {"functionName": "et.updateRoute", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51463}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 50046}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51328}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47365}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "Ge", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47411}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51259}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47365}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47393}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51170}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47670}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 48404}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47847}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 48039}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 48015}, {"functionName": "<PERSON>", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47965}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47474}, {"functionName": "f", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 50924}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47374}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47393}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51170}, {"functionName": "eval", "scriptId": "25", "url": "http://************:8081/js/setting-app.31dcaa0a.js", "lineNumber": 0, "columnNumber": 400894}, {"functionName": "f", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 50924}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47374}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "Ge", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47411}, {"functionName": "et.confirmTransition", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51194}, {"functionName": "et.transitionTo", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 50012}, {"functionName": "t.push", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 53641}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 57086}, {"functionName": "_t.push", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 57049}, {"functionName": "f.Ay.push", "scriptId": "25", "url": "http://************:8081/js/setting-app.31dcaa0a.js", "lineNumber": 0, "columnNumber": 389825}, {"functionName": "routeToItem", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 44, "columnNumber": 110768}, {"functionName": "handleItemClick", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 44, "columnNumber": 110374}, {"functionName": "zn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 24937}, {"functionName": "Ai.e.$emit", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 30964}, {"functionName": "dispatch", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 44, "columnNumber": 620936}, {"functionName": "handleClick", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 44, "columnNumber": 120315}, {"functionName": "zn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 24937}, {"functionName": "n", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 14734}, {"functionName": "Pa.o._wrapper", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 62419}]}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "1187519", "request": {"method": "POST", "url": "http://************:8081/setting/quota/list/workspace/1/10", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "zh-CN"}, {"name": "CSRF-TOKEN", "value": "442WhMRQfh9N2uVKgj6YOWgY6JmGJk+MkyKq0N6UMrxgeyaiiAtqTAPNjI4bu6miUKuGeevt23GOhSuvD8agAXaVgK/j9LbBH0aqfaXc2KU="}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "2"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Host", "value": "************:8081"}, {"name": "Origin", "value": "http://************:8081"}, {"name": "PROJECT", "value": "05a5c1ea-d1e2-4645-956f-6300d4becf00"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://************:8081/"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "WORKSPACE", "value": "048b143c-e071-424f-b5fe-3ca89fb93c13"}, {"name": "X-AUTH-TOKEN", "value": "616e19c0-3b10-4391-81a9-401e6f6f1b34"}], "queryString": [], "cookies": [], "headersSize": 720, "bodySize": 2, "postData": {"mimeType": "application/json", "text": "{}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Date", "value": "Thu, 08 Aug 2024 08:05:45 GMT"}, {"name": "Vary", "value": "Origin"}, {"name": "Vary", "value": "Access-Control-Request-Method"}, {"name": "Vary", "value": "Access-Control-Request-Headers"}, {"name": "Vary", "value": "Accept-Encoding"}, {"name": "transfer-encoding", "value": "chunked"}], "cookies": [], "content": {"size": 4081, "mimeType": "application/json", "compression": 3103, "text": "{\"success\":true,\"message\":null,\"data\":{\"listObject\":[{\"id\":\"87c67f3c-cd5d-4b88-b7db-bf95f6e8553b\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"c239e34f-faf3-43d1-8294-4584731c199e\",\"useDefault\":true,\"updateTime\":1721784547632,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"工作空间_ut4mjnvx\",\"projectName\":null,\"projectUsed\":0},{\"id\":\"121bbe7d-bc40-4a9b-923f-d1cfa41003cd\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"d2658c95-7d7a-46f9-8928-e77e7f9d2b4e\",\"useDefault\":true,\"updateTime\":1721784542058,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"工作空间_guPvyG90\",\"projectName\":null,\"projectUsed\":0},{\"id\":\"2617aa3e-b887-49d8-8c73-e12aca1f8a82\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"5ecb39c8-7789-4882-b73d-e9613f310aa7\",\"useDefault\":true,\"updateTime\":1721784531076,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"工作空间_xan83ZB7\",\"projectName\":null,\"projectUsed\":0},{\"id\":\"8a9a28dd-5adf-4543-9bdc-af28907aba26\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"2729e83e-984a-4a50-ae9c-85b5f81346b0\",\"useDefault\":true,\"updateTime\":1721784526531,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"工作空间_zfFEit0M\",\"projectName\":null,\"projectUsed\":0},{\"id\":\"3cc53e8c-990c-4ffb-aa8c-8a9302969012\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"c6b164bb-3966-4c10-8ac1-2f451cab339f\",\"useDefault\":true,\"updateTime\":1721784517974,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"工作空间_P0qyCMeh\",\"projectName\":null,\"projectUsed\":0},{\"id\":\"bf75cfac-3f7e-4587-a5bc-de1014825597\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"97679d16-4f30-4df5-be45-f91379aecf19\",\"useDefault\":true,\"updateTime\":1721783923519,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"工作空间_tQAI6tc4\",\"projectName\":null,\"projectUsed\":2},{\"id\":\"5510344a-d2cc-4587-a55e-2917f6514f7d\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"431e813d-5de5-4c1d-affe-34f8f15a1108\",\"useDefault\":true,\"updateTime\":1721783087058,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"工作空间_2pA85Gpv\",\"projectName\":null,\"projectUsed\":1},{\"id\":\"46872b1b-372b-4de7-8f0b-53d065893f08\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"b1da8acc-cb55-450c-9189-a36e143cfa57\",\"useDefault\":true,\"updateTime\":1720175342992,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"guoxiaoyong\",\"projectName\":null,\"projectUsed\":0},{\"id\":\"25d6051b-116a-4561-93b4-6bcd20b4803d\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"7caa64ef-1f1b-400c-964c-f52587856ccb\",\"useDefault\":true,\"updateTime\":1720075574958,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":0.17,\"moduleSetting\":null,\"workspaceName\":\"7.4工作空间-yc\",\"projectName\":null,\"projectUsed\":1},{\"id\":\"2f996458-fc84-42e7-813b-d802603acb2a\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"25140054-8520-4bc7-a231-76366f56cf78\",\"useDefault\":true,\"updateTime\":1719363182724,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"工作空间_nBt3I17E\",\"projectName\":null,\"projectUsed\":0}],\"itemCount\":67,\"pageCount\":7}}"}, "redirectURL": "", "headersSize": 252, "bodySize": 978, "_transferSize": 1230, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "************", "startedDateTime": "2024-08-08T08:06:14.400Z", "time": 154.3320000055246, "timings": {"blocked": 0.6249999846555292, "dns": -1, "ssl": -1, "connect": -1, "send": 0.05700000000000002, "wait": 153.4819999930989, "receive": 0.16800002777017653, "_blocked_queueing": 0.44899998465552926, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 145648}, {"functionName": "xhr", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 143691}, {"functionName": "Qt", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 150609}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "_request", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 153327}, {"functionName": "request", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 151983}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 121631}, {"functionName": "T", "scriptId": "25", "url": "http://************:8081/js/setting-app.31dcaa0a.js", "lineNumber": 0, "columnNumber": 361243}, {"functionName": "i", "scriptId": "47", "url": "http://************:8081/js/setting-8711.e5990e2c.js", "lineNumber": 0, "columnNumber": 19733}, {"functionName": "getResourcePool", "scriptId": "49", "url": "http://************:8081/js/setting-3617.3c6437cc.js", "lineNumber": 0, "columnNumber": 2049}, {"functionName": "mounted", "scriptId": "49", "url": "http://************:8081/js/setting-3617.3c6437cc.js", "lineNumber": 0, "columnNumber": 2522}, {"functionName": "zn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 24950}, {"functionName": "zi", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 33922}, {"functionName": "insert", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 37374}, {"functionName": "A", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 58002}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 59327}, {"functionName": "Pi.e._update", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 31510}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 32273}, {"functionName": "e.get", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 28492}, {"functionName": "e.run", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 29229}, {"functionName": "Qi", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 34489}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 26031}, {"functionName": "Yn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 25426}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "Hn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 25517}, {"functionName": "Qn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 26096}, {"functionName": "ir", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 35002}, {"functionName": "e.update", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 29165}, {"functionName": "e.notify", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 6804}, {"functionName": "set", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 8662}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 56623}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 56594}, {"functionName": "et.updateRoute", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51463}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 50046}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51328}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47365}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "Ge", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47411}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51259}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47365}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47393}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51170}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47670}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 48404}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47847}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 48039}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 48015}, {"functionName": "<PERSON>", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47965}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47474}, {"functionName": "f", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 50924}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47374}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47393}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51170}, {"functionName": "eval", "scriptId": "25", "url": "http://************:8081/js/setting-app.31dcaa0a.js", "lineNumber": 0, "columnNumber": 400894}, {"functionName": "f", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 50924}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47374}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "Ge", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47411}, {"functionName": "et.confirmTransition", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51194}, {"functionName": "et.transitionTo", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 50012}, {"functionName": "t.push", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 53641}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 57086}, {"functionName": "_t.push", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 57049}, {"functionName": "f.Ay.push", "scriptId": "25", "url": "http://************:8081/js/setting-app.31dcaa0a.js", "lineNumber": 0, "columnNumber": 389825}, {"functionName": "routeToItem", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 44, "columnNumber": 110768}, {"functionName": "handleItemClick", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 44, "columnNumber": 110374}, {"functionName": "zn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 24937}, {"functionName": "Ai.e.$emit", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 30964}, {"functionName": "dispatch", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 44, "columnNumber": 620936}, {"functionName": "handleClick", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 44, "columnNumber": 120315}, {"functionName": "zn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 24937}, {"functionName": "n", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 14734}, {"functionName": "Pa.o._wrapper", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 62419}]}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "1187656", "request": {"method": "GET", "url": "http://************:8081/setting/testresourcepool/list/all/valid", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "zh-CN"}, {"name": "CSRF-TOKEN", "value": "442WhMRQfh9N2uVKgj6YOWgY6JmGJk+MkyKq0N6UMrxgeyaiiAtqTAPNjI4bu6miUKuGeevt23GOhSuvD8agAXaVgK/j9LbBH0aqfaXc2KU="}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "************:8081"}, {"name": "PROJECT", "value": "05a5c1ea-d1e2-4645-956f-6300d4becf00"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://************:8081/"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "WORKSPACE", "value": "048b143c-e071-424f-b5fe-3ca89fb93c13"}, {"name": "X-AUTH-TOKEN", "value": "616e19c0-3b10-4391-81a9-401e6f6f1b34"}], "queryString": [], "cookies": [], "headersSize": 640, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Date", "value": "Thu, 08 Aug 2024 08:05:45 GMT"}, {"name": "Vary", "value": "Origin"}, {"name": "Vary", "value": "Access-Control-Request-Method"}, {"name": "Vary", "value": "Access-Control-Request-Headers"}, {"name": "Vary", "value": "Accept-Encoding"}, {"name": "transfer-encoding", "value": "chunked"}], "cookies": [], "content": {"size": 10961, "mimeType": "application/json", "compression": 8105, "text": "{\"success\":true,\"message\":null,\"data\":[{\"id\":\"8c17bdc2-1817-11eb-92cf-0242ac120004\",\"name\":\"LOCAL\",\"type\":\"NODE\",\"description\":\"系统默认创建的本地资源池\",\"status\":\"VALID\",\"createTime\":1603777497000,\"updateTime\":1721965035953,\"image\":\"registry.cn-qingdao.aliyuncs.com/metersphere/jmeter-master:5.5-ms12-jdk17\",\"heap\":\"-Xms4g -Xmx4g -XX:MaxMetaspaceSize=2048m\",\"gcAlgo\":null,\"createUser\":null,\"api\":true,\"performance\":true,\"backendListener\":false,\"resources\":[{\"id\":\"8c17fc37-1817-11eb-92cf-0242ac120004\",\"testResourcePoolId\":\"8c17bdc2-1817-11eb-92cf-0242ac120004\",\"status\":\"VALID\",\"createTime\":1721965035956,\"updateTime\":1721965035956,\"configuration\":\"{\\\"port\\\":8082,\\\"maxConcurrency\\\":5000,\\\"ip\\\":\\\"ms-node-controller\\\",\\\"id\\\":\\\"8c17fc37-1817-11eb-92cf-0242ac120004\\\",\\\"monitorPort\\\":9100,\\\"deployType\\\":\\\"DaemonSet\\\",\\\"deployName\\\":\\\"ms-node-controller\\\",\\\"enable\\\":true}\"}]},{\"id\":\"bafa05bb-d4f6-4eaf-a8de-0d895b80ed94\",\"name\":\"副老师的资源池\",\"type\":\"NODE\",\"description\":null,\"status\":\"VALID\",\"createTime\":1721730696108,\"updateTime\":1721730709537,\"image\":\"registry.cn-qingdao.aliyuncs.com/metersphere/jmeter-master:5.5-ms12-jdk17\",\"heap\":null,\"gcAlgo\":null,\"createUser\":null,\"api\":true,\"performance\":true,\"backendListener\":false,\"resources\":[{\"id\":\"5d45cbd9-78aa-4488-b619-a1d3d5057cdd\",\"testResourcePoolId\":\"bafa05bb-d4f6-4eaf-a8de-0d895b80ed94\",\"status\":\"VALID\",\"createTime\":1721730709543,\"updateTime\":1721730709543,\"configuration\":\"{\\\"ip\\\":\\\"*************\\\",\\\"port\\\":8082,\\\"monitorPort\\\":9100,\\\"maxConcurrency\\\":100,\\\"id\\\":\\\"5d45cbd9-78aa-4488-b619-a1d3d5057cdd\\\",\\\"deployType\\\":\\\"DaemonSet\\\",\\\"deployName\\\":\\\"ms-node-controller\\\"}\"}]},{\"id\":\"28947e4b-b2b5-4379-90d2-a6cf670b506e\",\"name\":\"66资源池\",\"type\":\"NODE\",\"description\":null,\"status\":\"VALID\",\"createTime\":1689760838776,\"updateTime\":1721369328900,\"image\":\"registry.cn-qingdao.aliyuncs.com/metersphere/jmeter-master:5.5-ms12-jdk17\",\"heap\":null,\"gcAlgo\":null,\"createUser\":null,\"api\":true,\"performance\":true,\"backendListener\":false,\"resources\":[{\"id\":\"9ffc1be4-bb29-4114-8648-0376a171f916\",\"testResourcePoolId\":\"28947e4b-b2b5-4379-90d2-a6cf670b506e\",\"status\":\"VALID\",\"createTime\":1721369328936,\"updateTime\":1721369328936,\"configuration\":\"{\\\"ip\\\":\\\"************\\\",\\\"port\\\":8082,\\\"monitorPort\\\":435,\\\"maxConcurrency\\\":5000,\\\"id\\\":\\\"9ffc1be4-bb29-4114-8648-0376a171f916\\\",\\\"deployType\\\":\\\"DaemonSet\\\",\\\"deployName\\\":\\\"ms-node-controller\\\"}\"}]},{\"id\":\"132c515d-5d39-4c21-af1e-40c6b748d366\",\"name\":\"K8s-200\",\"type\":\"K8S\",\"description\":null,\"status\":\"VALID\",\"createTime\":1690536571532,\"updateTime\":1720259779615,\"image\":\"registry.cn-qingdao.aliyuncs.com/metersphere/jmeter-master:5.5-ms9-jdk17\",\"heap\":null,\"gcAlgo\":null,\"createUser\":null,\"api\":true,\"performance\":true,\"backendListener\":false,\"resources\":[{\"id\":\"11cdfe06-0f34-4589-982d-04f6462f9e83\",\"testResourcePoolId\":\"132c515d-5d39-4c21-af1e-40c6b748d366\",\"status\":\"VALID\",\"createTime\":*************,\"updateTime\":*************,\"configuration\":\"{\\\"masterUrl\\\":\\\"https://*************:6443\\\",\\\"token\\\":\\\"**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\\\",\\\"namespace\\\":\\\"youly\\\",\\\"podThreadLimit\\\":5000,\\\"deployType\\\":\\\"DaemonSet\\\",\\\"deployName\\\":\\\"ms-node-controller\\\",\\\"maxConcurrency\\\":100,\\\"enable\\\":false,\\\"id\\\":\\\"11cdfe06-0f34-4589-982d-04f6462f9e83\\\",\\\"monitorPort\\\":\\\"9100\\\",\\\"jobTemplate\\\":\\\"apiVersion: batch/v1\\\\nkind: Job\\\\nmetadata:\\\\n  labels:\\\\n    test-id: ${TEST_ID}\\\\n  name: ${JOB_NAME}\\\\nspec:\\\\n  parallelism: 1\\\\n  template:\\\\n    metadata:\\\\n      labels:\\\\n        test-id: ${TEST_ID}\\\\n    spec:\\\\n      containers:\\\\n        - command:\\\\n            - sh\\\\n            - -c\\\\n            - /run-test.sh\\\\n          env:\\\\n            - name: START_TIME\\\\n              value: \\\\\\\"${START_TIME}\\\\\\\"\\\\n            - name: GRANULARITY\\\\n              value: \\\\\\\"${GRANULARITY}\\\\\\\"\\\\n            - name: JMETER_REPORTS_TOPIC\\\\n              value: ${JMETER_REPORTS_TOPIC}\\\\n            - name: METERSPHERE_URL\\\\n              value: ${METERSPHERE_URL}\\\\n            - name: RESOURCE_ID\\\\n              value: ${RESOURCE_ID}\\\\n            - name: BACKEND_LISTENER\\\\n              value: \\\\\\\"${BACKEND_LISTENER}\\\\\\\"\\\\n            - name: BOOTSTRAP_SERVERS\\\\n              value: ${BOOTSTRAP_SERVERS}\\\\n            - name: RATIO\\\\n              value: \\\\\\\"${RATIO}\\\\\\\"\\\\n            - name: TEST_ID\\\\n              value: ${TEST_ID}\\\\n            - name: THREAD_NUM\\\\n              value: \\\\\\\"${THREAD_NUM}\\\\\\\"\\\\n            - name: HEAP\\\\n              value: ${HEAP}\\\\n            - name: REPORT_ID\\\\n              value: ${REPORT_ID}\\\\n            - name: RESOURCE_INDEX\\\\n              value: \\\\\\\"${RESOURCE_INDEX}\\\\\\\"\\\\n            - name: LOG_TOPIC\\\\n              value: ${LOG_TOPIC}\\\\n            - name: GC_ALGO\\\\n              value: ${GC_ALGO}\\\\n          image: ${JMETER_IMAGE}\\\\n          imagePullPolicy: IfNotPresent\\\\n          name: jmeter\\\\n          ports:\\\\n            - containerPort: 60000\\\\n              protocol: TCP\\\\n          volumeMounts:\\\\n            - mountPath: /test\\\\n              name: test-files\\\\n            - mountPath: /jmeter-log\\\\n              name: log-files\\\\n      restartPolicy: Never\\\\n      volumes:\\\\n        - emptyDir: {}\\\\n          name: test-files\\\\n        - emptyDir: {}\\\\n          name: log-files\\\\n\\\"}\"}]},{\"id\":\"cdbd4079-3b9a-4182-8d66-1cd24b66de68\",\"name\":\"k8s-20\",\"type\":\"K8S\",\"description\":null,\"status\":\"VALID\",\"createTime\":1720259252623,\"updateTime\":1720259252623,\"image\":null,\"heap\":null,\"gcAlgo\":null,\"createUser\":null,\"api\":true,\"performance\":true,\"backendListener\":false,\"resources\":[{\"id\":\"c73882d8-a063-4ae3-84ae-bada7f3e4aeb\",\"testResourcePoolId\":\"cdbd4079-3b9a-4182-8d66-1cd24b66de68\",\"status\":\"VALID\",\"createTime\":*************,\"updateTime\":*************,\"configuration\":\"{\\\"masterUrl\\\":\\\"https://*************:6443\\\",\\\"token\\\":\\\"**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\\\",\\\"namespace\\\":\\\"youly\\\",\\\"podThreadLimit\\\":5000,\\\"deployType\\\":\\\"DaemonSet\\\",\\\"deployName\\\":\\\"ms-node-controller\\\",\\\"maxConcurrency\\\":100}\"}]},{\"id\":\"a360b679-88c6-4117-95dd-e4ea886b1b7d\",\"name\":\"test-wxg\",\"type\":\"NODE\",\"description\":null,\"status\":\"VALID\",\"createTime\":*************,\"updateTime\":*************,\"image\":null,\"heap\":null,\"gcAlgo\":null,\"createUser\":null,\"api\":true,\"performance\":true,\"backendListener\":true,\"resources\":[{\"id\":\"7fb67a0a-1394-48cf-a0e6-43b2e05bead2\",\"testResourcePoolId\":\"a360b679-88c6-4117-95dd-e4ea886b1b7d\",\"status\":\"VALID\",\"createTime\":1717484452330,\"updateTime\":1717484452330,\"configuration\":\"{\\\"ip\\\":\\\"127.0.0.1\\\",\\\"port\\\":8082,\\\"monitorPort\\\":9100,\\\"maxConcurrency\\\":100}\"}]},{\"id\":\"0b9f70e4-f159-4f29-ad1a-d3dd6f0fd5cd\",\"name\":\"200.4\",\"type\":\"NODE\",\"description\":null,\"status\":\"VALID\",\"createTime\":1689921113954,\"updateTime\":1717483467267,\"image\":\"registry.cn-qingdao.aliyuncs.com/metersphere/jmeter-master:5.5-ms8-jdk17\",\"heap\":null,\"gcAlgo\":null,\"createUser\":null,\"api\":true,\"performance\":true,\"backendListener\":false,\"resources\":[{\"id\":\"54057a38-f3fe-4235-adc2-f7bff69b10a0\",\"testResourcePoolId\":\"0b9f70e4-f159-4f29-ad1a-d3dd6f0fd5cd\",\"status\":\"VALID\",\"createTime\":1717483467295,\"updateTime\":1717483467295,\"configuration\":\"{\\\"ip\\\":\\\"************\\\",\\\"port\\\":8082,\\\"monitorPort\\\":4523,\\\"maxConcurrency\\\":100,\\\"id\\\":\\\"54057a38-f3fe-4235-adc2-f7bff69b10a0\\\",\\\"deployType\\\":\\\"DaemonSet\\\",\\\"deployName\\\":\\\"ms-node-controller\\\"}\"}]},{\"id\":\"03e331f4-6ac9-40c4-b3f9-b1789ae2f33d\",\"name\":\"155资源池\",\"type\":\"NODE\",\"description\":null,\"status\":\"VALID\",\"createTime\":1704865158507,\"updateTime\":1717483463658,\"image\":\"registry.cn-qingdao.aliyuncs.com/metersphere/jmeter-master:5.5-ms11-jdk17\",\"heap\":null,\"gcAlgo\":null,\"createUser\":null,\"api\":true,\"performance\":true,\"backendListener\":false,\"resources\":[{\"id\":\"b8ac7c2f-7e14-4e6e-b1b9-9a7184faca19\",\"testResourcePoolId\":\"03e331f4-6ac9-40c4-b3f9-b1789ae2f33d\",\"status\":\"VALID\",\"createTime\":1717483463676,\"updateTime\":1717483463676,\"configuration\":\"{\\\"ip\\\":\\\"*************\\\",\\\"port\\\":8082,\\\"monitorPort\\\":913,\\\"maxConcurrency\\\":10,\\\"id\\\":\\\"b8ac7c2f-7e14-4e6e-b1b9-9a7184faca19\\\",\\\"deployType\\\":\\\"DaemonSet\\\",\\\"deployName\\\":\\\"ms-node-controller\\\"}\"},{\"id\":\"6584492d-26e3-456d-a704-6d1eaae22c1b\",\"testResourcePoolId\":\"03e331f4-6ac9-40c4-b3f9-b1789ae2f33d\",\"status\":\"VALID\",\"createTime\":1717483463681,\"updateTime\":1717483463681,\"configuration\":\"{\\\"port\\\":8082,\\\"monitorPort\\\":9102,\\\"maxConcurrency\\\":100,\\\"ip\\\":\\\"************\\\",\\\"id\\\":\\\"6584492d-26e3-456d-a704-6d1eaae22c1b\\\",\\\"deployType\\\":\\\"DaemonSet\\\",\\\"deployName\\\":\\\"ms-node-controller\\\"}\"}]},{\"id\":\"519956e3-4478-40d9-a616-8f7917286ea8\",\"name\":\"66关闭后置监听器\",\"type\":\"NODE\",\"description\":null,\"status\":\"VALID\",\"createTime\":1713246503327,\"updateTime\":1717483451098,\"image\":null,\"heap\":null,\"gcAlgo\":null,\"createUser\":null,\"api\":true,\"performance\":true,\"backendListener\":false,\"resources\":[{\"id\":\"c248e3fe-4cf6-4842-9f4e-014c23c3fe70\",\"testResourcePoolId\":\"519956e3-4478-40d9-a616-8f7917286ea8\",\"status\":\"VALID\",\"createTime\":1717483451105,\"updateTime\":1717483451105,\"configuration\":\"{\\\"ip\\\":\\\"************\\\",\\\"port\\\":8082,\\\"monitorPort\\\":9130,\\\"maxConcurrency\\\":100,\\\"id\\\":\\\"c248e3fe-4cf6-4842-9f4e-014c23c3fe70\\\",\\\"deployType\\\":\\\"DaemonSet\\\",\\\"deployName\\\":\\\"ms-node-controller\\\"}\"}]}]}"}, "redirectURL": "", "headersSize": 252, "bodySize": 2856, "_transferSize": 3108, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "************", "startedDateTime": "2024-08-08T08:06:14.400Z", "time": 103.04200000246055, "timings": {"blocked": 0.6499999854788184, "dns": -1, "ssl": -1, "connect": -1, "send": 0.02699999999999997, "wait": 102.15499998954498, "receive": 0.21000002743676305, "_blocked_queueing": 0.2579999854788184, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 145648}, {"functionName": "xhr", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 143691}, {"functionName": "Qt", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 150609}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "_request", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 153327}, {"functionName": "request", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 151983}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 121631}, {"functionName": "T", "scriptId": "25", "url": "http://************:8081/js/setting-app.31dcaa0a.js", "lineNumber": 0, "columnNumber": 361243}, {"functionName": "n", "scriptId": "47", "url": "http://************:8081/js/setting-8711.e5990e2c.js", "lineNumber": 0, "columnNumber": 19797}, {"functionName": "getDefaultQuota", "scriptId": "49", "url": "http://************:8081/js/setting-3617.3c6437cc.js", "lineNumber": 0, "columnNumber": 2149}, {"functionName": "mounted", "scriptId": "49", "url": "http://************:8081/js/setting-3617.3c6437cc.js", "lineNumber": 0, "columnNumber": 2545}, {"functionName": "zn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 24950}, {"functionName": "zi", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 33922}, {"functionName": "insert", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 37374}, {"functionName": "A", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 58002}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 59327}, {"functionName": "Pi.e._update", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 31510}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 32273}, {"functionName": "e.get", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 28492}, {"functionName": "e.run", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 29229}, {"functionName": "Qi", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 34489}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 26031}, {"functionName": "Yn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 25426}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "Hn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 25517}, {"functionName": "Qn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 26096}, {"functionName": "ir", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 35002}, {"functionName": "e.update", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 29165}, {"functionName": "e.notify", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 6804}, {"functionName": "set", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 8662}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 56623}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 56594}, {"functionName": "et.updateRoute", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51463}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 50046}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51328}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47365}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "Ge", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47411}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51259}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47365}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47393}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51170}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47670}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 48404}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47847}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 48039}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 48015}, {"functionName": "<PERSON>", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47965}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47474}, {"functionName": "f", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 50924}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47374}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47393}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51170}, {"functionName": "eval", "scriptId": "25", "url": "http://************:8081/js/setting-app.31dcaa0a.js", "lineNumber": 0, "columnNumber": 400894}, {"functionName": "f", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 50924}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47374}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "Ge", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47411}, {"functionName": "et.confirmTransition", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51194}, {"functionName": "et.transitionTo", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 50012}, {"functionName": "t.push", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 53641}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 57086}, {"functionName": "_t.push", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 57049}, {"functionName": "f.Ay.push", "scriptId": "25", "url": "http://************:8081/js/setting-app.31dcaa0a.js", "lineNumber": 0, "columnNumber": 389825}, {"functionName": "routeToItem", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 44, "columnNumber": 110768}, {"functionName": "handleItemClick", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 44, "columnNumber": 110374}, {"functionName": "zn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 24937}, {"functionName": "Ai.e.$emit", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 30964}, {"functionName": "dispatch", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 44, "columnNumber": 620936}, {"functionName": "handleClick", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 44, "columnNumber": 120315}, {"functionName": "zn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 24937}, {"functionName": "n", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 14734}, {"functionName": "Pa.o._wrapper", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 62419}]}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "1187648", "request": {"method": "GET", "url": "http://************:8081/setting/quota/default/workspace", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "zh-CN"}, {"name": "CSRF-TOKEN", "value": "442WhMRQfh9N2uVKgj6YOWgY6JmGJk+MkyKq0N6UMrxgeyaiiAtqTAPNjI4bu6miUKuGeevt23GOhSuvD8agAXaVgK/j9LbBH0aqfaXc2KU="}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "************:8081"}, {"name": "PROJECT", "value": "05a5c1ea-d1e2-4645-956f-6300d4becf00"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://************:8081/"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "WORKSPACE", "value": "048b143c-e071-424f-b5fe-3ca89fb93c13"}, {"name": "X-AUTH-TOKEN", "value": "616e19c0-3b10-4391-81a9-401e6f6f1b34"}], "queryString": [], "cookies": [], "headersSize": 632, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Date", "value": "Thu, 08 Aug 2024 08:05:45 GMT"}, {"name": "Vary", "value": "Origin"}, {"name": "Vary", "value": "Access-Control-Request-Method"}, {"name": "Vary", "value": "Access-Control-Request-Headers"}, {"name": "Vary", "value": "Accept-Encoding"}, {"name": "transfer-encoding", "value": "chunked"}], "cookies": [], "content": {"size": 612, "mimeType": "application/json", "compression": 185, "text": "{\"success\":true,\"message\":null,\"data\":{\"id\":\"workspace\",\"api\":0,\"performance\":0,\"maxThreads\":0,\"duration\":0,\"resourcePool\":\"8c17bdc2-1817-11eb-92cf-0242ac120004,a360b679-88c6-4117-95dd-e4ea886b1b7d,519956e3-4478-40d9-a616-8f7917286ea8,132c515d-5d39-4c21-af1e-40c6b748d366,28947e4b-b2b5-4379-90d2-a6cf670b506e,0b9f70e4-f159-4f29-ad1a-d3dd6f0fd5cd,03e331f4-6ac9-40c4-b3f9-b1789ae2f33d,bafa05bb-d4f6-4eaf-a8de-0d895b80ed94\",\"workspaceId\":null,\"useDefault\":null,\"updateTime\":1721730820782,\"member\":0,\"project\":0,\"projectId\":null,\"vumTotal\":0.00,\"vumUsed\":null,\"moduleSetting\":\"workstation,track,api,ui,performance\"}}"}, "redirectURL": "", "headersSize": 252, "bodySize": 427, "_transferSize": 679, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "************", "startedDateTime": "2024-08-08T08:06:14.400Z", "time": 88.29600000171922, "timings": {"blocked": 0.5319999881871045, "dns": -1, "ssl": -1, "connect": -1, "send": 0.01699999999999996, "wait": 87.35399998962693, "receive": 0.39300002390518785, "_blocked_queueing": 0.26599998818710446, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 145648}, {"functionName": "xhr", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 143691}, {"functionName": "Qt", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 150609}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "_request", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 153327}, {"functionName": "request", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 151983}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 121631}, {"functionName": "V", "scriptId": "25", "url": "http://************:8081/js/setting-app.31dcaa0a.js", "lineNumber": 0, "columnNumber": 361292}, {"functionName": "c", "scriptId": "47", "url": "http://************:8081/js/setting-8711.e5990e2c.js", "lineNumber": 0, "columnNumber": 19924}, {"functionName": "search", "scriptId": "47", "url": "http://************:8081/js/setting-8711.e5990e2c.js", "lineNumber": 0, "columnNumber": 16984}, {"functionName": "activated", "scriptId": "47", "url": "http://************:8081/js/setting-8711.e5990e2c.js", "lineNumber": 0, "columnNumber": 18373}, {"functionName": "zn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 24950}, {"functionName": "zi", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 33922}, {"functionName": "Ri", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 33591}, {"functionName": "Ri", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 33572}, {"functionName": "nr", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 34807}, {"functionName": "Qi", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 34530}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 26031}, {"functionName": "Yn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 25426}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "Hn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 25517}, {"functionName": "Qn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 26096}, {"functionName": "ir", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 35002}, {"functionName": "e.update", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 29165}, {"functionName": "e.notify", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 6804}, {"functionName": "set", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 8662}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 56623}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 56594}, {"functionName": "et.updateRoute", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51463}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 50046}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51328}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47365}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "Ge", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47411}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51259}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47365}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47393}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51170}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47670}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 48404}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47847}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 48039}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 48015}, {"functionName": "<PERSON>", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47965}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47474}, {"functionName": "f", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 50924}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47374}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47393}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51170}, {"functionName": "eval", "scriptId": "25", "url": "http://************:8081/js/setting-app.31dcaa0a.js", "lineNumber": 0, "columnNumber": 400894}, {"functionName": "f", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 50924}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47374}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "i", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47403}, {"functionName": "Ge", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 47411}, {"functionName": "et.confirmTransition", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 51194}, {"functionName": "et.transitionTo", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 50012}, {"functionName": "t.push", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 53641}, {"functionName": "eval", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 57086}, {"functionName": "_t.push", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 86, "columnNumber": 57049}, {"functionName": "f.Ay.push", "scriptId": "25", "url": "http://************:8081/js/setting-app.31dcaa0a.js", "lineNumber": 0, "columnNumber": 389825}, {"functionName": "routeToItem", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 44, "columnNumber": 110768}, {"functionName": "handleItemClick", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 44, "columnNumber": 110374}, {"functionName": "zn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 24937}, {"functionName": "Ai.e.$emit", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 30964}, {"functionName": "dispatch", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 44, "columnNumber": 620936}, {"functionName": "handleClick", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 44, "columnNumber": 120315}, {"functionName": "zn", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 24937}, {"functionName": "n", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 14734}, {"functionName": "Pa.o._wrapper", "scriptId": "24", "url": "http://************:8081/js/setting-chunk-vendors.e1465f79.js", "lineNumber": 94, "columnNumber": 62419}]}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "1187567", "request": {"method": "POST", "url": "http://************:8081/setting/quota/list/workspace/1/10", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "zh-CN"}, {"name": "CSRF-TOKEN", "value": "442WhMRQfh9N2uVKgj6YOWgY6JmGJk+MkyKq0N6UMrxgeyaiiAtqTAPNjI4bu6miUKuGeevt23GOhSuvD8agAXaVgK/j9LbBH0aqfaXc2KU="}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "2"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Host", "value": "************:8081"}, {"name": "Origin", "value": "http://************:8081"}, {"name": "PROJECT", "value": "05a5c1ea-d1e2-4645-956f-6300d4becf00"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://************:8081/"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "WORKSPACE", "value": "048b143c-e071-424f-b5fe-3ca89fb93c13"}, {"name": "X-AUTH-TOKEN", "value": "616e19c0-3b10-4391-81a9-401e6f6f1b34"}], "queryString": [], "cookies": [], "headersSize": 720, "bodySize": 2, "postData": {"mimeType": "application/json", "text": "{}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Date", "value": "Thu, 08 Aug 2024 08:05:45 GMT"}, {"name": "Vary", "value": "Origin"}, {"name": "Vary", "value": "Access-Control-Request-Method"}, {"name": "Vary", "value": "Access-Control-Request-Headers"}, {"name": "Vary", "value": "Accept-Encoding"}, {"name": "transfer-encoding", "value": "chunked"}], "cookies": [], "content": {"size": 4081, "mimeType": "application/json", "compression": 3103, "text": "{\"success\":true,\"message\":null,\"data\":{\"listObject\":[{\"id\":\"87c67f3c-cd5d-4b88-b7db-bf95f6e8553b\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"c239e34f-faf3-43d1-8294-4584731c199e\",\"useDefault\":true,\"updateTime\":1721784547632,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"工作空间_ut4mjnvx\",\"projectName\":null,\"projectUsed\":0},{\"id\":\"121bbe7d-bc40-4a9b-923f-d1cfa41003cd\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"d2658c95-7d7a-46f9-8928-e77e7f9d2b4e\",\"useDefault\":true,\"updateTime\":1721784542058,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"工作空间_guPvyG90\",\"projectName\":null,\"projectUsed\":0},{\"id\":\"2617aa3e-b887-49d8-8c73-e12aca1f8a82\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"5ecb39c8-7789-4882-b73d-e9613f310aa7\",\"useDefault\":true,\"updateTime\":1721784531076,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"工作空间_xan83ZB7\",\"projectName\":null,\"projectUsed\":0},{\"id\":\"8a9a28dd-5adf-4543-9bdc-af28907aba26\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"2729e83e-984a-4a50-ae9c-85b5f81346b0\",\"useDefault\":true,\"updateTime\":1721784526531,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"工作空间_zfFEit0M\",\"projectName\":null,\"projectUsed\":0},{\"id\":\"3cc53e8c-990c-4ffb-aa8c-8a9302969012\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"c6b164bb-3966-4c10-8ac1-2f451cab339f\",\"useDefault\":true,\"updateTime\":1721784517974,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"工作空间_P0qyCMeh\",\"projectName\":null,\"projectUsed\":0},{\"id\":\"bf75cfac-3f7e-4587-a5bc-de1014825597\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"97679d16-4f30-4df5-be45-f91379aecf19\",\"useDefault\":true,\"updateTime\":1721783923519,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"工作空间_tQAI6tc4\",\"projectName\":null,\"projectUsed\":2},{\"id\":\"5510344a-d2cc-4587-a55e-2917f6514f7d\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"431e813d-5de5-4c1d-affe-34f8f15a1108\",\"useDefault\":true,\"updateTime\":1721783087058,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"工作空间_2pA85Gpv\",\"projectName\":null,\"projectUsed\":1},{\"id\":\"46872b1b-372b-4de7-8f0b-53d065893f08\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"b1da8acc-cb55-450c-9189-a36e143cfa57\",\"useDefault\":true,\"updateTime\":1720175342992,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"guoxiaoyong\",\"projectName\":null,\"projectUsed\":0},{\"id\":\"25d6051b-116a-4561-93b4-6bcd20b4803d\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"7caa64ef-1f1b-400c-964c-f52587856ccb\",\"useDefault\":true,\"updateTime\":1720075574958,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":0.17,\"moduleSetting\":null,\"workspaceName\":\"7.4工作空间-yc\",\"projectName\":null,\"projectUsed\":1},{\"id\":\"2f996458-fc84-42e7-813b-d802603acb2a\",\"api\":null,\"performance\":null,\"maxThreads\":null,\"duration\":null,\"resourcePool\":null,\"workspaceId\":\"25140054-8520-4bc7-a231-76366f56cf78\",\"useDefault\":true,\"updateTime\":1719363182724,\"member\":null,\"project\":null,\"projectId\":null,\"vumTotal\":null,\"vumUsed\":null,\"moduleSetting\":null,\"workspaceName\":\"工作空间_nBt3I17E\",\"projectName\":null,\"projectUsed\":0}],\"itemCount\":67,\"pageCount\":7}}"}, "redirectURL": "", "headersSize": 252, "bodySize": 978, "_transferSize": 1230, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "************", "startedDateTime": "2024-08-08T08:06:14.400Z", "time": 153.65900000324473, "timings": {"blocked": 0.44200001695752145, "dns": -1, "ssl": -1, "connect": -1, "send": 0.022999999999999993, "wait": 152.94599998964182, "receive": 0.2479999966453761, "_blocked_queueing": 0.30800001695752144, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}]}}