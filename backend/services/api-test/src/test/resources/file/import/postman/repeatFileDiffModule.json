{"info": {"_postman_id": "eb491590-d146-4dac-9c80-e4c79908fa65", "name": "repeatFileDiffModule", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "15342444"}, "item": [{"name": "firstlevel", "item": [{"name": "secondlevel", "item": [{"name": "https://api.tapd.cn/stories?workspace_id=AAAAA&idddd=BBBBB Copy 2", "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": "metersphere", "type": "string"}, {"key": "username", "value": "metersphere", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "https://api.tapd.cn/stories?workspace_id=AAAAA&idddd=BBBBB", "protocol": "https", "host": ["api", "tapd", "cn"], "path": ["stories"], "query": [{"key": "workspace_id", "value": "AAAAA"}, {"key": "idddd", "value": "BBBBB"}]}}, "response": []}, {"name": "post的mock请求", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "MMM", "value": "PPP", "type": "text"}]}, "url": {"raw": "https://ms-v3.fit2cloud.com/mock-server/100080/100004/mock-for-post", "protocol": "https", "host": ["ms-v3", "fit2cloud", "com"], "path": ["mock-server", "100080", "100004", "mock-for-post"]}}, "response": []}, {"name": "put的mock请求", "request": {"method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "methoddd", "value": "pppp", "type": "text"}]}, "url": {"raw": "https://ms-v3.fit2cloud.com/mock-server/100080/100005/mock-for-put", "protocol": "https", "host": ["ms-v3", "fit2cloud", "com"], "path": ["mock-server", "100080", "100005", "mock-for-put"]}}, "response": []}]}]}]}