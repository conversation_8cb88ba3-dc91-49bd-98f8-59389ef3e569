-- 插入测试数据

DELETE FROM `api_definition` WHERE `id` in ('1001','1002','1003','1004','1005','1006');
INSERT INTO `api_definition` (`id`, `name`, `protocol`, `method`, `path`, `status`, `num`, `tags`, `pos`, `project_id`, `module_id`, `latest`, `version_id`, `ref_id`, `description`, `create_time`, `create_user`, `update_time`, `update_user`, `delete_user`, `delete_time`, `deleted`) VALUES ('1001', 'test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test1test', 'HTTP', 'POST', '/api/admin/1', 'PROCESSING', 1001, '[\"test3\",\"te\"]', 1, '100001100001', 'root', b'1', '100570499574136985', '1001', NULL, 1699500298164, 'admin', 1699500298162, 'admin', NULL, NULL, b'0');
INSERT INTO `api_definition` (`id`, `name`, `protocol`, `method`, `path`, `status`, `num`, `tags`, `pos`, `project_id`, `module_id`, `latest`, `version_id`, `ref_id`, `description`, `create_time`, `create_user`, `update_time`, `update_user`, `delete_user`, `delete_time`, `deleted`) VALUES ('1002', 'test-2', 'HTTP', 'GET', '/api/admin/2', 'DEPRECATED', 1002, null, 1, '100001100001', '1001001', b'1', '1005704995741369851', '1002', NULL, 1699500298165, 'admin', 1699500298163, 'admin', NULL, NULL, b'0');
INSERT INTO `api_definition` (`id`, `name`, `protocol`, `method`, `path`, `status`, `num`, `tags`, `pos`, `project_id`, `module_id`, `latest`, `version_id`, `ref_id`, `description`, `create_time`, `create_user`, `update_time`, `update_user`, `delete_user`, `delete_time`, `deleted`) VALUES ('1003', 'test-3', 'HTTP', 'POST', '/api/admin/3', 'DEBUGGING', 1003, '[\"test3\",\"te\"]', 1, '100001100001', '10001', b'1', '100570499574136985', '1002', NULL, 1699500298166, 'admin', 1699500298164, 'admin', NULL, NULL, b'0');
INSERT INTO `api_definition` (`id`, `name`, `protocol`, `method`, `path`, `status`, `num`, `tags`, `pos`, `project_id`, `module_id`, `latest`, `version_id`, `ref_id`, `description`, `create_time`, `create_user`, `update_time`, `update_user`, `delete_user`, `delete_time`, `deleted`) VALUES ('1004', 'test-4', 'HTTP', 'GET', '/api/admin/4', 'PROCESSING', 1004, '[\"test4\",\"te\"]', 1, '100001100001', '10001', b'1', '100570499574136985', '1004', NULL, 1699500298167, 'admin', 1699500298165, 'admin', NULL, NULL, b'0');
INSERT INTO `api_definition` (`id`, `name`, `protocol`, `method`, `path`, `status`, `num`, `tags`, `pos`, `project_id`, `module_id`, `latest`, `version_id`, `ref_id`, `description`, `create_time`, `create_user`, `update_time`, `update_user`, `delete_user`, `delete_time`, `deleted`) VALUES ('1005', 'test-65', 'HTTP', 'POST', '/api/admin/5', 'DONE', 1005, '[\"test5\",\"te\"]', 1, '100001100001', '10001', b'0', '100570499574136985', '1004', NULL, 1699500298168, 'admin', 1699500298166, 'admin', NULL, NULL, b'0');
INSERT INTO `api_definition` (`id`, `name`, `protocol`, `method`, `path`, `status`, `num`, `tags`, `pos`, `project_id`, `module_id`, `latest`, `version_id`, `ref_id`, `description`, `create_time`, `create_user`, `update_time`, `update_user`, `delete_user`, `delete_time`, `deleted`) VALUES ('1006', 'test-6', 'HTTP', 'GET', '/api/admin/6', 'DEBUGGING', 1006, '[\"test6\",\"te\"]', 1, '100001100001', '10001', b'1', '100570499574136985', '1006', NULL, 1699500298169, 'admin', 1699500298167, 'admin', NULL, NULL, b'0');

DELETE FROM `project_version` WHERE `id` = '100570499574136985';
INSERT INTO project_version (id, project_id, name, description, status, latest, publish_time, start_time, end_time, create_time, create_user) VALUES ('100570499574136985', '100001100001', 'v1.0.0', NULL, 'open', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, 'admin');

DELETE FROM `api_test_case` WHERE `id` in ('12df5721-c5e6-a38b-e999-3eafcb992094','12df5721-c5e6-a38b-e999-3eafcb992100','12df5721-c5e6-a38b-e999-3eafcb992233','3ee2ae9c-a680-4ed6-b115-1f6ab8980973','3ee2ae9c-a680-4ed6-b115-1f6ab8980100','3ee2ae9c-a680-4ed6-b115-1f6ab8980589','3ee2ae9c-a680-4ed6-b115-1f6ab8980553','3ee2ae9c-a680-4ed6-b115-1f6ab8980104','3ee2ae9c-a680-4ed6-b115-1f6ab8980545');
INSERT INTO `api_test_case` (`id`, `name`, `priority`, `num`, `tags`, `status`, `last_report_status`, `last_report_id`, `pos`, `project_id`, `api_definition_id`, `version_id`, `environment_id`, `create_time`, `create_user`, `update_time`, `update_user`, `delete_time`, `delete_user`, `deleted`) VALUES ('12df5721-c5e6-a38b-e999-3eafcb992094', '查询windows主机1', 'P0', 100002001, NULL, 'PROCESSING', 'PENDING', '10001', 10000, '100001100001', '1001', '100570499574136985', 'admin', 1699500298164, 'admin', 1699500298164, 'admin', NULL, NULL, b'0');
INSERT INTO `api_test_case` (`id`, `name`, `priority`, `num`, `tags`, `status`, `last_report_status`, `last_report_id`, `pos`, `project_id`, `api_definition_id`, `version_id`, `environment_id`, `create_time`, `create_user`, `update_time`, `update_user`, `delete_time`, `delete_user`, `deleted`) VALUES ('12df5721-c5e6-a38b-e999-3eafcb992100', '查询windows主机2', 'P0', 100002002, NULL, 'PROCESSING', 'PENDING', '10002', 10000, '100001100001', '1001', '100570499574136985', 'admin', 1699500298164, 'admin', 1699500298164, 'admin', NULL, NULL, b'0');
INSERT INTO `api_test_case` (`id`, `name`, `priority`, `num`, `tags`, `status`, `last_report_status`, `last_report_id`, `pos`, `project_id`, `api_definition_id`, `version_id`, `environment_id`, `create_time`, `create_user`, `update_time`, `update_user`, `delete_time`, `delete_user`, `deleted`) VALUES ('12df5721-c5e6-a38b-e999-3eafcb992233', '查询windows主机3', 'P0', 100002003, NULL, 'PROCESSING', 'PENDING', '10003', 10000, '100001100001', '1002', '100570499574136985', 'admin', 1699500298164, 'admin', 1699500298164, 'admin', NULL, NULL, b'0');
INSERT INTO `api_test_case` (`id`, `name`, `priority`, `num`, `tags`, `status`, `last_report_status`, `last_report_id`, `pos`, `project_id`, `api_definition_id`, `version_id`, `environment_id`, `create_time`, `create_user`, `update_time`, `update_user`, `delete_time`, `delete_user`, `deleted`) VALUES ('3ee2ae9c-a680-4ed6-b115-1f6ab8980973', '查询Linux主机1', 'P0', 100002004, NULL, 'PROCESSING', 'PENDING', '10004', 10000, '100001100001', '1002', '100570499574136985', 'admin', 1699500298164, 'admin', 1699500298164, 'admin', NULL, NULL, b'0');
INSERT INTO `api_test_case` (`id`, `name`, `priority`, `num`, `tags`, `status`, `last_report_status`, `last_report_id`, `pos`, `project_id`, `api_definition_id`, `version_id`, `environment_id`, `create_time`, `create_user`, `update_time`, `update_user`, `delete_time`, `delete_user`, `deleted`) VALUES ('3ee2ae9c-a680-4ed6-b115-1f6ab8980100', '查询Linux主机2', 'P0', 100002005, NULL, 'PROCESSING', 'PENDING', '10005', 10000, '100001100001', '1003', '100570499574136985', 'admin', 1699500298164, 'admin', 1699500298164, 'admin', NULL, NULL, b'0');
INSERT INTO `api_test_case` (`id`, `name`, `priority`, `num`, `tags`, `status`, `last_report_status`, `last_report_id`, `pos`, `project_id`, `api_definition_id`, `version_id`, `environment_id`, `create_time`, `create_user`, `update_time`, `update_user`, `delete_time`, `delete_user`, `deleted`) VALUES ('3ee2ae9c-a680-4ed6-b115-1f6ab8980589', '查询Linux主机3', 'P0', 100002006, NULL, 'PROCESSING', 'PENDING', '10006', 10000, '100001100001', '1004', '100570499574136985', 'admin', 1699500298164, 'admin', 1699500298164, 'admin', NULL, NULL, b'0');
INSERT INTO `api_test_case` (`id`, `name`, `priority`, `num`, `tags`, `status`, `last_report_status`, `last_report_id`, `pos`, `project_id`, `api_definition_id`, `version_id`, `environment_id`, `create_time`, `create_user`, `update_time`, `update_user`, `delete_time`, `delete_user`, `deleted`) VALUES ('3ee2ae9c-a680-4ed6-b115-1f6ab8980553', '查询Linux主机4', 'P0', 100002007, NULL, 'PROCESSING', 'PENDING', '10007', 10000, '100001100001', '1005', '100570499574136985', 'admin', 1699500298164, 'admin', 1699500298164, 'admin', NULL, NULL, b'0');
INSERT INTO `api_test_case` (`id`, `name`, `priority`, `num`, `tags`, `status`, `last_report_status`, `last_report_id`, `pos`, `project_id`, `api_definition_id`, `version_id`, `environment_id`, `create_time`, `create_user`, `update_time`, `update_user`, `delete_time`, `delete_user`, `deleted`) VALUES ('3ee2ae9c-a680-4ed6-b115-1f6ab8980104', '查询Linux主机5', 'P0', 100002008, NULL, 'PROCESSING', 'PENDING', '10008', 10000, '100001100001', '1005', '100570499574136985', 'admin', 1699500298164, 'admin', 1699500298164, 'admin', NULL, NULL, b'0');
INSERT INTO `api_test_case` (`id`, `name`, `priority`, `num`, `tags`, `status`, `last_report_status`, `last_report_id`, `pos`, `project_id`, `api_definition_id`, `version_id`, `environment_id`, `create_time`, `create_user`, `update_time`, `update_user`, `delete_time`, `delete_user`, `deleted`) VALUES ('3ee2ae9c-a680-4ed6-b115-1f6ab8980545', '查询Linux主机6', 'P0', 100002009, NULL, 'PROCESSING', 'PENDING', NULL, 10000, '100001100001', '1005', '100570499574136985', 'admin', 1699500298164, 'admin', 1699500298164, 'admin', NULL, NULL, b'0');

DELETE FROM `api_definition_module` WHERE `id` in ('10001', 'case-moduleId');
INSERT INTO `api_definition_module` (`id`, `name`, `parent_id`, `project_id`, `pos`, `create_time`, `update_time`, `update_user`, `create_user`) VALUES ('10001', 'module1', 'NONE', '100001100001', 10, 0, 0, 'admin', 'admin');
INSERT INTO `api_definition_module` (`id`, `name`, `parent_id`, `project_id`, `pos`, `create_time`, `update_time`, `update_user`, `create_user`) VALUES ('case-moduleId', 'case-moduleId', 'NONE', '100001100001', 10, 0, 0, 'admin', 'admin');

DELETE FROM `api_definition_blob` WHERE `id` in ('1001','1002','1003','1004','1005','1006');
INSERT INTO `api_definition_blob` (`id`, `request`, `response`) VALUES ('1001', 0x504B03041400080808004A547857000000000000000000000000030000007A6970CD92B14EC3301040FFE5E6A8296B36842A180055281B62709D4B6D61FBDCF3991055FD779C840C949185C5A77BB6CFEF6C9F21921B3D7134563F2B8FD0C0537A68DBFDCEA1C720504158F01C2AC0A00EAEE4BD72092BD0C6BA8E314013B27315647665AD11894D5D0FC3B0114CB2D1E4CBD6A8C494C97A4225F52886BA02EE776D490FD48D6B1583AA434ED0BC9EE11D0B9EC70A3E94CB93CB127FC9749834DB28968ACF9CC1E5AD029ECEFB7BA9A90156BE1D23AE9E8CA76C194B13C2B9ECF2363C62384E6D2E0BBCFAFC093068EAD66326B953461EFFAD1D8941BEA3D0DB233467D014026A69AD47CAE54E6FB6DBED7CBF9142C22BAC91C5F6562BC15B6755794D5073ACA027E76878C1AEE86949ABA0CA4257F03253B32A2C9A9171CFA431259A3EC937A424D7F4F205504B070852639A522B010000E0020000504B010214001400080808004A54785752639A522B010000E00200000300000000000000000000000000000000007A6970504B05060000000001000100310000005C0100000000, 0x504B03041400080808004A547857000000000000000000000000030000007A6970A58F414BC4301085FFCB9C03B69EA4B74559141465295E9622B3CD743798263199C82EA5FFDD49EB82772F19DECB9BEF25FB098C86A656C09740D0C063DBBE81028763513B4AC1BB44B55827424D3141B39FE0932E725B4E05DF6873C9AE5301393C583106B48914684A7D34818D77122A0AE64EC1C16B414CCB6CD7EAEDEBEEE5E361D36E0432443FBE17E03FFB98124B2660C4F1B786E95CAC485FD94492CF73CCB2371AF74CEEC82768EE44E1F9AAEA4A41EF1D93E33F849B60D1B8A5BEF79AAE94C1587A12A6CBD6CEDDAC2031724EF74B046EAB0ACA0B07CC96B7168FEBDADCFD00504B0708FF595995E400000087010000504B010214001400080808004A547857FF595995E4000000870100000300000000000000000000000000000000007A6970504B0506000000000100010031000000150100000000);
INSERT INTO `api_definition_blob` (`id`, `request`, `response`) VALUES ('1002', 0x504B030414000808080049547857000000000000000000000000030000007A6970CD92B14EC3301040FFE5E6A8296B36842A180055281B62709D4B6D61FBDCF3991055FD779C840C949185C5A77BB6CFEF6C9F21921B3D7134563F2B8FD0C0537A68DBFDCEA1C720504158F01C2AC0A00EAEE4BD72092BD0C6BA8E314013B27315647665AD11894D5D0FC3B0114CB2D1E4CBD6A8C494C97A4225F52886BA02EE776D490FD48D6B1583AA434ED0BC9EE11D0B9EC70A3E94CB93CB127FC9749834DB28968ACF9CC1E5AD029ECEFB7BA9A90156BE1D23AE9E8CA76C194B13C2B9ECF2363C62384E6D2E0BBCFAFC093068EAD66326B953461EFFAD1D8941BEA3D0DB233467D014026A69AD47CAE54E6FB6DBED7CBF9142C22BAC91C5F6562BC15B6755794D5073ACA027E76878C1AEE86949ABA0CA4257F03253B32A2C9A9171CFA431259A3EC937A424D7F4F205504B070852639A522B010000E0020000504B010214001400080808004954785752639A522B010000E00200000300000000000000000000000000000000007A6970504B05060000000001000100310000005C0100000000, 0x504B030414000808080049547857000000000000000000000000030000007A6970A58F414BC4301085FFCB9C03B69EA4B74559141465295E9622B3CD743798263199C82EA5FFDD49EB82772F19DECB9BEF25FB098C86A656C09740D0C063DBBE81028763513B4AC1BB44B55827424D3141B39FE0932E725B4E05DF6873C9AE5301393C583106B48914684A7D34818D77122A0AE64EC1C16B414CCB6CD7EAEDEBEEE5E361D36E0432443FBE17E03FFB98124B2660C4F1B786E95CAC485FD94492CF73CCB2371AF74CEEC82768EE44E1F9AAEA4A41EF1D93E33F849B60D1B8A5BEF79AAE94C1587A12A6CBD6CEDDAC2031724EF74B046EAB0ACA0B07CC96B7168FEBDADCFD00504B0708FF595995E400000087010000504B0102140014000808080049547857FF595995E4000000870100000300000000000000000000000000000000007A6970504B0506000000000100010031000000150100000000);
INSERT INTO `api_definition_blob` (`id`, `request`, `response`) VALUES ('1003', 0x504B030414000808080049547857000000000000000000000000030000007A6970CD92B14EC3301040FFE5E6A8296B36842A180055281B62709D4B6D61FBDCF3991055FD779C840C949185C5A77BB6CFEF6C9F21921B3D7134563F2B8FD0C0537A68DBFDCEA1C720504158F01C2AC0A00EAEE4BD72092BD0C6BA8E314013B27315647665AD11894D5D0FC3B0114CB2D1E4CBD6A8C494C97A4225F52886BA02EE776D490FD48D6B1583AA434ED0BC9EE11D0B9EC70A3E94CB93CB127FC9749834DB28968ACF9CC1E5AD029ECEFB7BA9A90156BE1D23AE9E8CA76C194B13C2B9ECF2363C62384E6D2E0BBCFAFC093068EAD66326B953461EFFAD1D8941BEA3D0DB233467D014026A69AD47CAE54E6FB6DBED7CBF9142C22BAC91C5F6562BC15B6755794D5073ACA027E76878C1AEE86949ABA0CA4257F03253B32A2C9A9171CFA431259A3EC937A424D7F4F205504B070852639A522B010000E0020000504B010214001400080808004954785752639A522B010000E00200000300000000000000000000000000000000007A6970504B05060000000001000100310000005C0100000000, 0x504B030414000808080049547857000000000000000000000000030000007A6970A58F414BC4301085FFCB9C03B69EA4B74559141465295E9622B3CD743798263199C82EA5FFDD49EB82772F19DECB9BEF25FB098C86A656C09740D0C063DBBE81028763513B4AC1BB44B55827424D3141B39FE0932E725B4E05DF6873C9AE5301393C583106B48914684A7D34818D77122A0AE64EC1C16B414CCB6CD7EAEDEBEEE5E361D36E0432443FBE17E03FFB98124B2660C4F1B786E95CAC485FD94492CF73CCB2371AF74CEEC82768EE44E1F9AAEA4A41EF1D93E33F849B60D1B8A5BEF79AAE94C1587A12A6CBD6CEDDAC2031724EF74B046EAB0ACA0B07CC96B7168FEBDADCFD00504B0708FF595995E400000087010000504B0102140014000808080049547857FF595995E4000000870100000300000000000000000000000000000000007A6970504B0506000000000100010031000000150100000000);
INSERT INTO `api_definition_blob` (`id`, `request`, `response`) VALUES ('1004', 0x504B03041400080808004A547857000000000000000000000000030000007A6970CD92B14EC3301040FFE5E6A8296B36842A180055281B62709D4B6D61FBDCF3991055FD779C840C949185C5A77BB6CFEF6C9F21921B3D7134563F2B8FD0C0537A68DBFDCEA1C720504158F01C2AC0A00EAEE4BD72092BD0C6BA8E314013B27315647665AD11894D5D0FC3B0114CB2D1E4CBD6A8C494C97A4225F52886BA02EE776D490FD48D6B1583AA434ED0BC9EE11D0B9EC70A3E94CB93CB127FC9749834DB28968ACF9CC1E5AD029ECEFB7BA9A90156BE1D23AE9E8CA76C194B13C2B9ECF2363C62384E6D2E0BBCFAFC093068EAD66326B953461EFFAD1D8941BEA3D0DB233467D014026A69AD47CAE54E6FB6DBED7CBF9142C22BAC91C5F6562BC15B6755794D5073ACA027E76878C1AEE86949ABA0CA4257F03253B32A2C9A9171CFA431259A3EC937A424D7F4F205504B070852639A522B010000E0020000504B010214001400080808004A54785752639A522B010000E00200000300000000000000000000000000000000007A6970504B05060000000001000100310000005C0100000000, 0x504B03041400080808004A547857000000000000000000000000030000007A6970A58F414BC4301085FFCB9C03B69EA4B74559141465295E9622B3CD743798263199C82EA5FFDD49EB82772F19DECB9BEF25FB098C86A656C09740D0C063DBBE81028763513B4AC1BB44B55827424D3141B39FE0932E725B4E05DF6873C9AE5301393C583106B48914684A7D34818D77122A0AE64EC1C16B414CCB6CD7EAEDEBEEE5E361D36E0432443FBE17E03FFB98124B2660C4F1B786E95CAC485FD94492CF73CCB2371AF74CEEC82768EE44E1F9AAEA4A41EF1D93E33F849B60D1B8A5BEF79AAE94C1587A12A6CBD6CEDDAC2031724EF74B046EAB0ACA0B07CC96B7168FEBDADCFD00504B0708FF595995E400000087010000504B010214001400080808004A547857FF595995E4000000870100000300000000000000000000000000000000007A6970504B0506000000000100010031000000150100000000);
INSERT INTO `api_definition_blob` (`id`, `request`, `response`) VALUES ('1005', 0x504B030414000808080049547857000000000000000000000000030000007A6970CD92B14EC3301040FFE5E6A8296B36842A180055281B62709D4B6D61FBDCF3991055FD779C840C949185C5A77BB6CFEF6C9F21921B3D7134563F2B8FD0C0537A68DBFDCEA1C720504158F01C2AC0A00EAEE4BD72092BD0C6BA8E314013B27315647665AD11894D5D0FC3B0114CB2D1E4CBD6A8C494C97A4225F52886BA02EE776D490FD48D6B1583AA434ED0BC9EE11D0B9EC70A3E94CB93CB127FC9749834DB28968ACF9CC1E5AD029ECEFB7BA9A90156BE1D23AE9E8CA76C194B13C2B9ECF2363C62384E6D2E0BBCFAFC093068EAD66326B953461EFFAD1D8941BEA3D0DB233467D014026A69AD47CAE54E6FB6DBED7CBF9142C22BAC91C5F6562BC15B6755794D5073ACA027E76878C1AEE86949ABA0CA4257F03253B32A2C9A9171CFA431259A3EC937A424D7F4F205504B070852639A522B010000E0020000504B010214001400080808004954785752639A522B010000E00200000300000000000000000000000000000000007A6970504B05060000000001000100310000005C0100000000, 0x504B030414000808080049547857000000000000000000000000030000007A6970A58F414BC4301085FFCB9C03B69EA4B74559141465295E9622B3CD743798263199C82EA5FFDD49EB82772F19DECB9BEF25FB098C86A656C09740D0C063DBBE81028763513B4AC1BB44B55827424D3141B39FE0932E725B4E05DF6873C9AE5301393C583106B48914684A7D34818D77122A0AE64EC1C16B414CCB6CD7EAEDEBEEE5E361D36E0432443FBE17E03FFB98124B2660C4F1B786E95CAC485FD94492CF73CCB2371AF74CEEC82768EE44E1F9AAEA4A41EF1D93E33F849B60D1B8A5BEF79AAE94C1587A12A6CBD6CEDDAC2031724EF74B046EAB0ACA0B07CC96B7168FEBDADCFD00504B0708FF595995E400000087010000504B0102140014000808080049547857FF595995E4000000870100000300000000000000000000000000000000007A6970504B0506000000000100010031000000150100000000);
INSERT INTO `api_definition_blob` (`id`, `request`, `response`) VALUES ('1006', 0x504B030414000808080049547857000000000000000000000000030000007A6970CD92B14EC3301040FFE5E6A8296B36842A180055281B62709D4B6D61FBDCF3991055FD779C840C949185C5A77BB6CFEF6C9F21921B3D7134563F2B8FD0C0537A68DBFDCEA1C720504158F01C2AC0A00EAEE4BD72092BD0C6BA8E314013B27315647665AD11894D5D0FC3B0114CB2D1E4CBD6A8C494C97A4225F52886BA02EE776D490FD48D6B1583AA434ED0BC9EE11D0B9EC70A3E94CB93CB127FC9749834DB28968ACF9CC1E5AD029ECEFB7BA9A90156BE1D23AE9E8CA76C194B13C2B9ECF2363C62384E6D2E0BBCFAFC093068EAD66326B953461EFFAD1D8941BEA3D0DB233467D014026A69AD47CAE54E6FB6DBED7CBF9142C22BAC91C5F6562BC15B6755794D5073ACA027E76878C1AEE86949ABA0CA4257F03253B32A2C9A9171CFA431259A3EC937A424D7F4F205504B070852639A522B010000E0020000504B010214001400080808004954785752639A522B010000E00200000300000000000000000000000000000000007A6970504B05060000000001000100310000005C0100000000, 0x504B030414000808080049547857000000000000000000000000030000007A6970A58F414BC4301085FFCB9C03B69EA4B74559141465295E9622B3CD743798263199C82EA5FFDD49EB82772F19DECB9BEF25FB098C86A656C09740D0C063DBBE81028763513B4AC1BB44B55827424D3141B39FE0932E725B4E05DF6873C9AE5301393C583106B48914684A7D34818D77122A0AE64EC1C16B414CCB6CD7EAEDEBEEE5E361D36E0432443FBE17E03FFB98124B2660C4F1B786E95CAC485FD94492CF73CCB2371AF74CEEC82768EE44E1F9AAEA4A41EF1D93E33F849B60D1B8A5BEF79AAE94C1587A12A6CBD6CEDDAC2031724EF74B046EAB0ACA0B07CC96B7168FEBDADCFD00504B0708FF595995E400000087010000504B0102140014000808080049547857FF595995E4000000870100000300000000000000000000000000000000007A6970504B0506000000000100010031000000150100000000);

DELETE FROM `api_definition_custom_field` WHERE `api_id` in ('1003', '1004');
INSERT INTO api_definition_custom_field (api_id, field_id, value) VALUE ('1003', 'test_field', '["default", "default-1"]');
INSERT INTO api_definition_custom_field (api_id, field_id, value) VALUE ('1004', 'test_field', '[]');

DELETE FROM `custom_field` WHERE `id` in ('test_field', 'custom-field');
INSERT INTO custom_field (id, name, scene, type, remark, internal, scope_type, create_time, update_time, create_user, scope_id) VALUE
    ('test_field', '测试字段', 'API', 'MULTIPLE_SELECT', '', 0, 'PROJECT', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, 'admin', '100001100001'),
    ('custom-field', '测试字段', 'API', 'SELECT', '', 0, 'PROJECT', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, 'admin', '100001100001');

DELETE FROM `template` WHERE `id` in ('api-template-id', 'default-api-template-id');
INSERT INTO template (id, name, remark, internal, update_time, create_time, create_user, scope_type, scope_id, enable_third_part, scene) VALUES
 ('api-template-id', 'api-template', '', 0, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, 'admin', 'PROJECT', '100001100001', 0, 'API'),
 ('default-api-template-id', 'api-default-template', '', 0, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, 'admin', 'PROJECT', '100001100001', 0, 'API');


DELETE FROM `api_definition_mock` WHERE `id` in ('mock_1', 'mock_2', 'mock_3', 'mock_4','mock_5');
INSERT INTO `api_definition_mock` VALUES
                                      ('mock_1', 1641120000000, 1641120000000, 'user1', 'Mock 1', '[\"tag1\",\"tag2\"]', 1, 'EXPECT001', '100001100001', '1001', 200, null,'1'),
                                      ('mock_2', 1641121000000, 1641121000000, 'user2', 'Mock 2', '[\"tag2\",\"tag3\"]', 1, 'EXPECT002', '100001100001', '1002', 200, null,'1'),
                                      ('mock_3', 1641122000000, 1641122000000, 'user3', 'Mock 3', '[\"tag3\",\"tag4\"]', 1, 'EXPECT003', '100001100001', '1003', 200, null,'1'),
                                      ('mock_4', 1641123000000, 1641123000000, 'user1', 'Mock 4', '[\"tag4\",\"tag5\"]', 1, 'EXPECT004', '100001100001', '1005', 400, null,'1'),
                                      ('mock_5', 1641124000000, 1641124000000, 'user2', 'Mock 5', '[\"tag5\",\"tag1\"]', 1, 'EXPECT005', '100001100001', '1005', 400, null,'1');

DELETE FROM `api_definition_mock_config` WHERE `id` in ('mock_1', 'mock_2', 'mock_3', 'mock_4','mock_5');
INSERT INTO `api_definition_mock_config` VALUES
                                             ('mock_1', '{"type": "exact", "value": "request_value"}', '{"status": 200, "body": {"message": "Mock Response 1"}}'),
                                             ('mock_2', '{"type": "regex", "value": "\d{3}"}', '{"status": 404, "body": {"error": "Not Found"}}'),
                                             ('mock_3', '{"type": "contains", "value": "partial_value"}', '{"status": 500, "body": {"error": "Internal Server Error"}}'),
                                             ('mock_4', '{"type": "exact", "value": "another_exact_value"}', '{"status": 200, "body": {"data": "Another Mock Response"}}'),
                                             ('mock_5', '{"type": "jsonpath", "value": "$.items[0].name"}', '{"status": 200, "body": {"items": [{"name": "Item 1"}]}}');


DELETE FROM `api_report` WHERE `id` in ('1', '2', '3', '4','5','6', '7', '8', '9','10','11', '12', '13', '14','15','16', '17', '18', '19','20');
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('1', 'Test Report 1', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642000001, UNIX_TIMESTAMP() * 1000, 1642002000, 1000, 'SUCCESS', 'MANUAL', 'SEQUENTIAL', '100660357777795313', b'0', '100001100001', 'env_1', 0, 0, 0, 100, 150, 150, '50%', '10%', '5%', '80%', '90%', 'script_1',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('10', 'Test Report 10', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642400101, UNIX_TIMESTAMP() * 1000, 1642402100, 1000, 'ERROR', 'AUTOMATED', 'PARALLEL', '100660357777795313', b'0', '100001100001', 'env_5', 10, 5, 10, 85, 150, 145, '50%', '10%', '5%', '80%', '90%', 'script_10',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('11', 'Test Report 11', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642500001, UNIX_TIMESTAMP() * 1000, 1642502000, 1000, 'FAKE_ERROR', 'MANUAL', 'SEQUENTIAL', '100660357777795313', b'0', '100001100001', 'env_6', 0, 0, 0, 100, 150, 150, '50%', '10%', '5%', '80%', '90%', 'script_11',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('12', 'Test Report 12', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642500101, UNIX_TIMESTAMP() * 1000, 1642502100, 1000, 'ERROR', 'AUTOMATED', 'PARALLEL', '100660357777795313', b'0', '100001100001', 'env_6', 10, 5, 10, 85, 150, 145, '50%', '10%', '5%', '80%', '90%', 'script_12',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('13', 'Test Report 13', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642600001, UNIX_TIMESTAMP() * 1000, 1642602000, 1000, 'SUCCESS', 'MANUAL', 'SEQUENTIAL', '100660357777795313', b'0', '100001100001', 'env_7', 0, 0, 0, 100, 150, 150, '50%', '10%', '5%', '80%', '90%', 'script_13',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('14', 'Test Report 14', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642600101, UNIX_TIMESTAMP() * 1000, 1642602100, 1000, 'FAKE_ERROR', 'AUTOMATED', 'PARALLEL', '100660357777795313', b'0', '100001100001', 'env_7', 10, 5, 10, 85, 150, 145, '50%', '10%', '5%', '80%', '90%', 'script_14',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('15', 'Test Report 15', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642700001, UNIX_TIMESTAMP() * 1000, 1642702000, 1000, 'SUCCESS', 'MANUAL', 'SEQUENTIAL', '100660357777795313', b'0', '100001100001', 'env_8', 0, 0, 0, 100, 150, 150, '50%', '10%', '5%', '80%', '90%', 'script_15',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('16', 'Test Report 16', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642700101, UNIX_TIMESTAMP() * 1000, 1642702100, 1000, 'SUCCESS', 'AUTOMATED', 'PARALLEL', '100660357777795313', b'0', '100001100001', 'env_8', 10, 5, 10, 85, 150, 145, '50%', '10%', '5%', '80%', '90%', 'script_16',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('17', 'Test Report 17', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642800001, UNIX_TIMESTAMP() * 1000, 1642802000, 1000, 'SUCCESS', 'MANUAL', 'SEQUENTIAL', '100660357777795313', b'0', '100001100001', 'env_9', 0, 0, 0, 100, 150, 150, '50%', '10%', '5%', '80%', '90%', 'script_17',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('18', 'Test Report 18', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642800101, UNIX_TIMESTAMP() * 1000, 1642802100, 1000, 'ERROR', 'AUTOMATED', 'PARALLEL', '100660357777795313', b'0', '100001100001', 'env_9', 10, 5, 10, 85, 150, 145, '50%', '10%', '5%', '80%', '90%', 'script_18',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('19', 'Test Report 19', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642900001, UNIX_TIMESTAMP() * 1000, 1642902000, 1000, 'FAKE_ERROR', 'MANUAL', 'SEQUENTIAL', '100660357777795313', b'0', '100001100001', 'env_10', 0, 0, 0, 100, 150, 150, '50%', '10%', '5%', '80%', '90%', 'script_19',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('2', 'Test Report 2', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642000101, UNIX_TIMESTAMP() * 1000, 1642002100, 1000, 'ERROR', 'AUTOMATED', 'PARALLEL', '100660357777795313', b'0', '100001100001', 'env_1', 10, 5, 10, 85, 150, 145, '50%', '10%', '5%', '80%', '90%', 'script_2',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('20', 'Test Report 20', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642900101, UNIX_TIMESTAMP() * 1000, 1642902100, 1000, 'ERROR', 'AUTOMATED', 'PARALLEL', '100660357777795313', b'0', '100001100001', 'env_10', 10, 5, 10, 85, 150, 145, '50%', '10%', '5%', '80%', '90%', 'script_20',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('3', 'Test Report 3',  'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642100001, UNIX_TIMESTAMP() * 1000, 1642102000, 1000, 'SUCCESS', 'MANUAL', 'SEQUENTIAL', '100660357777795313', b'0', '100001100001', 'env_2', 0, 0, 0, 100, 150, 150, '50%', '10%', '5%', '80%', '90%', 'script_3',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('4', 'Test Report 4',  'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642100101, UNIX_TIMESTAMP() * 1000, 1642102100, 1000, 'ERROR', 'AUTOMATED', 'PARALLEL', '100660357777795313', b'1', '100001100001', 'env_2', 10, 5, 10, 85, 150, 145, '50%', '10%', '5%', '80%', '90%', 'script_4',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('5', 'Test Report 5',  'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642200001, UNIX_TIMESTAMP() * 1000, 1642202000, 1000, 'SUCCESS', 'MANUAL', 'SEQUENTIAL', '100660357777795313', b'1', '100001100001', 'env_3', 0, 0, 0, 100, 150, 150, '50%', '10%', '5%', '80%', '90%', 'script_5',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('6', 'Test Report 6',  'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642200101, UNIX_TIMESTAMP() * 1000, 1642202100, 1000, 'ERROR', 'AUTOMATED', 'PARALLEL', '100660357777795313', b'1', '100001100001', 'env_3', 10, 5, 10, 85, 150, 145, '50%', '10%', '5%', '80%', '90%', 'script_6',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('7', 'Test Report 7',  'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642300001, UNIX_TIMESTAMP() * 1000, 1642302000, 1000, 'SUCCESS', 'MANUAL', 'SEQUENTIAL', '100660357777795313', b'1', '100001100001', 'env_4', 0, 0, 0, 100, 150, 150, '50%', '10%', '5%', '80%', '90%', 'script_7',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('8', 'Test Report 8',  'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642300101, UNIX_TIMESTAMP() * 1000, 1642302100, 1000, 'ERROR', 'AUTOMATED', 'PARALLEL', '100660357777795313', b'1', '100001100001', 'env_4', 10, 5, 10, 85, 150, 145, '50%', '10%', '5%', '80%', '90%', 'script_8',0);
INSERT INTO `api_report` (`id`, `name`, `test_plan_case_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('9', 'Test Report 9',  'NONE', 'admin', NULL, NULL, b'0', 'admin', 1642400001, UNIX_TIMESTAMP() * 1000, 1642402000, 1000, 'SUCCESS', 'MANUAL', 'SEQUENTIAL', '100660357777795313', b'1', '100001100001', 'env_5', 0, 0, 0, 100, 150, 150, '50%', '10%', '5%', '80%', '90%', 'script_9',0);

DELETE FROM `api_test_case_record` WHERE `api_report_id` in ('1', '2', '3', '4','5','6', '7', '8', '9','10','11', '12', '13', '14','15','16', '17', '18', '19','20');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('1', '12df5721-c5e6-a38b-e999-3eafcb992094');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('10', '12df5721-c5e6-a38b-e999-3eafcb992100');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('11', '12df5721-c5e6-a38b-e999-3eafcb992233');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('12', '3ee2ae9c-a680-4ed6-b115-1f6ab8980100');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('13', '3ee2ae9c-a680-4ed6-b115-1f6ab8980104');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('14', '3ee2ae9c-a680-4ed6-b115-1f6ab8980545');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('15', '3ee2ae9c-a680-4ed6-b115-1f6ab8980553');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('16', '3ee2ae9c-a680-4ed6-b115-1f6ab8980589');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('17', '3ee2ae9c-a680-4ed6-b115-1f6ab8980973');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('18', '12df5721-c5e6-a38b-e999-3eafcb992094');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('19', '12df5721-c5e6-a38b-e999-3eafcb992100');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('2', '12df5721-c5e6-a38b-e999-3eafcb992233');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('20', '3ee2ae9c-a680-4ed6-b115-1f6ab8980100');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('3', '3ee2ae9c-a680-4ed6-b115-1f6ab8980104');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('4', 'resource_4');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('5', 'resource_5');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('6', 'resource_6');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('7', 'resource_7');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('8', 'resource_8');
INSERT INTO `api_test_case_record` (`api_report_id`, `api_test_case_id`) VALUES ('9', 'resource_9');

DELETE FROM `test_resource_pool` WHERE `id` in ('100660357777795313');
INSERT INTO `test_resource_pool` (`id`, `name`, `type`, `description`, `enable`, `create_time`, `update_time`, `create_user`, `server_url`, `all_org`, `deleted`) VALUES ('100660357777795313', 'LOCAL', 'Node', '测试资源池', b'1', 1705894549000, 1705894549000, 'admin', NULL, b'1', b'0');

DELETE FROM `api_scenario` WHERE `id` in ('1', '2', '3', '4','5','6', '7', '8', '9');
INSERT INTO `api_scenario` (`id`, `name`, `priority`, `status`, `step_total`, `request_pass_rate`, `last_report_status`, `last_report_id`, `num`, `deleted`, `pos`, `version_id`, `ref_id`, `latest`, `project_id`, `module_id`, `description`, `tags`, `grouped`, `environment_id`, `create_user`, `create_time`, `delete_time`, `delete_user`, `update_user`, `update_time`) VALUES ('1', 'Scenario 1', 'P0', 'Completed', 10, '95%', 'Passed', 'report_1', 1001, b'0', 1, 'version_1', 'ref_1', b'1', '100001100001', 'module_1', 'Description 1', '[\"tag1\",\"tag2\"]', b'0', 'env_1', 'admin', 1640772861000, NULL, NULL, 'admin', 1640772861000);
INSERT INTO `api_scenario` (`id`, `name`, `priority`, `status`, `step_total`, `request_pass_rate`, `last_report_status`, `last_report_id`, `num`, `deleted`, `pos`, `version_id`, `ref_id`, `latest`, `project_id`, `module_id`, `description`, `tags`, `grouped`, `environment_id`, `create_user`, `create_time`, `delete_time`, `delete_user`, `update_user`, `update_time`) VALUES ('2', 'Scenario 2', 'P1', 'In Progress', 15, '80%', 'Running', 'report_2', 1002, b'0', 2, 'version_2', 'ref_2', b'0', '100001100001', 'module_2', 'Description 2', '[\"tag2\",\"tag3\"]', b'0', NULL, 'admin', 1640772862000, NULL, NULL, 'admin', 1640772862000);
INSERT INTO `api_scenario` (`id`, `name`, `priority`, `status`, `step_total`, `request_pass_rate`, `last_report_status`, `last_report_id`, `num`, `deleted`, `pos`, `version_id`, `ref_id`, `latest`, `project_id`, `module_id`, `description`, `tags`, `grouped`, `environment_id`, `create_user`, `create_time`, `delete_time`, `delete_user`, `update_user`, `update_time`) VALUES ('3', 'Scenario 3', 'P2', 'Not Planned', 8, 'Calculating', 'PENDING', NULL, 1003, b'0', 3, 'version_3', 'ref_3', b'1', '100001100001', 'module_3', 'Description 3', '[\"tag1\",\"tag3\"]', b'1', 'env_2', 'admin', 1640772863000, NULL, NULL, 'admin', 1640772863000);
INSERT INTO `api_scenario` (`id`, `name`, `priority`, `status`, `step_total`, `request_pass_rate`, `last_report_status`, `last_report_id`, `num`, `deleted`, `pos`, `version_id`, `ref_id`, `latest`, `project_id`, `module_id`, `description`, `tags`, `grouped`, `environment_id`, `create_user`, `create_time`, `delete_time`, `delete_user`, `update_user`, `update_time`) VALUES ('4', 'Scenario 4', 'P1', 'Completed', 12, '90%', 'Passed', 'report_4', 1004, b'0', 4, 'version_4', 'ref_4', b'0', '100001100001', 'module_4', 'Description 4', '[\"tag1\",\"tag2\"]', b'0', NULL, 'admin', 1640772864000, NULL, NULL, 'admin', 1640772864000);
INSERT INTO `api_scenario` (`id`, `name`, `priority`, `status`, `step_total`, `request_pass_rate`, `last_report_status`, `last_report_id`, `num`, `deleted`, `pos`, `version_id`, `ref_id`, `latest`, `project_id`, `module_id`, `description`, `tags`, `grouped`, `environment_id`, `create_user`, `create_time`, `delete_time`, `delete_user`, `update_user`, `update_time`) VALUES ('5', 'Scenario 5', 'P0', 'In Progress', 18, '75%', 'Running', 'report_5', 1005, b'0', 5, 'version_5', 'ref_5', b'1', '100001100001', 'module_5', 'Description 5', '[\"tag2\",\"tag3\"]', b'1', 'env_3', 'admin', 1640772865000, NULL, NULL, 'admin', 1640772865000);
INSERT INTO `api_scenario` (`id`, `name`, `priority`, `status`, `step_total`, `request_pass_rate`, `last_report_status`, `last_report_id`, `num`, `deleted`, `pos`, `version_id`, `ref_id`, `latest`, `project_id`, `module_id`, `description`, `tags`, `grouped`, `environment_id`, `create_user`, `create_time`, `delete_time`, `delete_user`, `update_user`, `update_time`) VALUES ('6', 'Scenario 6', 'P2', 'Not Planned', 10, 'Calculating', 'PENDING', NULL, 1006, b'0', 6, 'version_6', 'ref_6', b'0', '100001100001', 'module_6', 'Description 6', '[\"tag1\",\"tag3\"]', b'0', NULL, 'admin', 1640772866000, NULL, NULL, 'admin', 1640772866000);
INSERT INTO `api_scenario` (`id`, `name`, `priority`, `status`, `step_total`, `request_pass_rate`, `last_report_status`, `last_report_id`, `num`, `deleted`, `pos`, `version_id`, `ref_id`, `latest`, `project_id`, `module_id`, `description`, `tags`, `grouped`, `environment_id`, `create_user`, `create_time`, `delete_time`, `delete_user`, `update_user`, `update_time`) VALUES ('7', 'Scenario 7', 'P1', 'Completed', 14, '85%', 'Passed', 'report_7', 1007, b'0', 7, 'version_7', 'ref_7', b'1', '100001100001', 'module_7', 'Description 7', '[\"tag1\",\"tag2\"]', b'1', 'env_4', 'admin', 1640772867000, NULL, NULL, 'admin', 1640772867000);
INSERT INTO `api_scenario` (`id`, `name`, `priority`, `status`, `step_total`, `request_pass_rate`, `last_report_status`, `last_report_id`, `num`, `deleted`, `pos`, `version_id`, `ref_id`, `latest`, `project_id`, `module_id`, `description`, `tags`, `grouped`, `environment_id`, `create_user`, `create_time`, `delete_time`, `delete_user`, `update_user`, `update_time`) VALUES ('8', 'Scenario 8', 'P0', 'In Progress', 20, '70%', 'Running', 'report_8', 1008, b'0', 8, 'version_8', 'ref_8', b'0', '100001100001', 'module_8', 'Description 8', '[\"tag2\",\"tag3\"]', b'0', NULL, 'admin', 1640772868000, NULL, NULL, 'admin', 1640772868000);
INSERT INTO `api_scenario` (`id`, `name`, `priority`, `status`, `step_total`, `request_pass_rate`, `last_report_status`, `last_report_id`, `num`, `deleted`, `pos`, `version_id`, `ref_id`, `latest`, `project_id`, `module_id`, `description`, `tags`, `grouped`, `environment_id`, `create_user`, `create_time`, `delete_time`, `delete_user`, `update_user`, `update_time`) VALUES ('9', 'Scenario 9', 'P2', 'Not Planned', 16, 'Calculating', 'PENDING', NULL, 1009, b'0', 9, 'version_9', 'ref_9', b'1', '100001100001', 'module_9', 'Description 9', '[\"tag1\",\"tag3\"]', b'1', 'env_5', 'admin', 1640772869000, NULL, NULL, 'admin', 1640772869000);

DELETE FROM `api_scenario_report` WHERE `id` in ('report_1', 'report_2', 'report_3', 'report_4','report_5','report_6', 'report_7', 'report_8', 'report_9','report_10','report_11', 'report_12', 'report_13', 'report_14','report_15','report_16', 'report_17', 'report_18', 'report_19','report_20');
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_1', 'Report 1', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640773000000, UNIX_TIMESTAMP() * 1000, 1640774000000, 5000, 'SUCCESS', 'Manual', 'Standalone', '100660357777795313', b'0', '100001100001', 'env_1', 2, 0, 0, 2, 20, 18, '95%', 'Calculating', 'Calculating', '90%', '90%', 'script_1',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_10', 'Report 10', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640773900000, UNIX_TIMESTAMP() * 1000, 1640774900000, 5000, 'SUCCESS', 'Manual', 'Standalone', '100660357777795313', b'0', '100001100001', 'env_10', 2, 0, 0, 2, 22, 20, '90%', 'Calculating', 'Calculating', '85%', '80%', 'script_10',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_11', 'Report 11', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640774000000, UNIX_TIMESTAMP() * 1000, 1640775000000, 5000, 'ERROR', 'Automated', 'Distributed', '100660357777795313', b'0', '100001100001', 'env_11', 3, 1, 0, 2, 28, 26, '75%', 'Calculating', 'Calculating', '80%', '85%', 'script_11',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_12', 'Report 12', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640774100000, UNIX_TIMESTAMP() * 1000, 1640775100000, 5000, 'PENDING', 'Automated', 'Standalone', '100660357777795313', b'1', '100001100001', 'env_12', 0, 0, 16, 16, 26, 24, 'Calculating', 'Calculating', 'Calculating', 'Calculating', 'Calculating', 'script_12',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_13', 'Report 13', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640774200000, UNIX_TIMESTAMP() * 1000, 1640775200000, 5000, 'SUCCESS', 'Manual', 'Standalone', '100660357777795313', b'0', '100001100001', 'env_13', 2, 0, 0, 2, 24, 22, '80%', 'Calculating', 'Calculating', '85%', '90%', 'script_13',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_14', 'Report 14', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640774300000, UNIX_TIMESTAMP() * 1000, 1640775300000, 5000, 'ERROR', 'Automated', 'Distributed', '100660357777795313', b'0', '100001100001', 'env_14', 3, 1, 0, 2, 20, 18, '70%', 'Calculating', 'Calculating', '75%', '80%', 'script_14',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_15', 'Report 15', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640774400000, UNIX_TIMESTAMP() * 1000, 1640775400000, 5000, 'PENDING', 'Automated', 'Standalone', '100660357777795313', b'1', '100001100001', 'env_15', 0, 0, 18, 18, 22, 20, 'Calculating','Calculating', 'Calculating',  'Calculating', 'Calculating', 'script_15',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_16', 'Report 16', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640774500000, UNIX_TIMESTAMP() * 1000, 1640775500000, 5000, 'SUCCESS', 'Manual', 'Standalone', '100660357777795313', b'0', '100001100001', 'env_16', 2, 0, 0, 2, 20, 18, '85%', 'Calculating', 'Calculating', '90%', '90%', 'script_16',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_17', 'Report 17', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640774600000, UNIX_TIMESTAMP() * 1000, 1640775600000, 5000, 'ERROR', 'Automated', 'Distributed', '100660357777795313', b'0', '100001100001', 'env_17', 3, 1, 0, 2, 26, 24, '75%', 'Calculating', 'Calculating', '80%', '85%', 'script_17',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_18', 'Report 18', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640774700000, UNIX_TIMESTAMP() * 1000, 1640775700000, 5000, 'PENDING', 'Automated', 'Standalone', '100660357777795313', b'1', '100001100001', 'env_18', 0, 0, 20, 20, 30, 28, 'Calculating', 'Calculating', 'Calculating', 'Calculating', 'Calculating', 'script_18',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_19', 'Report 19', 'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640774800000, UNIX_TIMESTAMP() * 1000, 1640775800000, 5000, 'SUCCESS', 'Manual', 'Standalone', '100660357777795313', b'0', '100001100001', 'env_19', 2, 0, 0, 2, 18, 16, '90%', 'Calculating', 'Calculating', '85%', '80%', 'script_19',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_2', 'Report 2',  'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640773100000, UNIX_TIMESTAMP() * 1000, 1640774100000, 5000, 'ERROR', 'Automated', 'Distributed', '100660357777795313', b'0', '100001100001', 'env_2', 3, 1, 0, 2, 22, 20, '80%', 'Calculating', 'Calculating', '85%', '90%', 'script_2',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_20', 'Report 20',  'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640774900000, UNIX_TIMESTAMP() * 1000, 1640775900000, 5000, 'ERROR', 'Automated', 'Distributed', '100660357777795313', b'0', '100001100001', 'env_20', 3, 1, 0, 2, 22, 20, '70%', 'Calculating', 'Calculating', '75%', '80%', 'script_20',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_3', 'Report 3',  'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640773200000, UNIX_TIMESTAMP() * 1000, 1640774200000, 5000, 'PENDING', 'Automated', 'Standalone', '100660357777795313', b'1', '100001100001', 'env_3', 0, 0, 10, 10, 30, 28, 'Calculating','Calculating', 'Calculating',  'Calculating', 'Calculating', 'script_3',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_4', 'Report 4',  'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640773300000, UNIX_TIMESTAMP() * 1000, 1640774300000, 5000, 'SUCCESS', 'Manual', 'Standalone', '100660357777795313', b'0', '100001100001', 'env_4', 2, 0, 0, 2, 18, 16, '90%','Calculating', 'Calculating',  '85%', '80%', 'script_4',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_5', 'Report 5',  'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640773400000, UNIX_TIMESTAMP() * 1000, 1640774400000, 5000, 'ERROR', 'Automated', 'Distributed', '100660357777795313', b'0', '100001100001', 'env_5', 3, 1, 0, 2, 24, 22, '70%', 'Calculating', 'Calculating', '75%', '80%', 'script_5',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_6', 'Report 6',  'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640773500000, UNIX_TIMESTAMP() * 1000, 1640774500000, 5000, 'PENDING', 'Automated', 'Standalone', '100660357777795313', b'1', '100001100001', 'env_6', 0, 0, 12, 12, 20, 18, 'Calculating','Calculating', 'Calculating',  'Calculating', 'Calculating', 'script_6',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_7', 'Report 7',  'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640773600000, UNIX_TIMESTAMP() * 1000, 1640774600000, 5000, 'SUCCESS', 'Manual', 'Standalone', '100660357777795313', b'0', '100001100001', 'env_7', 2, 0, 0, 2, 16, 14, '85%', 'Calculating', 'Calculating', '90%', '90%', 'script_7',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_8', 'Report 8',  'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640773700000, UNIX_TIMESTAMP() * 1000, 1640774700000, 5000, 'ERROR', 'Automated', 'Distributed', '100660357777795313', b'0', '100001100001', 'env_8', 3, 1, 0, 2, 20, 18, 'Calculating', 'Calculating', '70%', '75%', '80%', 'script_8',0);
INSERT INTO `api_scenario_report` (`id`, `name`, `test_plan_scenario_id`, `create_user`, `delete_time`, `delete_user`, `deleted`, `update_user`, `update_time`, `start_time`, `end_time`, `request_duration`, `status`, `trigger_mode`, `run_mode`, `pool_id`, `integrated`, `project_id`, `environment_id`, `error_count`, `fake_error_count`, `pending_count`, `success_count`, `assertion_count`, `assertion_success_count`, `request_error_rate`, `request_pending_rate`, `request_fake_error_rate`, `request_pass_rate`, `assertion_pass_rate`, `script_identifier`,`plan`) VALUES ('report_9', 'Report 9',  'NONE', 'admin', NULL, NULL, b'0', 'admin', 1640773800000, UNIX_TIMESTAMP() * 1000, 1640774800000, 5000, 'PENDING', 'Automated', 'Standalone', '100660357777795313', b'1', '100001100001', 'env_9', 0, 0, 14, 14, 18, 16, 'Calculating', 'Calculating', 'Calculating', 'Calculating', 'Calculating', 'script_9',0);



