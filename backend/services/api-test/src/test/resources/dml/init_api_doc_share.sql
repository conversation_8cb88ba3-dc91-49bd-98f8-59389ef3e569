-- 插入测试数据
INSERT INTO `api_definition` (`id`, `name`, `protocol`, `method`, `path`, `status`, `num`, `tags`, `pos`, `project_id`, `module_id`, `latest`, `version_id`, `ref_id`, `description`, `create_time`, `create_user`, `update_time`, `update_user`, `delete_user`, `delete_time`, `deleted`) VALUES
('doc-share-id', 'test1', 'HTTP', 'POST', '/api/admin/1', 'PROCESSING', 1001, '[\"test3\",\"te\"]', 1, '100001100001', 'root', b'1', '100570499574136985', '1001', NULL, 1699500298164, 'admin', 1699500298162, 'admin', NULL, NULL, b'0');
