<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.bug.mapper.ExtBugRelateCaseMapper">
    <select id="getRelateCaseModule" resultType="io.metersphere.system.dto.sdk.BaseTreeNode">
        select
        distinct mt.id,
        mt.parent_id as parentId,
        mt.name,
        mt.pos,
        mt.project_id
        from ${moduleTable} mt
        <if test="caseTable != null and caseTable != '' and caseTable != 'api_test_case'">
            left join ${caseTable} ct on ct.module_id = mt.id
        </if>
        <if test="caseTable != null and caseTable != '' and caseTable == 'api_test_case'">
            left join api_definition ad on ad.module_id = mt.id
            left join api_test_case ct on ct.api_definition_id = ad.id
        </if>
        where mt.project_id = #{request.projectId}
            and ct.id not in
            (
                select brc.case_id from bug_relation_case brc where brc.bug_id = #{request.sourceId} and brc.case_type = #{request.sourceType}
            )
        <include refid="queryModuleWhereCondition"/>
        order by mt.pos
    </select>

    <select id="countRelateCaseModuleTree" resultType="io.metersphere.project.dto.ModuleCountDTO">
        select
        <if test="caseTable != null and caseTable != '' and caseTable == 'api_test_case'">
            ad.module_id as moduleId,
            count(ad.id) as dataCount
        </if>
        <if test="caseTable != null and caseTable != '' and caseTable != 'api_test_case'">
            ct.module_id as moduleId,
            count(ct.id) as dataCount
        </if>
        from ${caseTable} ct
        <if test="caseTable != null and caseTable != '' and caseTable == 'api_test_case'">
            join api_definition ad on ct.api_definition_id = ad.id
        </if>
        where ct.deleted = #{deleted}
        and ct.project_id = #{request.projectId}
        and ct.id not in
        (
            select brc.case_id from bug_relation_case brc where brc.bug_id = #{request.sourceId} and brc.case_type = #{request.sourceType}
        )
        <include refid="queryModuleWhereCondition"/>
        <if test="caseTable != null and caseTable != '' and caseTable == 'api_test_case'">
            group by ad.module_id
        </if>
        <if test="caseTable != null and caseTable != '' and caseTable != 'api_test_case'">
            group by ct.module_id
        </if>
    </select>

    <select id="countRelationCases" resultType="io.metersphere.bug.dto.response.BugRelateCaseCountDTO">
        select count(distinct case_id) as relationCaseCount, bug_id as bugId from bug_relation_case brc
        where brc.bug_id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        group by brc.bug_id
    </select>

    <select id="list" resultType="io.metersphere.bug.dto.response.BugRelateCaseDTO">
        select * from (
            <!-- 功能用例 -->
            select brc.id relateId, brc.create_time, brc.case_id relateCaseId,
            fc.num relateCaseNum, fc.name relateCaseName, fc.project_id projectId, fc.version_id versionId,
            brc.case_type relateCaseType, max(brc.test_plan_id) is not null relatePlanCase
            from bug_relation_case brc
            join functional_case fc on (brc.case_id = fc.id and fc.deleted = false)
            where brc.bug_id = #{request.bugId}
            <if test="request.keyword != null and request.keyword != ''">
                and (
                    fc.name like concat('%', #{request.keyword}, '%')
                    or fc.num like concat('%', #{request.keyword}, '%')
                )
            </if>
            group by brc.case_id
            union all
            <!-- 接口用例 -->
            select brc.id relateId, brc.create_time, brc.case_id relateCaseId,
            atc.num relateCaseNum, atc.name relateCaseName, atc.project_id projectId, atc.version_id versionId,
            brc.case_type relateCaseType, max(brc.test_plan_id) is not null relatePlanCase
            from bug_relation_case brc
            join api_test_case atc on (brc.case_id = atc.id and atc.deleted = false)
            where brc.bug_id = #{request.bugId}
            <if test="request.keyword != null and request.keyword != ''">
                and (
                    atc.name like concat('%', #{request.keyword}, '%')
                    or atc.num like concat('%', #{request.keyword}, '%')
                )
            </if>
            group by brc.case_id
            union all
            <!-- 接口场景用例 -->
            select brc.id relateId, brc.create_time, brc.case_id relateCaseId,
            ao.num relateCaseNum, ao.name relateCaseName, ao.project_id projectId, ao.version_id versionId,
            brc.case_type relateCaseType, max(brc.test_plan_id) is not null relatePlanCase
            from bug_relation_case brc
            join api_scenario ao on (brc.case_id = ao.id and ao.deleted = false)
            where brc.bug_id = #{request.bugId}
            <if test="request.keyword != null and request.keyword != ''">
                and (
                    ao.name like concat('%', #{request.keyword}, '%')
                    or ao.num like concat('%', #{request.keyword}, '%')
                )
            </if>
            group by brc.case_id
        ) tmp
        order by tmp.create_time desc
    </select>

    <select id="getRelateCase" resultType="io.metersphere.bug.dto.response.BugRelateCaseDTO">
        select distinct c.name relateCaseName, c.project_id projectId, c.version_id versionId, brc.case_type relateCaseType, c.num relateId,
        brc.test_plan_id is not null relatePlanCase, brc.case_id is not null relateCase
        from bug_relation_case brc
            <if test="sourceType == 'FUNCTIONAL'">
                join functional_case c on (brc.case_id = c.id or brc.test_plan_case_id = c.id)
            </if>
            <!-- 后续根据SourceType扩展 -->
        where c.id = #{id} and c.deleted = false
    </select>

    <select id="getAssociateBugs" resultType="io.metersphere.dto.BugProviderDTO">
        SELECT
            brc.id as id,
            brc.bug_id bugId,
            b.num as num,
            b.title as name,
            b.handle_user handleUser,
            b.create_user createUser,
            createUser.name AS createUserName,
            b.`status`,
            bc.description as content,
            brc.test_plan_id testPlanId,
            tp.name testPlanName
        FROM
            bug_relation_case brc
                INNER JOIN bug b ON brc.bug_id = b.id
                INNER JOIN bug_content bc ON brc.bug_id = bc.bug_id
        LEFT JOIN user createUser ON b.create_user = createUser.id
        left join test_plan tp on brc.test_plan_id = tp.id
        <where>
            <include refid="queryWhereConditionByProvider"/>
            <include refid="filter"/>
            order by
            <if test="sort != null and sort != ''">
                brc.${sort}
            </if>
            <if test="sort == null or sort == ''">
                brc.create_time desc
            </if>
        </where>
    </select>

    <select id="countByCaseId" resultType="java.lang.Long">
        SELECT count(fc.id)
        FROM bug_relation_case brc
                 INNER JOIN functional_case fc ON brc.case_id = fc.id
        where brc.bug_id = #{caseId}
          AND fc.deleted = false
    </select>

    <sql id="queryWhereConditionByProvider">
        <if test="request.caseId != null and request.caseId != ''">
            and brc.case_id = #{request.caseId}
            and brc.test_plan_id is null
        </if>
        <if test="request.testPlanCaseId != null and request.testPlanCaseId != ''">
            and brc.case_id = #{request.testPlanCaseId}
            and brc.test_plan_id is not null
        </if>
        <if test="request.keyword != null and request.keyword != ''">
            and b.title like concat('%', #{request.keyword},'%')
        </if>
    </sql>

    <sql id="filter">
        <if test="request.filter != null and request.filter.size() > 0">
            <foreach collection="request.filter.entrySet()" index="key" item="values">
                <if test="values != null and values.size() > 0">
                    <choose>
                        <!-- 处理人 -->
                        <when test="key == 'handleUser'">
                            and b.handle_user in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                        </when>
                        <!-- 删除人 -->
                        <when test="key == 'deleteUser'">
                            and b.delete_user in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                        </when>
                        <!-- 更新人 -->
                        <when test="key == 'updateUser'">
                            and b.update_user in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                        </when>
                        <!-- 创建人 -->
                        <when test="key == 'createUser'">
                            and b.create_user in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                        </when>
                        <!-- 平台 -->
                        <when test="key == 'platform'">
                            and b.platform in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                        </when>
                        <!-- 状态 -->
                        <when test="key == 'status'">
                            and b.status in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                        </when>
                        <!-- 自定义单选字段 -->
                        <when test="key.startsWith('custom_single')">
                            and b.id in (
                            select bug_id from bug_custom_field where concat('custom_single_', field_id) = #{key}
                            and trim(both '"' from `value`) in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                            )
                        </when>
                        <!-- 自定义多选字段 -->
                        <when test="key.startsWith('custom_multiple')">
                            and b.id in (
                            select bug_id from bug_custom_field where concat('custom_multiple_', field_id) = #{key}
                            and
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterMultipleWrapper"/>
                            )
                        </when>
                    </choose>
                </if>
            </foreach>
        </if>
    </sql>

    <sql id="queryModuleWhereCondition">
        <!-- 待补充关联Case弹窗中的高级搜索条件filter, combine -->
        <if test="request.keyword != null and request.keyword != ''">
            and (
                ct.num like concat('%', #{request.keyword}, '%')
                or ct.name like concat('%', #{request.keyword}, '%')
                or ct.tags like concat('%', #{request.keyword}, '%')
            )
        </if>
        <if test="request.moduleIds != null and request.moduleIds.size() > 0">
            <if test="caseTable != null and caseTable != '' and caseTable == 'api_test_case'">
                and ad.module_id in
                <foreach collection="request.moduleIds" item="moduleId" open="(" separator="," close=")">
                    #{moduleId}
                </foreach>
            </if>
            <if test="caseTable != null and caseTable != '' and caseTable != 'api_test_case'">
                and ct.module_id in
                <foreach collection="request.moduleIds" item="moduleId" open="(" separator="," close=")">
                    #{moduleId}
                </foreach>
            </if>
        </if>
    </sql>

    <select id="getBugCountByIds" resultType="io.metersphere.bug.dto.CaseRelateBugDTO">
        SELECT
            bug.id as bugId,
            bug.num as num,
            bug.status as status,
            bug.title as title,
            bug_relation_case.case_type as caseType,
            bug_relation_case.case_id as caseId,
            bug_relation_case.id
        FROM
            bug_relation_case
                INNER JOIN bug ON bug.id = bug_relation_case.bug_id
        WHERE
            bug_relation_case.test_plan_case_id IN
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        and bug_relation_case.test_plan_id = #{testPlanId}
        ORDER BY
            bug.pos DESC
    </select>
    <select id="getTestPlanAssociateBugs" resultType="io.metersphere.dto.BugProviderDTO">
        SELECT
        brc.id as id,
        brc.bug_id bugId,
        b.num as num,
        b.title as name,
        b.handle_user handleUser,
        b.create_user createUser,
        createUser.name AS createUserName,
        b.`status`,
        bc.description as content,
        brc.test_plan_id testPlanId,
        tp.name testPlanName
        FROM
        bug_relation_case brc
        INNER JOIN bug b ON brc.bug_id = b.id
        INNER JOIN bug_content bc ON brc.bug_id = bc.bug_id
        LEFT JOIN test_plan tp on brc.test_plan_id = tp.id
        LEFT JOIN user createUser ON b.create_user = createUser.id
        <where>
            b.deleted = false
            <if test="request.testPlanCaseId != null and request.testPlanCaseId != ''">
                and brc.test_plan_case_id = #{request.testPlanCaseId}
            </if>
            <if test="request.keyword != null and request.keyword != ''">
                and b.title like concat('%', #{request.keyword},'%')
            </if>
            <include refid="filter"/>
            order by
            <if test="sort != null and sort != ''">
                brc.${sort}
            </if>
            <if test="sort == null or sort == ''">
                brc.create_time desc
            </if>
        </where>
    </select>

    <select id="getPlanRelateBugIds" resultType="java.lang.String">
        select distinct bug_id from bug_relation_case where test_plan_id = #{id}
    </select>
</mapper>