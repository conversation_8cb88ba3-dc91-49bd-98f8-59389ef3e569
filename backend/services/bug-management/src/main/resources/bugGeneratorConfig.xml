<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >
<generatorConfiguration>
    <!--配置数据库连接的位置-->
    <properties url="file:///opt/metersphere/conf/metersphere.properties"/>
    <!-- 设置mysql驱动路径 -->
    <!--<classPathEntry location="/Users/<USER>/.m2/repository/mysql/mysql-connector-java/5.1.34/mysql-connector-java-5.1.34.jar"/>-->
    <!-- 此处指定生成针对MyBatis3的DAO -->
    <context id="mysql" targetRuntime="MyBatis3" defaultModelType="flat">
        <!-- 字段带`,解决列表跟关键字冲突问题 -->
        <property name="autoDelimitKeywords" value="true"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>
        <plugin type="io.metersphere.tools.mybatis.swagger.ImportSwagger3Plugin"/>
        <plugin type="io.metersphere.tools.mybatis.validation.ImportValidatorPlugin"/>
        <plugin type="com.itfsw.mybatis.generator.plugins.ModelColumnPlugin"/>
        <plugin type="com.itfsw.mybatis.generator.plugins.BatchInsertPlugin"/>

        <!-- Lombok插件 -->
        <plugin type="com.itfsw.mybatis.generator.plugins.LombokPlugin">
            <!-- @Data 默认开启,同时插件会对子类自动附加@EqualsAndHashCode(callSuper = true)，@ToString(callSuper = true) -->
            <property name="@Data" value="true"/>
            <!-- @Builder 必须在 Lombok 版本 >= 1.18.2 的情况下开启，对存在继承关系的类自动替换成@SuperBuilder -->
            <property name="@Builder" value="false"/>
            <!-- @NoArgsConstructor 和 @AllArgsConstructor 使用规则和Lombok一致 -->
            <property name="@AllArgsConstructor" value="false"/>
            <property name="@NoArgsConstructor" value="false"/>
            <!-- @Getter、@Setter、@Accessors 等使用规则参见官方文档 -->
            <property name="@Accessors(chain = true)" value="false"/>
            <!-- 临时解决IDEA工具对@SuperBuilder的不支持问题，开启后(默认未开启)插件在遇到@SuperBuilder注解时会调用ModelBuilderPlugin来生成相应的builder代码 -->
            <property name="supportSuperBuilderForIdea" value="false"/>
        </plugin>


        <!-- 用来除去时间信息的，这在配合类似subversion的代码管理工具时使用很有效，因为可以减少没有必要的注释迁入 -->
        <commentGenerator type="io.metersphere.tools.mybatis.validation.SchemaAnnotationGenerator">
            <property name="suppressDate" value="true"/>
            <!--  关闭自动生成的注释  -->
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>


        <!-- jdbc连接信息 --> <!-- EduLoanManage EduTestDataBase -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="${spring.datasource.url}&amp;nullCatalogMeansCurrent=true"
                        userId="${spring.datasource.username}"
                        password="${spring.datasource.password}"/>

        <!-- javaTypeResolver式类型转换的信息 -->
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>
        <!-- 模型对象 -->
        <javaModelGenerator targetPackage="io.metersphere.bug.domain" targetProject="../../framework/domain/src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>
        <!-- XML映射文件 -->
        <sqlMapGenerator targetPackage="io.metersphere.bug.mapper" targetProject="../../framework/domain/src/main/java">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>
        <!-- 接口 -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="io.metersphere.bug.mapper"
                             targetProject="../../framework/domain/src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!--要生成的数据库表 -->
        <table tableName="bug">
            <columnOverride column="tags" javaType="java.util.List&lt;String&gt;" jdbcType="VARCHAR"
                            typeHandler="io.metersphere.handler.ListTypeHandler"/>
        </table>
<!--        <table tableName="bug_content" />-->
<!--        <table tableName="bug_follower" />-->
<!--        <table tableName="bug_comment" />-->
<!--        <table tableName="bug_local_attachment" />-->
<!--        <table tableName="bug_custom_field" />-->
<!--        <table tableName="bug_relation_case" />-->
<!--        <table tableName="bug_history" />-->



        <!--        要忽略的字段-->
<!--        <table tableName="bug">-->
<!--             <ignoreColumn column="handle_user"/>-->
<!--         </table>-->

        <!--        表名和关键字冲突-->
        <!--                <table tableName="group" delimitIdentifiers="true"></table>-->

    </context>
</generatorConfiguration>
