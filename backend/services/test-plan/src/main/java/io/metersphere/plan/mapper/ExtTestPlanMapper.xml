<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.plan.mapper.ExtTestPlanMapper">
    <resultMap id="testPlanBaseInfo" type="io.metersphere.plan.dto.response.TestPlanResponse">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="num" jdbcType="BIGINT" property="num"/>
        <result column="module_id" jdbcType="VARCHAR" property="moduleId"/>
        <result column="project_id" jdbcType="VARCHAR" property="projectId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="tags" jdbcType="VARCHAR" property="tags" typeHandler="io.metersphere.handler.ListTypeHandler"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="group_id" jdbcType="VARCHAR" property="groupId"/>
    </resultMap>

    <resultMap id="BaseResultMapDTO" type="io.metersphere.plan.dto.response.TestPlanResponse">
        <result column="tags" jdbcType="VARCHAR" property="tags" typeHandler="io.metersphere.handler.ListTypeHandler" />
    </resultMap>

    <resultMap id="BaseResultMap" type="io.metersphere.plan.domain.TestPlan">
        <result column="tags" jdbcType="VARCHAR" property="tags" typeHandler="io.metersphere.handler.ListTypeHandler" />
    </resultMap>


    <update id="updateDefaultGroupId">
        UPDATE test_plan
        SET group_id = 'NONE'
        WHERE group_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectIdByGroupId" resultType="java.lang.String">
        SELECT id FROM test_plan WHERE group_id = #{parentId}
    </select>
    <select id="selectByGroupIdList" resultType="java.lang.String">
        SELECT id FROM test_plan WHERE group_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByConditions"
            resultMap="BaseResultMapDTO">
        SELECT
        t.id,t.num,t.name,t.status,t.group_id,
        t.create_user AS createUser,
        createUser.name AS createUserName,
        t.create_time as createTime,
        t.module_id as moduleId,
        t.type,
        t.description,
        t.pos,
        t.planned_start_time AS plannedStartTime,
        t.planned_end_time AS plannedEndTime,
        t.actual_start_time AS actualStartTime,
        t.actual_end_time AS actualEndTime,
        t.tags
        FROM test_plan t
        INNER JOIN user createUser ON t.create_user = createUser.id
        WHERE t.project_id = #{request.projectId}
        <include refid="queryByTableRequest"/>
    </select>

    <select id="selectTodoByConditions" resultMap="BaseResultMapDTO">
        select
        t.id,t.num,t.name,t.status,t.group_id,
        t.create_user as createuser,
        createuser.name as createusername,
        t.create_time as createtime,
        t.module_id as moduleid,
        t.type,
        t.description,
        t.pos,
        t.planned_start_time as plannedstarttime,
        t.planned_end_time as plannedendtime,
        t.actual_start_time as actualstarttime,
        t.actual_end_time as actualendtime,
        t.tags
        from test_plan t
        inner join user createuser on t.create_user = createuser.id
        where t.project_id = #{request.projectid}
        <include refid="queryByTableRequest"/>
    </select>

    <select id="selectByGroupIds"
            resultMap="BaseResultMapDTO">
        SELECT
        t.id,t.num,t.name,t.status,t.group_id,
        t.create_user AS createUser,
        createUser.name AS createUserName,
        t.create_time as createTime,
        t.module_id as moduleId,
        t.type,
        t.description,
        t.pos,
        t.actual_start_time AS actualStartTime,
        t.actual_end_time AS actualEndTime,
        t.planned_start_time AS plannedStartTime,
        t.planned_end_time AS plannedEndTime,
        t.group_id AS parent,
        t.tags
        FROM test_plan t
        INNER JOIN user createUser ON t.create_user = createUser.id
        WHERE t.group_id IN
        <foreach collection="groupIds" item="groupId" separator="," open="(" close=")">
            #{groupId}
            </foreach>
        ORDER BY t.pos DESC
    </select>

    <sql id="queryByTableRequest">
        <if test="request.myTodo">
            <!-- 待办参数: 查询创建人为我的所有的独立计划或子计划(doneExcludeIds: 通过率达到阈值需排除) -->
            and t.type = 'TEST_PLAN'
            and t.create_user = #{request.myTodoUserId}
            <if test="request.doneExcludeIds != null and request.doneExcludeIds.size() > 0">
                and t.id not in
                <foreach collection="request.doneExcludeIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </if>
        <include refid="baseConditionQuery"/>

        <if test="request.keyword != null and request.keyword != ''">
            and (
            t.name like concat('%', #{request.keyword},'%')
            or t.num like concat('%', #{request.keyword},'%')
            or t.tags like concat('%', #{request.keyword}, '%')
            <if test="request.keywordFilterIds != null and request.keywordFilterIds.size() > 0">
                or t.id in
                <foreach collection="request.keywordFilterIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            )
        </if>
        <if test="request.innerIds != null and request.innerIds.size() > 0">
            and t.id in
            <foreach collection="request.innerIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        <!--<choose>
            <when test='request.searchMode == "AND"'>
                AND <include refid="queryCombine"/>
            </when>
            <when test='request.searchMode == "OR"'>
                and (
                <include refid="queryCombine"/>
                )
            </when>
        </choose>-->
        <if test="request.combineInnerIds != null and request.combineInnerIds.size() > 0">
            and t.id
            <if test="request.combineOperator != 'NOT_IN'">
                 in
            </if>
            <if test="request.combineOperator == 'NOT_IN'">
                not in
            </if>
            <foreach collection="request.combineInnerIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <include refid="filters">
            <property name="filter" value="request.filter"/>
        </include>
        <include refid="combine">
            <property name="combineSearch" value="request.combineSearch"/>
        </include>
    </sql>


    <sql id="filters">
        <if test="${filter} != null and ${filter}.size() > 0">
            <foreach collection="${filter}.entrySet()" index="key" item="values">
                <if test="values != null and values.size() > 0">
                    <choose>
                        <!-- 状态 -->
                        <when test="key=='status'">
                            and t.status in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                        </when>
                    </choose>
                </if>
            </foreach>
        </if>
    </sql>



    <sql id="combine">
        <trim prefix="AND">
            <trim prefix="(" suffix=")" suffixOverrides="AND|OR">
                <if test="${combineSearch} != null">
                    <foreach collection="${combineSearch}.userViewConditions" item="condition">
                        <if test="condition.name == 'createUser'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                <property name="condition" value="condition"/>
                                <property name="column" value="t.create_user"/>
                            </include>
                        </if>
                        <if test="condition.name == 'follower'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.associationCondition">
                                <property name="mainIdColumn" value="t.id"/>
                                <property name="associationTable" value="test_plan_follower"/>
                                <property name="associationIdColumn" value="test_plan_id"/>
                                <property name="searchColumn" value="user_id"/>
                                <property name="condition" value="condition"/>
                            </include>
                        </if>
                        <if test="condition.name == 'archived'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                <property name="condition" value="condition"/>
                                <property name="column" value="t.status"/>
                            </include>
                        </if>
                        <include refid="io.metersphere.system.mapper.BaseMapper.queryType">
                            <property name="searchMode" value="${combineSearch}.searchMode"/>
                        </include>
                    </foreach>
                </if>
            </trim>
        </trim>

        <trim prefix="AND">
            <trim prefix="(" suffix=")" suffixOverrides="AND|OR">
                <if test="${combineSearch} != null">
                    <foreach collection="${combineSearch}.systemFieldConditions" item="condition">
                        <if test="condition.name == 'moduleId'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                <property name="condition" value="condition"/>
                                <property name="column" value="t.module_id"/>
                            </include>
                        </if>
                        <if test="condition.name == 'name'">
                            <trim prefix="(" suffix=")">
                                <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                    <property name="condition" value="condition"/>
                                    <property name="column" value="t.name"/>
                                </include>
                                <if test="request.type != 'TEST_PLAN'" >
                                    <if test="condition.operator == 'CONTAINS' or condition.operator == 'EQUALS'">
                                        or t.id in
                                        (
                                        SELECT group_id
                                        FROM test_plan
                                        WHERE project_id = #{request.projectId}
                                        AND
                                        <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                            <property name="condition" value="condition"/>
                                            <property name="column" value="test_plan.name"/>
                                        </include>
                                        )
                                    </if>
                                </if>
                            </trim>
                        </if>
                        <if test="condition.name == 'num'">
                            <trim prefix="(" suffix=")">
                                <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                    <property name="condition" value="condition"/>
                                    <property name="column" value="t.num"/>
                                </include>
                                <if test="request.type != 'TEST_PLAN'" >
                                    <if test="condition.operator == 'CONTAINS' or condition.operator == 'EQUALS'">
                                        or t.id in
                                        (
                                        SELECT group_id
                                        FROM test_plan
                                        WHERE project_id = #{request.projectId}
                                        AND
                                        <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                            <property name="condition" value="condition"/>
                                            <property name="column" value="test_plan.num"/>
                                        </include>
                                        )
                                    </if>
                                </if>
                            </trim>
                        </if>
                        <if test="condition.name == 'createUser'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                <property name="condition" value="condition"/>
                                <property name="column" value="t.create_user"/>
                            </include>
                        </if>
                        <if test="condition.name == 'updateUser'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                <property name="condition" value="condition"/>
                                <property name="column" value="t.update_user"/>
                            </include>
                        </if>
                        <if test="condition.name == 'updateTime'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                <property name="condition" value="condition"/>
                                <property name="column" value="t.update_time"/>
                            </include>
                        </if>
                        <if test="condition.name == 'createTime'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                <property name="condition" value="condition"/>
                                <property name="column" value="t.create_time"/>
                            </include>
                        </if>
                        <if test="condition.name == 'tags'">
                            <trim prefix="(" suffix=")">
                                <include refid="io.metersphere.system.mapper.BaseMapper.arrayValueCondition">
                                    <property name="condition" value="condition"/>
                                    <property name="column" value="t.tags"/>
                                </include>
                                <if test="request.type != 'TEST_PLAN'" >
                                    <if test="condition.operator == 'CONTAINS'">
                                        or t.id in
                                        (
                                        SELECT group_id
                                        FROM test_plan
                                        WHERE project_id = #{request.projectId}
                                        AND
                                        <include refid="io.metersphere.system.mapper.BaseMapper.arrayValueCondition">
                                            <property name="condition" value="condition"/>
                                            <property name="column" value="test_plan.tags"/>
                                        </include>
                                        )
                                    </if>
                                </if>
                            </trim>
                        </if>

                        <!-- 状态 -->
                        <if test="condition.name == 'status'">
                            status in
                            <foreach collection="condition.value" item="v" separator="," open="(" close=")">
                                #{v}
                            </foreach>
                        </if>
                        <include refid="io.metersphere.system.mapper.BaseMapper.queryType">
                            <property name="searchMode" value="${combineSearch}.searchMode"/>
                        </include>
                    </foreach>
                </if>
            </trim>
        </trim>
    </sql>



   <!-- <sql id="queryCombine">
        <if test="request.combine != null">
            <include refid="combine">
                <property name="condition" value="request.combine"/>
                <property name="searchMode" value="request.searchMode"/>
            </include>
        </if>
        1=1
    </sql>

    <sql id="combine">
        &lt;!&ndash; 名称 &ndash;&gt;
        <if test='${condition}.name != null'>
            t.name
            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                <property name="object" value="${condition}.name"/>
            </include>
            <include refid="queryType">
                <property name="searchMode" value="${searchMode}"/>
            </include>
        </if>
        &lt;!&ndash; id &ndash;&gt;
        <if test='${condition}.id != null'>
            t.num
            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                <property name="object" value="${condition}.id"/>
            </include>
            <include refid="queryType">
                <property name="searchMode" value="${searchMode}"/>
            </include>
        </if>
        &lt;!&ndash; 所属模块 &ndash;&gt;
        <if test='${condition}.moduleId != null'>
            t.moduleId
            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                <property name="object" value="${condition}.moduleId"/>
            </include>
            <include refid="queryType">
                <property name="searchMode" value="${searchMode}"/>
            </include>
        </if>
        &lt;!&ndash; 创建人 &ndash;&gt;
        <if test='${condition}.createUser != null'>
            t.create_user
            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                <property name="object" value="${condition}.createUser"/>
            </include>
            <include refid="queryType">
                <property name="searchMode" value="${searchMode}"/>
            </include>
        </if>
        &lt;!&ndash; 创建时间 &ndash;&gt;
        <if test='${condition}.createTime != null'>
            t.create_time
            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                <property name="object" value="${condition}.createTime"/>
            </include>
            <include refid="queryType">
                <property name="searchMode" value="${searchMode}"/>
            </include>
        </if>
        &lt;!&ndash; 标签 &ndash;&gt;
        <if test='${condition}.tags != null'>
            <include refid="queryTag">
                <property name="searchMode" value="${searchMode}"/>
                <property name="combineTag" value="${condition}.tags"/>
            </include>
        </if>
    </sql>-->

    <!--<sql id="queryType">
        <choose>
            <when test='${searchMode} == "AND"'>
                AND
            </when>
            <when test='${searchMode} == "OR"'>
                OR
            </when>
        </choose>
    </sql>-->


    <!--<sql id="queryTag">
        &lt;!&ndash; 不包含 &ndash;&gt;
        <if test='${combineTag}.value.size() > 0 and ${combineTag}.operator == "not like"'>
            (
            t.tags is null or t.tags = '[]' or
            <foreach collection="${combineTag}.value" item="tag" separator="and" open="(" close=")">
                !JSON_CONTAINS(t.tags, JSON_ARRAY(#{tag}))
            </foreach>
            )
            <include refid="queryType">
                <property name="searchMode" value="${searchMode}"/>
            </include>
        </if>
        &lt;!&ndash; 包含 &ndash;&gt;
        <if test='${combineTag}.value.size() > 0 and ${combineTag}.operator == "like"'>
            <foreach collection="${combineTag}.value" item="tag" separator="or" open="(" close=")">
                JSON_CONTAINS(t.tags, JSON_ARRAY(#{tag}))
            </foreach>
            <include refid="queryType">
                <property name="searchMode" value="${searchMode}"/>
            </include>
        </if>
        &lt;!&ndash; 空 &ndash;&gt;
        <if test='${combineTag}.operator == "is null"'>
            (t.tags is null or t.tags = '[]')
            <include refid="queryType">
                <property name="searchMode" value="${searchMode}"/>
            </include>
        </if>
    </sql>-->


    <select id="countModuleIdByConditions"
            resultType="io.metersphere.project.dto.ModuleCountDTO">
        SELECT t.module_id AS moduleId, count(t.id) AS dataCount
        FROM test_plan t
        INNER JOIN user createUser ON t.create_user = createUser.id
        WHERE t.project_id = #{request.projectId}
        <include refid="queryByTableRequest"/>
        GROUP BY t.module_id
    </select>
    <select id="selectIdByConditions"
            parameterType="io.metersphere.plan.dto.TestPlanQueryConditions"
            resultType="java.lang.String">
        SELECT
        t.id
        FROM test_plan t
        WHERE t.project_id = #{request.projectId}
        <include refid="queryByTestPlanQueryConditions"/>
    </select>
    <sql id="baseConditionQuery">
        <if test="request.moduleIds != null and request.moduleIds.size() > 0">
            and t.module_id in
            <foreach collection="request.moduleIds" item="moduleId" separator="," open="(" close=")">
                #{moduleId}
            </foreach>
        </if>
        <if test="request.type != null and request.type != ''">
            <choose>
                <when test="request.type == 'ALL'">
                    and t.group_id = 'NONE'
                </when>
                <when test="request.type == 'TEST_PLAN'">
                    and t.group_id = 'NONE'
                    and t.type = 'TEST_PLAN'
                </when>
                <when test="request.type == 'GROUP'">
                    and t.group_id = 'NONE'
                    and t.type = 'GROUP'
                </when>
            </choose>
        </if>
    </sql>
    <sql id="queryByTestPlanQueryConditions">
        <include refid="baseConditionQuery"/>
        <if test="request.condition.keyword != null and request.condition.keyword != ''">
            and (
            t.name like concat('%', #{request.condition.keyword},'%')
            or t.num like concat('%', #{request.condition.keyword},'%')
            or t.tags like concat('%', #{request.condition.keyword}, '%')
            )
        </if>
        <choose>
            <when test='request.condition.searchMode == "AND"'>
                AND <include refid="baseQueryCombine"/>
            </when>
            <when test='request.condition.searchMode == "OR"'>
                and (
                <include refid="baseQueryCombine"/>
                )
            </when>
        </choose>
        <include refid="filters">
            <property name="filter" value="request.condition.filter"/>
        </include>
    </sql>

    <sql id="baseQueryCombine">
        <if test="request.condition.combine != null">
            <include refid="combine">
                <property name="condition" value="request.condition.combine"/>
                <property name="searchMode" value="request.condition.searchMode"/>
            </include>
        </if>
        1=1
    </sql>


    <select id="selectGroupIdByConditions"
            parameterType="io.metersphere.plan.dto.TestPlanQueryConditions"
            resultType="java.lang.String">
        SELECT
        distinct t.group_id
        FROM test_plan t
        INNER JOIN user createUser ON t.create_user = createUser.id
        <include refid="test_plan_page_request"/>
        AND t.group_id != 'NONE'
    </select>

    <select id="selectBaseInfoByIds" resultType="io.metersphere.plan.domain.TestPlan">
        SELECT
        t.id,t.name,t.type,t.project_id,t.type
        FROM test_plan t
        WHERE t.id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectProjectIdByTestPlanId" resultType="java.lang.String">
        SELECT project_id
        FROM test_plan
        WHERE id = #{testPlanId}
    </select>

    <sql id="test_plan_page_request">
        WHERE t.project_id = #{projectId}
        <if test="groupId != null and groupId != ''">
            AND t.group_id = #{groupId}
            </if>
            <if test="condition.keyword != null and condition.keyword != ''">
                AND t.name like concat('%', #{condition.keyword}, '%')
            </if>
            <if test="moduleIds != null and moduleIds.size() != 0">
                AND t.module_id IN
                <foreach collection="moduleIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="hiddenIds != null and hiddenIds.size()>0">
                AND t.id NOT IN
                <foreach collection="hiddenIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        <if test="includeIds != null and includeIds.size() != 0">
            OR
            t.id IN
            <foreach collection="includeIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>


    <update id="batchUpdateStatus">
        UPDATE test_plan
        SET status = #{status}, update_user = #{userId}, update_time = #{updateTime}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="batchMove">
        UPDATE test_plan
        SET module_id = #{moduleId}, update_user = #{userId}, update_time = #{updateTime}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getTagsByIds" resultMap="BaseResultMap">
        select id, tags from test_plan
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectIdByProjectId" resultType="java.lang.String">
        SELECT id
        FROM test_plan
        WHERE project_id = #{0}
    </select>
    <select id="selectNotArchivedIds" resultType="java.lang.String">
        SELECT id FROM test_plan WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND status != 'ARCHIVED'
    </select>
    <select id="selectDragInfoById" resultType="io.metersphere.project.dto.DropNode">
        SELECT id, pos
        FROM test_plan
        WHERE id = #{0}
    </select>

    <select id="selectNodeByPosOperator"
            parameterType="io.metersphere.project.dto.NodeSortQueryParam"
            resultType="io.metersphere.project.dto.DropNode">
        SELECT id, pos
        FROM test_plan
        WHERE group_id = #{parentId}
        <if test="operator == 'moreThan'">
            AND pos &gt; #{pos}
        </if>
        <if test="operator == 'lessThan'">
            AND pos &lt; #{pos}
        </if>
        ORDER BY pos
        <if test="operator == 'lessThan' or operator == 'latest'">
            DESC
        </if>
        LIMIT 1
    </select>
    <select id="selectMaxPosByGroupId" resultType="java.lang.Long">
        SELECT IF(MAX(pos) IS NULL, 0, MAX(pos)) AS pos
        FROM test_plan
        WHERE group_id = #{0}
    </select>
    <select id="selectMaxPosByProjectIdAndGroupId" resultType="java.lang.Long">
        SELECT IF(MAX(pos) IS NULL, 0, MAX(pos)) AS pos
        FROM test_plan
        WHERE project_id = #{projectId}
          AND group_id = #{groupId}
    </select>
    <select id="selectRightfulIdsForExecute" resultType="java.lang.String">
        SELECT id FROM test_plan WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND status != 'ARCHIVED'
    </select>

    <select id="listHis" resultType="io.metersphere.plan.dto.TestPlanExecuteHisDTO">
        select et.id, from_unixtime(et.create_time / 1000, '%Y%m%d%H%i%s') as num, et.trigger_mode triggerMode, et.status execStatus, et.result execResult,
        et.create_user operationUser, et.start_time startTime, et.end_time endTime, arrt.report_id reportId
        from exec_task et left join api_report_relate_task arrt on et.id = arrt.task_resource_id
        where et.resource_id = #{request.testPlanId}
        <include refid="filterTask"/>
        order by et.create_time desc
    </select>

    <select id="selectGroupIdByKeyword" resultType="java.lang.String">
        SELECT t.group_id
        FROM test_plan t
        WHERE t.project_id = #{projectId}
          AND (
            t.name like concat('%', #{keyword}, '%')
                or t.num like concat('%', #{keyword}, '%')
                or t.tags like concat('%', #{keyword}, '%')
            )
    </select>
    <select id="countByGroupPlan" resultType="io.metersphere.plan.dto.TestPlanGroupCountDTO">
        SELECT group_id AS groupId, count(id) AS count
        FROM test_plan
        WHERE project_id = #{0}
          AND status != 'ARCHIVED'
          AND group_id != 'NONE'
        GROUP BY group_id;
    </select>
    <select id="selectIdByProjectIdAndWithoutList" resultType="java.lang.String">
        SELECT id
        FROM test_plan
        WHERE project_id = #{projectId}
        AND status != 'ARCHIVED'
        AND group_id = 'NONE'
        <if test="withoutList != null and withoutList.size() != ''">
            AND id not in
            <foreach collection="withoutList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <update id="batchUpdate">
        update test_plan
        <set>
            <if test="testPlan.tags != null and testPlan.tags != ''">
                tags = #{testPlan.tags,typeHandler=io.metersphere.handler.ListTypeHandler},
            </if>
            <if test="testPlan.updateUser != null and testPlan.updateUser != ''">
                update_user = #{testPlan.updateUser},
            </if>
            <if test="testPlan.updateTime != null">
                update_time = #{testPlan.updateTime},
            </if>
        </set>
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and project_id = #{testPlan.projectId}
    </update>
    <update id="setActualStartTime">
        update test_plan
        set actual_start_time = #{time}
        where id = #{id}
          and actual_start_time is null
    </update>
    <update id="setActualEndTime">
        update test_plan
        set actual_end_time = #{time}
        where id = #{id}
    </update>
    <update id="clearActualEndTime">
        update test_plan
        set actual_end_time = null
        where id = #{0}
          and actual_end_time is not null
    </update>

    <sql id="filter">
        <if test="request.filter != null and request.filter.size() > 0">
            <foreach collection="request.filter.entrySet()" index="key" item="values">
                <if test="values != null and values.size() > 0">
                    <choose>
                        <when test="key == 'triggerMode'">
                            and tpr.trigger_mode in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                        </when>
                        <!-- 执行结果 -->
                        <when test="key == 'execResult'">
                            and tpr.result_status in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                        </when>
                    </choose>
                </if>
            </foreach>
        </if>
    </sql>

    <sql id="filterTask">
        <if test="request.filter != null and request.filter.size() > 0">
            <foreach collection="request.filter.entrySet()" index="key" item="values">
                <if test="values != null and values.size() > 0">
                    <choose>
                        <when test="key == 'triggerMode'">
                            and et.trigger_mode in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                        </when>
                        <!-- 执行结果 -->
                        <when test="key == 'execResult'">
                            and et.result in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                        </when>
                    </choose>
                </if>
            </foreach>
        </if>
    </sql>

    <select id="projectPlanCount"
            resultType="io.metersphere.project.dto.ProjectCountDTO">
        SELECT test_plan.project_id as projectId, count(test_plan.id) as count
        FROM test_plan
        WHERE
        test_plan.project_id IN
        <foreach collection="projectIds" item="projectId" separator="," open="(" close=")">
            #{projectId}
        </foreach>
        <if test="startTime != null and endTime != null">
            AND test_plan.create_time BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="userId != null and userId != ''">
            AND test_plan.create_user = #{userId}
        </if>
        AND test_plan.type = 'TEST_PLAN'
        group by test_plan.project_id;
    </select>

    <select id="userCreatePlanCount"
            resultType="io.metersphere.project.dto.ProjectUserCreateCount">
        SELECT test_plan.create_user as userId, count(test_plan.id) as count
        FROM test_plan
        WHERE test_plan.project_id =  #{projectId}
        <if test="startTime != null and endTime != null">
            AND test_plan.create_time BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="userIds != null and userIds.size() > 0">
            and test_plan.create_user in
            <foreach collection="userIds" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        AND test_plan.type = 'TEST_PLAN'
        group by test_plan.create_user;
    </select>



    <select id="selectMyFollowByConditions"
            resultMap="BaseResultMapDTO">
        select t.* from (
                SELECT
                t.id,t.num,t.name,t.status,t.group_id,
                t.create_user AS createUser,
                createUser.name AS createUserName,
                t.create_time,
                t.module_id as moduleId,
                t.type,
                t.description,
                t.pos,
                t.planned_start_time AS plannedStartTime,
                t.planned_end_time AS plannedEndTime,
                t.actual_start_time AS actualStartTime,
                t.actual_end_time AS actualEndTime,
                t.tags
                FROM test_plan t
                INNER JOIN user createUser ON t.create_user = createUser.id
                WHERE t.project_id = #{request.projectId}
                <include refid="queryMyFollowGroupByTableRequest">
                    <property name="group" value="true"/>
                    <property name="request" value="request"/>
                </include>
                union
                SELECT
                t.id,t.num,t.name,t.status,t.group_id,
                t.create_user AS createUser,
                createUser.name AS createUserName,
                t.create_time,
                t.module_id as moduleId,
                t.type,
                t.description,
                t.pos,
                t.planned_start_time AS plannedStartTime,
                t.planned_end_time AS plannedEndTime,
                t.actual_start_time AS actualStartTime,
                t.actual_end_time AS actualEndTime,
                t.tags
                FROM test_plan t
                INNER JOIN user createUser ON t.create_user = createUser.id
                WHERE t.project_id = #{request.projectId}
                <include refid="queryMyFollowGroupByTableRequest">
                    <property name="group" value="false"/>
                    <property name="request" value="request"/>
                </include>
        ) t
    </select>


    <select id="selectIdAndStatusByProjectIdAndCreateTimeRangeAndType" resultType="io.metersphere.plan.domain.TestPlan">
        SELECT id, status
        FROM test_plan
        WHERE project_id = #{projectId}
        <if test="startTime != null and endTime != null">
            AND create_time BETWEEN #{startTime} AND #{endTime}
        </if>
          AND type = #{type}
    </select>
    <select id="getPlanBugList" resultType="io.metersphere.plugin.platform.dto.SelectOption">
        select distinct bug.id as `value`, bug.status as `text`
        from test_plan
                 left join bug_relation_case on test_plan.id = bug_relation_case.test_plan_id
                 left join bug on bug_relation_case.bug_id = bug.id
        where test_plan.status = 'NOT_ARCHIVED'
          and bug.deleted = false
        <if test="type != null and type != ''">
            and test_plan.type= #{type}
        </if>
          and test_plan.project_id = #{projectId}
        <if test="statusList != null and statusList.size() > 0">
            and bug.status in
            <foreach collection="statusList" item="status" separator="," open="(" close=")">
                #{status}
            </foreach>
        </if>
        <if test="platforms != null and platforms.size() > 0">
            and bug.platform in
            <foreach collection="platforms" item="platform" separator="," open="(" close=")">
                #{platform}
            </foreach>
        </if>
    </select>
    <select id="getGroupAndPlanInfo" resultType="io.metersphere.plan.dto.TestPlanAndGroupInfoDTO">
        select  son.id, son.name, son.group_id, son.project_id, father.name as groupName, son.create_time, father.create_time as groupCreateTime
        from test_plan son
                 left join test_plan father on father.id = son.group_id
        where son.type = 'TEST_PLAN' and son.status='NOT_ARCHIVED'
          and father.type = 'GROUP' and son.project_id = #{projectId};
    </select>
    <select id="getLatestPlan" resultType="io.metersphere.plan.domain.TestPlan">
        select id, name, group_id
        from test_plan
        where type = 'TEST_PLAN' and status='NOT_ARCHIVED' and project_id = #{projectId} order by create_time desc limit 1;
    </select>

    <select id="getLatestPlanByProjectIds" resultType="io.metersphere.plan.domain.TestPlan">
        select id, name, group_id, project_id
        from test_plan
        where type = 'TEST_PLAN' and status='NOT_ARCHIVED' and project_id  IN
        <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        order by create_time desc limit 1;
    </select>

    <select id="selectTestPlanConfigByTestPlanIds" resultType="io.metersphere.plan.domain.TestPlanConfig">
        SELECT *
        from test_plan_config test_plan_id
        WHERE test_plan_id IN
        <foreach collection="testPlanIds" item="testPlanId" open="(" separator="," close=")">
            #{testPlanId}
        </foreach>
    </select>
    <select id="selectIdAndGroupIdByProjectId" resultType="io.metersphere.plan.domain.TestPlan">
        SELECT id, group_id, type
        FROM test_plan
        WHERE project_id = #{projectId}
        <if test="selectArchived == false">
            AND status != 'ARCHIVED'
        </if>
        <if test="selectArchived == true">
            AND status = 'ARCHIVED'
        </if>
    </select>

    <sql id="queryMyFollowGroupByTableRequest">
        <include refid="baseConditionQuery"/>
        <if test="request.keyword != null and request.keyword != ''">
            and (
            t.name like concat('%', #{request.keyword},'%')
            or t.num like concat('%', #{request.keyword},'%')
            or t.tags like concat('%', #{request.keyword}, '%')
            <if test="request.keywordFilterIds != null and request.keywordFilterIds.size() > 0">
                or t.id in
                <foreach collection="request.keywordFilterIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            )
        </if>
        <if test="request.innerIds != null and request.innerIds.size() > 0">
            and t.id in
            <foreach collection="request.innerIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="request.combineInnerIds != null and request.combineInnerIds.size() > 0">
            and t.id in
            <foreach collection="request.combineInnerIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        <include refid="filter">
            <property name="filter" value="request.filter"/>
        </include>
        <include refid="myFollowCombine">
            <property name="combineSearch" value="request.combineSearch"/>
            <property name="group" value="${group}"/>
        </include>
    </sql>



    <sql id="myFollowCombine">
        <trim prefix="AND">
            <trim prefix="(" suffix=")" suffixOverrides="AND|OR">
                <if test="${combineSearch} != null">
                    <foreach collection="${combineSearch}.userViewConditions" item="condition">
                        <if test="condition.name == 'createUser'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                <property name="condition" value="condition"/>
                                <property name="column" value="t.create_user"/>
                            </include>
                        </if>
                        <if test="condition.name == 'follower' and ${group}">
                            t.id IN (
                            SELECT
                            test_plan.group_id
                            FROM
                            test_plan_follower
                            INNER JOIN test_plan on test_plan_follower.test_plan_id = test_plan.id
                            WHERE
                            test_plan.project_id = #{request.projectId}
                            and test_plan_follower.user_id IN
                            <foreach collection="condition.value" item="v" separator="," open="(" close=")">
                                #{v}
                            </foreach>
                            )
                        </if>

                        <if test="condition.name == 'follower' and !${group}">
                            <include refid="io.metersphere.system.mapper.BaseMapper.associationCondition">
                                <property name="mainIdColumn" value="t.id"/>
                                <property name="associationTable" value="test_plan_follower"/>
                                <property name="associationIdColumn" value="test_plan_id"/>
                                <property name="searchColumn" value="user_id"/>
                                <property name="condition" value="condition"/>
                            </include>
                        </if>
                        <include refid="io.metersphere.system.mapper.BaseMapper.queryType">
                            <property name="searchMode" value="${combineSearch}.searchMode"/>
                        </include>
                    </foreach>
                </if>
            </trim>
        </trim>


        <trim prefix="AND">
            <trim prefix="(" suffix=")" suffixOverrides="AND|OR">
                <if test="${combineSearch} != null">
                    <foreach collection="${combineSearch}.systemFieldConditions" item="condition">
                        <if test="condition.name == 'moduleId'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                <property name="condition" value="condition"/>
                                <property name="column" value="t.module_id"/>
                            </include>
                        </if>
                        <if test="condition.name == 'name'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                <property name="condition" value="condition"/>
                                <property name="column" value="t.name"/>
                            </include>
                            <if test="request.type != 'TEST_PLAN'" >
                                <if test="condition.operator == 'CONTAINS' or condition.operator == 'EQUALS'">
                                    or t.id in
                                    (
                                    SELECT group_id
                                    FROM test_plan
                                    WHERE project_id = #{request.projectId}
                                    AND
                                    <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                        <property name="condition" value="condition"/>
                                        <property name="column" value="test_plan.name"/>
                                    </include>
                                    )
                                </if>
                            </if>
                        </if>
                        <if test="condition.name == 'num'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                <property name="condition" value="condition"/>
                                <property name="column" value="t.num"/>
                            </include>
                            <if test="request.type != 'TEST_PLAN'" >
                                <if test="condition.operator == 'CONTAINS' or condition.operator == 'EQUALS'">
                                    or t.id in
                                    (
                                    SELECT group_id
                                    FROM test_plan
                                    WHERE project_id = #{request.projectId}
                                    AND
                                    <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                        <property name="condition" value="condition"/>
                                        <property name="column" value="test_plan.num"/>
                                    </include>
                                    )
                                </if>
                            </if>
                        </if>
                        <if test="condition.name == 'createUser'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                <property name="condition" value="condition"/>
                                <property name="column" value="t.create_user"/>
                            </include>
                        </if>
                        <if test="condition.name == 'updateUser'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                <property name="condition" value="condition"/>
                                <property name="column" value="t.update_user"/>
                            </include>
                        </if>
                        <if test="condition.name == 'updateTime'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                <property name="condition" value="condition"/>
                                <property name="column" value="t.update_time"/>
                            </include>
                        </if>
                        <if test="condition.name == 'createTime'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                <property name="condition" value="condition"/>
                                <property name="column" value="t.create_time"/>
                            </include>
                        </if>
                        <if test="condition.name == 'tags'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.arrayValueCondition">
                                <property name="condition" value="condition"/>
                                <property name="column" value="t.tags"/>
                            </include>
                            <if test="request.type != 'TEST_PLAN'" >
                                <if test="condition.operator == 'CONTAINS'">
                                    or t.id in
                                    (
                                    SELECT group_id
                                    FROM test_plan
                                    WHERE project_id = #{request.projectId}
                                    AND
                                    <include refid="io.metersphere.system.mapper.BaseMapper.arrayValueCondition">
                                        <property name="condition" value="condition"/>
                                        <property name="column" value="test_plan.tags"/>
                                    </include>
                                    )
                                </if>
                            </if>
                        </if>
                        <!-- 状态 -->
                        <if test="condition.name == 'status'">
                            <include refid="io.metersphere.system.mapper.BaseMapper.condition">
                                <property name="condition" value="condition"/>
                                <property name="column" value="t.status"/>
                            </include>
                        </if>
                        <include refid="io.metersphere.system.mapper.BaseMapper.queryType">
                            <property name="searchMode" value="${combineSearch}.searchMode"/>
                        </include>
                    </foreach>
                </if>
            </trim>
        </trim>
    </sql>

</mapper>