# embedded config
embedded.containers.enabled=true
embedded.containers.forceShutdown=true
# mysql
embedded.mysql.enabled=true
embedded.mysql.encoding=utf8mb4
embedded.mysql.collation=utf8mb4_general_ci
embedded.mysql.dockerImage=mysql:8.0.35
# redis
embedded.redis.enabled=true
# kafka
embedded.kafka.enabled=true
# minio
embedded.minio.enabled=true
# server
embedded.server.enabled=true
spring.cloud.compatibility-verifier.enabled=false
management.metrics.enable.logback=false
