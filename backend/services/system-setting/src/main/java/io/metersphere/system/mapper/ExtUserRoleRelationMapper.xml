<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.system.mapper.ExtUserRoleRelationMapper">

    <select id="selectGlobalRoleByUserIdList" resultType="io.metersphere.system.domain.UserRoleRelation">
        SELECT * FROM
        user_role_relation
        WHERE
        user_id IN
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND role_id IN (
        SELECT id FROM user_role WHERE scope_id = 'global'
        )
    </select>

    <select id="selectRoleByUserIdList" resultType="io.metersphere.system.domain.UserRoleRelation">
        SELECT * FROM
        user_role_relation
        WHERE
        user_id IN
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectGlobalRoleByUserId" resultType="io.metersphere.system.domain.UserRoleRelation">
        SELECT *
        FROM user_role_relation
        WHERE user_id = #{userId}
          AND role_id IN (SELECT id
                          FROM user_role
                          WHERE type = 'SYSTEM' AND scope_id = 'global')
    </select>
    <select id="listGlobal" resultType="io.metersphere.system.dto.user.UserRoleRelationUserDTO">
        select urr.id, u.id as userId, u.name, u.email, u.phone
        from user_role_relation urr
        inner join user u on urr.user_id = u.id and urr.role_id = #{request.roleId} and u.deleted = 0
        <if test="request.keyword != null and request.keyword != ''">
            and (
            u.name like concat('%', #{request.keyword},'%')
            or u.email like concat('%', #{request.keyword},'%')
            or u.phone like concat('%', #{request.keyword},'%')
            )
        </if>
        order by urr.create_time desc
    </select>

    <select id="selectUserRoleByUserIds" resultType="io.metersphere.system.dto.user.UserRoleOptionDto">
        SELECT DISTINCT
        user_role_relation.role_id as id,
        user_role.`name` as name,
        user_role_relation.user_id as userId
        FROM
        user_role_relation
        LEFT JOIN user_role ON user_role_relation.role_id = user_role.id
        where user_role_relation.user_id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and organization_id = #{orgId}
        and source_id = #{orgId}
    </select>

    <select id="selectProjectUserRoleByUserIds" resultType="io.metersphere.system.dto.user.UserRoleOptionDto">
        SELECT DISTINCT
        user_role_relation.role_id as id,
        user_role.`name` as name,
        user_role_relation.user_id as userId
        FROM
        user_role_relation
        LEFT JOIN user_role ON user_role_relation.role_id = user_role.id
        where user_role_relation.user_id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and source_id = #{projectId}
    </select>
</mapper>