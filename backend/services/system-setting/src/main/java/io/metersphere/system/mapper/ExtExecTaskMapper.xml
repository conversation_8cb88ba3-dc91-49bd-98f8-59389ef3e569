<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.system.mapper.ExtExecTaskMapper">
    <select id="selectList" resultType="io.metersphere.system.dto.taskhub.TaskHubDTO">
        SELECT *
        FROM exec_task
        <where>
            exec_task.deleted = false
            <if test="request.keyword != null and request.keyword != ''">
                and (
                exec_task.num like concat('%', #{request.keyword},'%')
                or exec_task.task_name like concat('%', #{request.keyword},'%')
                )
            </if>
            <if test="orgId != null">
                and exec_task.organization_id = #{orgId}
            </if>
            <if test="projectId != null">
                and exec_task.project_id = #{projectId}
            </if>
            <include refid="queryWhereCondition"/>
        </where>
    </select>
    <sql id="queryWhereCondition">
        <include refid="filters">
            <property name="filter" value="request.filter"/>
        </include>
    </sql>


    <update id="deleteTaskByIds">
        UPDATE exec_task
        SET deleted = true
        WHERE id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="projectId != null and projectId != ''">
            and project_id = #{projectId}
        </if>
        <if test="orgId != null and orgId != ''">
            and organization_id = #{orgId}
        </if>
    </update>


    <select id="getIds" resultType="java.lang.String">
        select id from exec_task
        <where>
            deleted = false
            <if test="flag">
                and status in ('RUNNING', 'RERUNNING')
            </if>
            <if test="projectId != null and projectId != ''">
                and exec_task.project_id = #{projectId}
            </if>
            <if test="organizationId != null and organizationId != ''">
                and exec_task.organization_id = #{organizationId}
            </if>
            <include refid="queryWhereConditionByBaseQueryRequest"/>
        </where>
    </select>

    <sql id="queryWhereConditionByBaseQueryRequest">
        <if test="request.condition.keyword != null and request.condition.keyword != ''">
            and (
            exec_task.num like concat('%', #{request.condition.keyword},'%')
            or exec_task.task_name like concat('%', #{request.condition.keyword},'%')
            )
        </if>
        <include refid="filters">
            <property name="filter" value="request.condition.filter"/>
        </include>
    </sql>


    <sql id="filters">
        <if test="${filter} != null and ${filter}.size() > 0">
            <foreach collection="${filter}.entrySet()" index="key" item="values">
                <if test="values != null and values.size() > 0">
                    <choose>
                        <!-- 执行状态 -->
                        <when test="key=='status'">
                            and exec_task.status in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                        </when>
                        <!-- 执行方式 -->
                        <when test="key=='triggerMode'">
                            and exec_task.trigger_mode in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                        </when>
                        <!-- 执行结果 -->
                        <when test="key=='result'">
                            and exec_task.result in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                        </when>
                        <!-- 所属组织 -->
                        <when test="key=='organizationName'">
                            and exec_task.organization_id in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                        </when>
                        <!-- 所属项目 -->
                        <when test="key=='projectName'">
                            and exec_task.project_id in
                            <include refid="io.metersphere.system.mapper.BaseMapper.filterInWrapper"/>
                        </when>
                    </choose>
                </if>
            </foreach>
        </if>
    </sql>



    <update id="batchUpdateTaskStatus">
        UPDATE exec_task
        SET `status` = #{status},
        create_user = #{userId}
        WHERE  id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="projectId != null and projectId != ''">
            and project_id = #{projectId}
        </if>
        <if test="organizationId != null and organizationId != ''">
            and organization_id = #{organizationId}
        </if>
        and `status` in ('RUNNING', 'RERUNNING')
        and deleted = false
    </update>

    <select id="getTaskIdsByTime" resultType="java.lang.String">
        select id from exec_task where project_id = #{projectId} and exec_task.create_time &lt;= #{timeMills} and deleted = false
    </select>


    <select id="selectTypeByItemId" resultType="io.metersphere.system.dto.taskhub.ExecTaskItemDetailDTO">
        select exec_task.id,
               exec_task.integrated,
               exec_task.create_time as createTime,
               exec_task_item.result as result,
               exec_task_item.status as status,
               exec_task_item.resource_pool_id as resourcePoolId,
               exec_task_item.resource_pool_node as resourcePoolNode,
                exec_task_item.thread_id as threadId,
               exec_task_item.start_time as startTime,
               exec_task_item.end_time as endTime,
                exec_task_item.task_origin as taskOrigin
               from exec_task inner join exec_task_item on exec_task.id = exec_task_item.task_id where exec_task_item.id = #{itemId}
    </select>

    <select id="getSelectIds" resultType="java.lang.String">
        select id from exec_task
        where deleted = false
        <if test="flag">
            and status = 'RUNNING'
        </if>
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>