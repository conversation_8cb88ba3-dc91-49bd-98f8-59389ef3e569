<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="io.metersphere.project.mapper.ExtFakeErrorMapper">

    <select id="selectByKeyword" resultType="java.lang.String">
        select id from fake_error
        <where>
            <if test="keyword != null and keyword != ''">
                and name like concat('%', #{keyword}, '%')
            </if>
        </where>
    </select>

</mapper>