# 插入测试数据
INSERT INTO organization(id, num, name, description, create_time, update_time, create_user, update_user, deleted, delete_user, delete_time) VALUE
    ('organization-message-test', null, 'organization-message-test', 'organization-message-test', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, 'admin', 'admin', 0, null, null);
INSERT INTO project (id, num, organization_id, name, description, create_user, update_user, create_time, update_time) VALUES
                                                                                                                          ('project-message-test', null, 'organization-message-test', '默认项目', '系统默认创建的项目', 'admin', 'admin', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
                                                                                                                          ('project-message-test-1', null, 'organization-message-test-1', '默认项目1', '系统默认创建的项目1', 'admin', 'admin', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
                                                                                                                          ('project-message-test-2', null, 'organization-message-test-2', '默认项目2', '系统默认创建的项目2', 'admin', 'admin', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
                                                                                                                          ('project-message-test-3', null, 'organization-message-test-3', '默认项目3', '系统默认创建的项目3', 'admin', 'admin', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);


INSERT INTO user(id, name, email, password, create_time, update_time, language, last_organization_id, phone, source, last_project_id, create_user, update_user, deleted) VALUES
                                                                                                                                                                             ('project-message-user-1', 'project-message-user-1', '<EMAIL>', MD5('metersphere'), UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, NULL, NUll, '', 'LOCAL', NULL, 'admin', 'admin', 0),
                                                                                                                                                                             ('project-message-user-2', 'project-message-user-2', '<EMAIL>', MD5('metersphere'), UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, NULL, NUll, '', 'LOCAL', NULL, 'admin', 'admin', 0),
                                                                                                                                                                             ('project-message-user-3', 'project-message-user-3', '<EMAIL>', MD5('metersphere'), UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, NULL, NUll, '', 'LOCAL', NULL, 'admin', 'admin', 0),
                                                                                                                                                                             ('project-message-user-4', 'project-message-user-4', '<EMAIL>', MD5('metersphere'), UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, NULL, NUll, '', 'LOCAL', NULL, 'admin', 'admin', 0),
                                                                                                                                                                             ('project-message-user-5', 'project-message-user-5', '<EMAIL>', MD5('metersphere'), UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, NULL, NUll, '', 'LOCAL', NULL, 'admin', 'admin', 0),
                                                                                                                                                                             ('project-message-user-6', 'project-message-user-6', '<EMAIL>', MD5('metersphere'), UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, NULL, NUll, '', 'LOCAL', NULL, 'admin', 'admin', 0),
                                                                                                                                                                             ('project-message-user-7', 'project-message-user-7', '<EMAIL>', MD5('metersphere'), UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, NULL, NUll, '', 'LOCAL', NULL, 'admin', 'admin', 0),
                                                                                                                                                                             ('project-message-user-8', 'project-message-user-8', '<EMAIL>', MD5('metersphere'), UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, NULL, NUll, '', 'LOCAL', NULL, 'admin', 'admin', 0),
                                                                                                                                                                             ('project-message-user-9', 'project-message-user-9', '<EMAIL>', MD5('metersphere'), UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, NULL, NUll, '', 'LOCAL', NULL, 'admin', 'admin', 0),
                                                                                                                                                                             ('project-message-user-10', 'project-message-user-10', '<EMAIL>', MD5('metersphere'), UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, NULL, NUll, '', 'LOCAL', NULL, 'admin', 'admin', 0),
                                                                                                                                                                             ('project-message-user-del', 'project-message-user-del', '<EMAIL>', MD5('metersphere'), UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, NULL, NUll, '', 'LOCAL', NULL, 'admin', 'admin', 1);

INSERT INTO user_role_relation (id, user_id, role_id, source_id, organization_id, create_time, create_user) VALUES
                                                                                                                (UUID(), 'project-message-user-1', 'project_member', 'project-message-test', 'organization-message-test', UNIX_TIMESTAMP() * 1000, 'admin'),
                                                                                                                (UUID(), 'project-message-user-2', 'project_member', 'project-message-test', 'organization-message-test', UNIX_TIMESTAMP() * 1000, 'admin'),
                                                                                                                (UUID(), 'project-message-user-3', 'project_member', 'project-message-test-1', 'organization-message-test', UNIX_TIMESTAMP() * 1000, 'admin'),
                                                                                                                (UUID(), 'project-message-user-4', 'project_member', 'project-message-test-1', 'organization-message-test', UNIX_TIMESTAMP() * 1000, 'admin'),
                                                                                                                (UUID(), 'project-message-user-5', 'project_member', 'project-message-test', 'organization-message-test', UNIX_TIMESTAMP() * 1000, 'admin'),
                                                                                                                (UUID(), 'project-message-user-6', 'project_member', 'project-message-test', 'organization-message-test', UNIX_TIMESTAMP() * 1000, 'admin'),
                                                                                                                (UUID(), 'project-message-user-7', 'project_member', 'project-message-test-1', 'organization-message-test', UNIX_TIMESTAMP() * 1000, 'admin'),
                                                                                                                (UUID(), 'project-message-user-8', 'project_member', 'project-message-test-1', 'organization-message-test', UNIX_TIMESTAMP() * 1000, 'admin'),
                                                                                                                (UUID(), 'project-message-user-9', 'project_member', 'project-message-test-1', 'organization-message-test', UNIX_TIMESTAMP() * 1000, 'admin'),
                                                                                                                (UUID(), 'project-message-user-10', 'project_member', 'project-message-test-1', 'organization-message-test', UNIX_TIMESTAMP() * 1000, 'admin');



INSERT INTO project_robot(id, project_id, name, platform, webhook, type, app_key, app_secret, enable, create_user, create_time, update_user, update_time, description) VALUES ('test_message_robot1', 'project-message-test-1', '测试机器人1', 'IN_SITE', 'NONE', null,  null, null, true, 'admin', unix_timestamp() * 1000,'admin',  unix_timestamp() * 1000, null),
                                                                                                                                                                              ('test_message_robot2', 'project-message-test-1', '测试机器人2', 'MAIL', 'NONE', null,  null, null, true, 'admin', unix_timestamp() * 1000,'admin',  unix_timestamp() * 1000, null),
                                                                                                                                                                              ('test_message_robot3', 'project-message-test-1', '测试机器人3', 'WE_COM', 'NONE', null,  null, null, true, 'admin', unix_timestamp() * 1000,'admin',  unix_timestamp() * 1000, null),
                                                                                                                                                                              ('test_message_robot4', 'project-message-test-3', '测试机器人1', 'IN_SITE', 'NONE', null,  null, null, true, 'admin', unix_timestamp() * 1000,'admin',  unix_timestamp() * 1000, null),
                                                                                                                                                                              ('test_message_robot5', 'project-message-test', '测试机器人6', 'IN_SITE', 'NONE', null,  null, null, true, 'admin', unix_timestamp() * 1000,'admin',  unix_timestamp() * 1000, null);

INSERT INTO custom_field(id, name, scene, `type`, remark, internal, scope_type, create_time, update_time, create_user, scope_id)
VALUES('gyq_custom_id_message1', 'gyq_functional_priority', 'FUNCTIONAL', 'SELECT', '', 1, 'ORGANIZATION', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, 'admin', 'project-message-test');






