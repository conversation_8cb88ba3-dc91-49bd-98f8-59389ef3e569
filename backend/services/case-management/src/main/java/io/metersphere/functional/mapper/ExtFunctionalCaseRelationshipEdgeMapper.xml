<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.functional.mapper.ExtFunctionalCaseRelationshipEdgeMapper">


    <select id="getGraphIds" resultType="java.lang.String">
        select distinct graph_id
        from functional_case_relationship_edge
        where source_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        or target_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="list" parameterType="io.metersphere.functional.request.RelationshipRequest" resultType="io.metersphere.functional.dto.FunctionalCaseRelationshipDTO">
        SELECT
            fcre.id id,
            fc.NAME name,
            fc.version_id versionId,
            pv.`name` versionName,
            u.`name` userName,
            fc.num,
            fc.id caseId
        FROM
            functional_case_relationship_edge fcre
                <if test="request.type != null and request.type != ''">
                    <choose>
                        <when test="request.type == 'PRE'">
                            LEFT JOIN functional_case fc ON fcre.target_id = fc.id
                        </when>
                        <when test="request.type == 'POST'">
                            LEFT JOIN functional_case fc ON fcre.source_id = fc.id
                        </when>
                    </choose>
                </if>
                LEFT JOIN `user` u ON fc.create_user = u.id
                LEFT JOIN project_version pv ON pv.id = fc.version_id
        <where>
            <if test="request.type != null and request.type != ''">
                <choose>
                    <when test="request.type == 'PRE'">
                        AND fcre.source_id = #{request.id}
                    </when>
                    <when test="request.type == 'POST'">
                        AND fcre.target_id = #{request.id}
                    </when>
                </choose>
            </if>
            <if test="request.keyword != null and request.keyword != ''">
                and fc.name like concat('%', #{request.keyword},'%')
            </if>
            order by
            <if test="sort != null and sort != ''">
                fc.${sort}
            </if>
            <if test="sort == null or sort == ''">
                fcre.create_time desc
            </if>
        </where>

    </select>
    
    <select id="getGraphId" resultType="io.metersphere.system.dto.RelationshipEdgeDTO" parameterType="java.lang.String">
        select source_id sourceId, target_id targetId, graph_id graphId from functional_case_relationship_edge where id = #{id}
    </select>

    <select id="getEdgeByGraphId" parameterType="java.lang.String" resultType="io.metersphere.system.dto.RelationshipEdgeDTO">
        select source_id sourceId, target_id targetId from functional_case_relationship_edge where graph_id = #{graphId}
    </select>

    <update id="update">
        update functional_case_relationship_edge set graph_id = #{graphId} where (source_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        or target_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>)
    </update>
</mapper>