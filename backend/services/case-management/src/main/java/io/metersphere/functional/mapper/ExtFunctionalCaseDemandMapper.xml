<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.metersphere.functional.mapper.ExtFunctionalCaseDemandMapper">

    <select id="selectParentDemandByKeyword" resultType="io.metersphere.functional.dto.FunctionalDemandDTO">
        SELECT
        *
        FROM functional_case_demand
        WHERE functional_case_demand.case_id = #{caseId}
          AND
        (
        functional_case_demand.parent = 'NONE' OR
        (functional_case_demand.with_parent = 'false')
        )
        <if test="keyword != null and keyword != ''">
            AND functional_case_demand.demand_name LIKE CONCAT('%', #{keyword}, '%')
        </if>
    </select>

    <select id="selectDemandByProjectId" resultType="io.metersphere.functional.domain.FunctionalCaseDemand">
        select functional_case_demand.id, functional_case_demand.demand_id, functional_case_demand.case_id
        from functional_case_demand
                 left join functional_case on functional_case.id = functional_case_demand.case_id
        where functional_case.project_id = #{projectId} and functional_case_demand.demand_platform=#{platform}
    </select>

    <select id="selectDemandIdsByCaseId" resultType="java.lang.String">
        select demand_id
        from functional_case_demand
        where case_id = #{caseId} and demand_platform=#{platform}
    </select>

</mapper>