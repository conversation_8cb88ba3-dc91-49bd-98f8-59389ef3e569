<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.metersphere</groupId>
        <artifactId>services</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>metersphere-case-management</artifactId>
    <version>${revision}</version>
    <name>case-management</name>
    <properties>
    </properties>
    <dependencies>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-sdk</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-system-setting</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-project-management</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-system-setting</artifactId>
            <version>${revision}</version>
            <classifier>tests</classifier>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.metersphere</groupId>
            <artifactId>metersphere-provider</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-core</artifactId>
        </dependency>
        <dependency>
            <groupId>jaxen</groupId>
            <artifactId>jaxen</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.7</version>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                    <configurationFile>src/main/resources/functionalGeneratorConfig.xml</configurationFile>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>com.mysql</groupId>
                        <artifactId>mysql-connector-j</artifactId>
                        <version>${mysql-connector-java.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>com.itfsw</groupId>
                        <artifactId>mybatis-generator-plugin</artifactId>
                        <version>1.3.10</version>
                    </dependency>
                    <dependency>
                        <groupId>io.metersphere</groupId>
                        <artifactId>mybatis-tools</artifactId>
                        <version>3.0.0</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

</project>
