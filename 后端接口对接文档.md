# 接口自动化测试平台 - 后端接口对接文档

## 一、数据库设计

### 1. 用户管理模块

#### 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    real_name VARCHAR(50) COMMENT '真实姓名',
    avatar VARCHAR(255) COMMENT '头像URL',
    role ENUM('admin', 'developer', 'tester') DEFAULT 'tester' COMMENT '角色',
    status TINYINT DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email)
) COMMENT='用户表';
```

#### 操作日志表 (operation_logs)
```sql
CREATE TABLE operation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    user_id BIGINT NOT NULL COMMENT '操作用户ID',
    action VARCHAR(50) NOT NULL COMMENT '操作动作',
    resource_type VARCHAR(50) NOT NULL COMMENT '资源类型',
    resource_id BIGINT COMMENT '资源ID',
    resource_name VARCHAR(255) COMMENT '资源名称',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    request_data JSON COMMENT '请求数据',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource_type (resource_type),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT='操作日志表';
```

### 2. 项目管理模块

#### 项目表 (projects)
```sql
CREATE TABLE projects (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '项目ID',
    name VARCHAR(100) NOT NULL COMMENT '项目名称',
    description TEXT COMMENT '项目描述',
    owner_id BIGINT NOT NULL COMMENT '负责人ID',
    status TINYINT DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    api_count INT DEFAULT 0 COMMENT '接口总数',
    case_count INT DEFAULT 0 COMMENT '用例总数',
    last_test_result JSON COMMENT '最近执行结果',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_owner_id (owner_id),
    INDEX idx_name (name),
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE RESTRICT
) COMMENT='项目表';
```

#### 项目成员表 (project_members)
```sql
CREATE TABLE project_members (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role ENUM('admin', 'developer', 'tester') DEFAULT 'tester' COMMENT '项目角色',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    UNIQUE KEY uk_project_user (project_id, user_id),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT='项目成员表';
```

#### 环境配置表 (environments)
```sql
CREATE TABLE environments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '环境ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    name VARCHAR(50) NOT NULL COMMENT '环境名称',
    base_url VARCHAR(255) NOT NULL COMMENT '基础URL',
    global_variables JSON COMMENT '全局变量',
    global_headers JSON COMMENT '全局请求头',
    auth_config JSON COMMENT '认证配置',
    is_default TINYINT DEFAULT 0 COMMENT '是否默认环境',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_project_id (project_id),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
) COMMENT='环境配置表';
```

### 3. 接口管理模块

#### 接口分组表 (api_groups)
```sql
CREATE TABLE api_groups (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分组ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    name VARCHAR(100) NOT NULL COMMENT '分组名称',
    description TEXT COMMENT '分组描述',
    parent_id BIGINT COMMENT '父分组ID',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_project_id (project_id),
    INDEX idx_parent_id (parent_id),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES api_groups(id) ON DELETE CASCADE
) COMMENT='接口分组表';
```

#### 接口表 (apis)
```sql
CREATE TABLE apis (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '接口ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    group_id BIGINT COMMENT '分组ID',
    name VARCHAR(200) NOT NULL COMMENT '接口名称',
    method ENUM('GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS') NOT NULL COMMENT '请求方法',
    path VARCHAR(500) NOT NULL COMMENT '接口路径',
    description TEXT COMMENT '接口描述',
    tags JSON COMMENT '标签',
    status TINYINT DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    request_headers JSON COMMENT '请求头定义',
    query_params JSON COMMENT 'Query参数定义',
    path_params JSON COMMENT 'Path参数定义',
    request_body JSON COMMENT '请求体定义',
    response_definition JSON COMMENT '响应定义',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_project_id (project_id),
    INDEX idx_group_id (group_id),
    INDEX idx_method_path (method, path),
    INDEX idx_creator_id (creator_id),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES api_groups(id) ON DELETE SET NULL,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT
) COMMENT='接口表';
```

### 4. 测试用例模块

#### 测试用例表 (test_cases)
```sql
CREATE TABLE test_cases (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用例ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    api_id BIGINT COMMENT '关联接口ID',
    name VARCHAR(200) NOT NULL COMMENT '用例名称',
    description TEXT COMMENT '用例描述',
    priority ENUM('high', 'medium', 'low') DEFAULT 'medium' COMMENT '优先级',
    status TINYINT DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    pre_operations JSON COMMENT '前置操作',
    request_config JSON COMMENT '请求配置',
    assertions JSON COMMENT '断言规则',
    post_operations JSON COMMENT '后置操作',
    data_driven_config JSON COMMENT '数据驱动配置',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    last_execution_status ENUM('pass', 'fail', 'skip', 'pending') COMMENT '最后执行状态',
    last_execution_time DATETIME COMMENT '最后执行时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_project_id (project_id),
    INDEX idx_api_id (api_id),
    INDEX idx_creator_id (creator_id),
    INDEX idx_status (status),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (api_id) REFERENCES apis(id) ON DELETE SET NULL,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT
) COMMENT='测试用例表';
```

#### 测试用例执行记录表 (test_case_executions)
```sql
CREATE TABLE test_case_executions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '执行记录ID',
    case_id BIGINT NOT NULL COMMENT '用例ID',
    execution_id VARCHAR(50) NOT NULL COMMENT '执行批次ID',
    environment_id BIGINT NOT NULL COMMENT '执行环境ID',
    status ENUM('pass', 'fail', 'skip', 'error') NOT NULL COMMENT '执行状态',
    request_data JSON COMMENT '实际请求数据',
    response_data JSON COMMENT '实际响应数据',
    assertion_results JSON COMMENT '断言结果',
    error_message TEXT COMMENT '错误信息',
    execution_time INT COMMENT '执行耗时(ms)',
    started_at DATETIME NOT NULL COMMENT '开始时间',
    finished_at DATETIME COMMENT '结束时间',
    INDEX idx_case_id (case_id),
    INDEX idx_execution_id (execution_id),
    INDEX idx_environment_id (environment_id),
    INDEX idx_status (status),
    INDEX idx_started_at (started_at),
    FOREIGN KEY (case_id) REFERENCES test_cases(id) ON DELETE CASCADE,
    FOREIGN KEY (environment_id) REFERENCES environments(id) ON DELETE RESTRICT
) COMMENT='测试用例执行记录表';
```

### 5. 测试计划模块

#### 测试计划表 (test_plans)
```sql
CREATE TABLE test_plans (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '计划ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    name VARCHAR(200) NOT NULL COMMENT '计划名称',
    description TEXT COMMENT '计划描述',
    environment_id BIGINT NOT NULL COMMENT '执行环境ID',
    schedule_type ENUM('immediate', 'scheduled', 'cron') DEFAULT 'immediate' COMMENT '调度类型',
    schedule_config JSON COMMENT '调度配置',
    notification_config JSON COMMENT '通知配置',
    retry_config JSON COMMENT '重试配置',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    status TINYINT DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    last_execution_time DATETIME COMMENT '最后执行时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_project_id (project_id),
    INDEX idx_environment_id (environment_id),
    INDEX idx_creator_id (creator_id),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (environment_id) REFERENCES environments(id) ON DELETE RESTRICT,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT
) COMMENT='测试计划表';
```

#### 测试计划用例关联表 (test_plan_cases)
```sql
CREATE TABLE test_plan_cases (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    plan_id BIGINT NOT NULL COMMENT '计划ID',
    case_id BIGINT NOT NULL COMMENT '用例ID',
    sort_order INT DEFAULT 0 COMMENT '执行顺序',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_plan_case (plan_id, case_id),
    INDEX idx_plan_id (plan_id),
    INDEX idx_case_id (case_id),
    FOREIGN KEY (plan_id) REFERENCES test_plans(id) ON DELETE CASCADE,
    FOREIGN KEY (case_id) REFERENCES test_cases(id) ON DELETE CASCADE
) COMMENT='测试计划用例关联表';
```

#### 测试计划执行记录表 (test_plan_executions)
```sql
CREATE TABLE test_plan_executions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '执行记录ID',
    execution_id VARCHAR(50) NOT NULL UNIQUE COMMENT '执行批次ID',
    plan_id BIGINT NOT NULL COMMENT '计划ID',
    environment_id BIGINT NOT NULL COMMENT '执行环境ID',
    trigger_type ENUM('manual', 'scheduled', 'api') NOT NULL COMMENT '触发类型',
    trigger_user_id BIGINT COMMENT '触发用户ID',
    status ENUM('running', 'completed', 'failed', 'cancelled') NOT NULL COMMENT '执行状态',
    total_cases INT DEFAULT 0 COMMENT '总用例数',
    passed_cases INT DEFAULT 0 COMMENT '通过用例数',
    failed_cases INT DEFAULT 0 COMMENT '失败用例数',
    skipped_cases INT DEFAULT 0 COMMENT '跳过用例数',
    execution_log TEXT COMMENT '执行日志',
    started_at DATETIME NOT NULL COMMENT '开始时间',
    finished_at DATETIME COMMENT '结束时间',
    INDEX idx_execution_id (execution_id),
    INDEX idx_plan_id (plan_id),
    INDEX idx_status (status),
    INDEX idx_started_at (started_at),
    FOREIGN KEY (plan_id) REFERENCES test_plans(id) ON DELETE CASCADE,
    FOREIGN KEY (environment_id) REFERENCES environments(id) ON DELETE RESTRICT,
    FOREIGN KEY (trigger_user_id) REFERENCES users(id) ON DELETE SET NULL
) COMMENT='测试计划执行记录表';
```

### 6. Mock服务模块

#### Mock规则表 (mock_rules)
```sql
CREATE TABLE mock_rules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'Mock规则ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    name VARCHAR(200) NOT NULL COMMENT '规则名称',
    description TEXT COMMENT '规则描述',
    method ENUM('GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS', 'ANY') DEFAULT 'ANY' COMMENT '请求方法',
    path_pattern VARCHAR(500) NOT NULL COMMENT '路径匹配模式',
    match_conditions JSON COMMENT '匹配条件',
    response_status INT DEFAULT 200 COMMENT '响应状态码',
    response_headers JSON COMMENT '响应头',
    response_body TEXT COMMENT '响应体模板',
    response_delay INT DEFAULT 0 COMMENT '响应延迟(ms)',
    is_enabled TINYINT DEFAULT 1 COMMENT '是否启用',
    hit_count INT DEFAULT 0 COMMENT '命中次数',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_project_id (project_id),
    INDEX idx_path_pattern (path_pattern),
    INDEX idx_creator_id (creator_id),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT
) COMMENT='Mock规则表';
```

#### Mock请求日志表 (mock_request_logs)
```sql
CREATE TABLE mock_request_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    rule_id BIGINT NOT NULL COMMENT 'Mock规则ID',
    request_method VARCHAR(10) NOT NULL COMMENT '请求方法',
    request_path VARCHAR(500) NOT NULL COMMENT '请求路径',
    request_headers JSON COMMENT '请求头',
    request_body TEXT COMMENT '请求体',
    response_status INT NOT NULL COMMENT '响应状态码',
    response_headers JSON COMMENT '响应头',
    response_body TEXT COMMENT '响应体',
    response_time INT COMMENT '响应时间(ms)',
    client_ip VARCHAR(45) COMMENT '客户端IP',
    user_agent TEXT COMMENT '用户代理',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间',
    INDEX idx_rule_id (rule_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (rule_id) REFERENCES mock_rules(id) ON DELETE CASCADE
) COMMENT='Mock请求日志表';
```

### 7. 性能测试模块

#### 性能测试配置表 (performance_tests)
```sql
CREATE TABLE performance_tests (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '性能测试ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    name VARCHAR(200) NOT NULL COMMENT '测试名称',
    description TEXT COMMENT '测试描述',
    target_api_id BIGINT NOT NULL COMMENT '目标接口ID',
    environment_id BIGINT NOT NULL COMMENT '测试环境ID',
    concurrent_users INT NOT NULL COMMENT '并发用户数',
    duration_seconds INT NOT NULL COMMENT '持续时间(秒)',
    ramp_up_seconds INT DEFAULT 0 COMMENT 'Ramp-up时间(秒)',
    test_data_config JSON COMMENT '测试数据配置',
    performance_thresholds JSON COMMENT '性能阈值配置',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    status TINYINT DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_project_id (project_id),
    INDEX idx_target_api_id (target_api_id),
    INDEX idx_environment_id (environment_id),
    INDEX idx_creator_id (creator_id),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (target_api_id) REFERENCES apis(id) ON DELETE CASCADE,
    FOREIGN KEY (environment_id) REFERENCES environments(id) ON DELETE RESTRICT,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT
) COMMENT='性能测试配置表';
```

#### 性能测试执行记录表 (performance_test_executions)
```sql
CREATE TABLE performance_test_executions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '执行记录ID',
    execution_id VARCHAR(50) NOT NULL UNIQUE COMMENT '执行批次ID',
    test_id BIGINT NOT NULL COMMENT '性能测试ID',
    status ENUM('running', 'completed', 'failed', 'cancelled') NOT NULL COMMENT '执行状态',
    total_requests INT DEFAULT 0 COMMENT '总请求数',
    successful_requests INT DEFAULT 0 COMMENT '成功请求数',
    failed_requests INT DEFAULT 0 COMMENT '失败请求数',
    avg_response_time DECIMAL(10,2) COMMENT '平均响应时间(ms)',
    p90_response_time DECIMAL(10,2) COMMENT 'P90响应时间(ms)',
    p95_response_time DECIMAL(10,2) COMMENT 'P95响应时间(ms)',
    p99_response_time DECIMAL(10,2) COMMENT 'P99响应时间(ms)',
    max_response_time DECIMAL(10,2) COMMENT '最大响应时间(ms)',
    min_response_time DECIMAL(10,2) COMMENT '最小响应时间(ms)',
    tps DECIMAL(10,2) COMMENT '每秒事务数',
    error_rate DECIMAL(5,2) COMMENT '错误率(%)',
    cpu_usage DECIMAL(5,2) COMMENT 'CPU使用率(%)',
    memory_usage DECIMAL(5,2) COMMENT '内存使用率(%)',
    detailed_report JSON COMMENT '详细报告数据',
    started_at DATETIME NOT NULL COMMENT '开始时间',
    finished_at DATETIME COMMENT '结束时间',
    INDEX idx_execution_id (execution_id),
    INDEX idx_test_id (test_id),
    INDEX idx_status (status),
    INDEX idx_started_at (started_at),
    FOREIGN KEY (test_id) REFERENCES performance_tests(id) ON DELETE CASCADE
) COMMENT='性能测试执行记录表';
```

### 8. 数据驱动测试模块

#### 测试数据集表 (test_datasets)
```sql
CREATE TABLE test_datasets (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '数据集ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    name VARCHAR(200) NOT NULL COMMENT '数据集名称',
    description TEXT COMMENT '数据集描述',
    data_type ENUM('csv', 'json', 'sql', 'api') NOT NULL COMMENT '数据类型',
    data_source JSON COMMENT '数据源配置',
    data_content LONGTEXT COMMENT '数据内容',
    schema_definition JSON COMMENT '数据结构定义',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_project_id (project_id),
    INDEX idx_creator_id (creator_id),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT
) COMMENT='测试数据集表';
```

### 9. 系统配置模块

#### 系统配置表 (system_configs)
```sql
CREATE TABLE system_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    config_category ENUM('basic', 'security', 'feature', 'storage', 'notification') DEFAULT 'basic' COMMENT '配置分类',
    description TEXT COMMENT '配置描述',
    is_public TINYINT DEFAULT 0 COMMENT '是否公开(1:公开 0:私有)',
    is_readonly TINYINT DEFAULT 0 COMMENT '是否只读(1:只读 0:可编辑)',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_key (config_key),
    INDEX idx_config_category (config_category)
) COMMENT='系统配置表';
```

#### 系统配置历史表 (system_config_history)
```sql
CREATE TABLE system_config_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '历史记录ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    old_value TEXT COMMENT '旧值',
    new_value TEXT COMMENT '新值',
    change_type ENUM('create', 'update', 'delete', 'restore') NOT NULL COMMENT '变更类型',
    operator_id BIGINT NOT NULL COMMENT '操作人ID',
    operator_name VARCHAR(50) NOT NULL COMMENT '操作人姓名',
    change_reason VARCHAR(255) COMMENT '变更原因',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    INDEX idx_config_key (config_key),
    INDEX idx_operator_id (operator_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (operator_id) REFERENCES users(id) ON DELETE RESTRICT
) COMMENT='系统配置历史表';
```

#### 系统监控表 (system_monitoring)
```sql
CREATE TABLE system_monitoring (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '监控记录ID',
    cpu_usage DECIMAL(5,2) COMMENT 'CPU使用率(%)',
    memory_usage DECIMAL(5,2) COMMENT '内存使用率(%)',
    disk_usage DECIMAL(5,2) COMMENT '磁盘使用率(%)',
    network_in BIGINT COMMENT '网络入流量(bytes)',
    network_out BIGINT COMMENT '网络出流量(bytes)',
    active_connections INT COMMENT '活跃连接数',
    database_connections INT COMMENT '数据库连接数',
    redis_connections INT COMMENT 'Redis连接数',
    queue_size INT COMMENT '队列大小',
    error_count INT DEFAULT 0 COMMENT '错误计数',
    warning_count INT DEFAULT 0 COMMENT '警告计数',
    uptime_seconds BIGINT COMMENT '运行时间(秒)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
    INDEX idx_created_at (created_at)
) COMMENT='系统监控表';
```

#### 系统配置默认数据
```sql
-- 插入默认系统配置数据
INSERT INTO system_configs (config_key, config_value, config_type, config_category, description, is_public) VALUES
-- 基本设置
('basic.system_name', 'API测试平台', 'string', 'basic', '系统名称', 1),
('basic.system_version', '1.0.0', 'string', 'basic', '系统版本', 1),
('basic.system_logo', '', 'string', 'basic', '系统Logo URL', 1),
('basic.system_description', '专业的API接口自动化测试平台', 'string', 'basic', '系统描述', 1),
('basic.company_name', '', 'string', 'basic', '公司名称', 1),
('basic.contact_email', '', 'string', 'basic', '联系邮箱', 1),
('basic.copyright', '© 2024 Your Company. All rights reserved.', 'string', 'basic', '版权信息', 1),

-- 安全设置
('security.password_min_length', '8', 'number', 'security', '密码最小长度', 0),
('security.password_complexity', 'medium', 'string', 'security', '密码复杂度要求', 0),
('security.login_lock_enabled', 'true', 'boolean', 'security', '是否启用登录失败锁定', 0),
('security.max_login_attempts', '5', 'number', 'security', '最大登录失败次数', 0),
('security.session_timeout', '120', 'number', 'security', '会话超时时间(分钟)', 0),
('security.force_https', 'false', 'boolean', 'security', '是否强制HTTPS', 0),
('security.ip_whitelist', '', 'string', 'security', 'IP白名单', 0),

-- 功能设置
('feature.user_registration_enabled', 'true', 'boolean', 'feature', '是否允许用户注册', 0),
('feature.email_verification_enabled', 'true', 'boolean', 'feature', '是否启用邮箱验证', 0),
('feature.mock_service_enabled', 'true', 'boolean', 'feature', '是否启用Mock服务', 0),
('feature.performance_test_enabled', 'true', 'boolean', 'feature', '是否启用性能测试', 0),
('feature.cicd_integration_enabled', 'true', 'boolean', 'feature', '是否启用CI/CD集成', 0),
('feature.data_driven_test_enabled', 'true', 'boolean', 'feature', '是否启用数据驱动测试', 0),
('feature.max_projects_per_user', '10', 'number', 'feature', '每个用户最大项目数', 0),
('feature.max_concurrent_executions', '5', 'number', 'feature', '最大并发执行数', 0),
('feature.max_file_size', '10', 'number', 'feature', '最大文件上传大小(MB)', 0),
('feature.allowed_file_types', 'jpg,png,pdf,csv,json,xml', 'string', 'feature', '允许的文件类型', 0),

-- 存储设置
('storage.storage_type', 'local', 'string', 'storage', '存储类型', 0),
('storage.data_retention_days', '90', 'number', 'storage', '数据保留天数', 0),
('storage.local_storage_path', '/var/www/uploads', 'string', 'storage', '本地存储路径', 0),
('storage.cloud_access_key', '', 'string', 'storage', '云存储访问密钥', 0),
('storage.cloud_secret_key', '', 'string', 'storage', '云存储私钥', 0),
('storage.cloud_bucket', '', 'string', 'storage', '云存储桶名称', 0),
('storage.cloud_region', '', 'string', 'storage', '云存储区域', 0),

-- 通知设置
('notification.email_enabled', 'false', 'boolean', 'notification', '是否启用邮件通知', 0),
('notification.webhook_enabled', 'false', 'boolean', 'notification', '是否启用Webhook通知', 0),
('notification.sms_enabled', 'false', 'boolean', 'notification', '是否启用短信通知', 0);
```

#### 文件存储表 (file_storage)
```sql
CREATE TABLE file_storage (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文件ID',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    stored_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    file_hash VARCHAR(64) COMMENT '文件哈希值',
    uploader_id BIGINT NOT NULL COMMENT '上传者ID',
    project_id BIGINT COMMENT '关联项目ID',
    usage_type ENUM('avatar', 'test_data', 'report', 'attachment') NOT NULL COMMENT '使用类型',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_uploader_id (uploader_id),
    INDEX idx_project_id (project_id),
    INDEX idx_file_hash (file_hash),
    FOREIGN KEY (uploader_id) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
) COMMENT='文件存储表';
```

## 二、后端API接口设计

### 1. 认证授权接口

#### 1.1 用户登录
```
POST /api/auth/login
```
**请求参数：**
```json
{
    "username": "string",     // 用户名或邮箱
    "password": "string",     // 密码
    "remember": "boolean"     // 是否记住登录状态
}
```
**响应数据：**
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "string",           // JWT令牌
        "refresh_token": "string",   // 刷新令牌
        "expires_in": 7200,          // 过期时间(秒)
        "user": {
            "id": 1,
            "username": "admin",
            "email": "<EMAIL>",
            "real_name": "管理员",
            "avatar": "string",
            "role": "admin"
        }
    }
}
```

#### 1.2 用户注册
```
POST /api/auth/register
```
**请求参数：**
```json
{
    "username": "string",     // 用户名
    "email": "string",        // 邮箱
    "password": "string",     // 密码
    "real_name": "string"     // 真实姓名
}
```

#### 1.3 刷新令牌
```
POST /api/auth/refresh
```
**请求参数：**
```json
{
    "refresh_token": "string"
}
```

#### 1.4 用户登出
```
POST /api/auth/logout
```

### 2. 项目管理接口

#### 2.1 获取项目列表
```
GET /api/projects
```
**查询参数：**
- `page`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 20)
- `keyword`: 搜索关键词
- `status`: 项目状态

**响应数据：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 100,
        "page": 1,
        "size": 20,
        "items": [
            {
                "id": 1,
                "name": "项目名称",
                "description": "项目描述",
                "owner": {
                    "id": 1,
                    "username": "admin",
                    "real_name": "管理员"
                },
                "status": 1,
                "api_count": 50,
                "case_count": 120,
                "last_test_result": {
                    "pass_rate": 85.5,
                    "total_cases": 120,
                    "passed_cases": 102,
                    "failed_cases": 18
                },
                "created_at": "2024-01-01 10:00:00",
                "updated_at": "2024-01-15 15:30:00"
            }
        ]
    }
}
```

#### 2.2 创建项目
```
POST /api/projects
```
**请求参数：**
```json
{
    "name": "string",         // 项目名称
    "description": "string"   // 项目描述
}
```

#### 2.3 获取项目详情
```
GET /api/projects/{id}
```

#### 2.4 更新项目
```
PUT /api/projects/{id}
```

#### 2.5 删除项目
```
DELETE /api/projects/{id}
```

#### 2.6 获取项目成员列表
```
GET /api/projects/{id}/members
```

#### 2.7 添加项目成员
```
POST /api/projects/{id}/members
```
**请求参数：**
```json
{
    "user_id": 1,
    "role": "developer"  // admin, developer, tester
}
```

#### 2.8 移除项目成员
```
DELETE /api/projects/{id}/members/{user_id}
```

### 3. 环境配置接口

#### 3.1 获取环境列表
```
GET /api/projects/{project_id}/environments
```

#### 3.2 创建环境
```
POST /api/projects/{project_id}/environments
```
**请求参数：**
```json
{
    "name": "string",              // 环境名称
    "base_url": "string",          // 基础URL
    "global_variables": {          // 全局变量
        "host": "api.example.com",
        "version": "v1"
    },
    "global_headers": {            // 全局请求头
        "Content-Type": "application/json",
        "User-Agent": "ApiTest/1.0"
    },
    "auth_config": {               // 认证配置
        "type": "bearer",          // bearer, basic, apikey
        "token": "string"
    },
    "is_default": false
}
```

#### 3.3 更新环境
```
PUT /api/environments/{id}
```

#### 3.4 删除环境
```
DELETE /api/environments/{id}
```

### 4. 接口管理接口

#### 4.1 获取接口分组列表
```
GET /api/projects/{project_id}/api-groups
```

#### 4.2 创建接口分组
```
POST /api/projects/{project_id}/api-groups
```
**请求参数：**
```json
{
    "name": "string",
    "description": "string",
    "parent_id": 1,        // 可选，父分组ID
    "sort_order": 0
}
```

#### 4.3 获取接口列表
```
GET /api/projects/{project_id}/apis
```
**查询参数：**
- `group_id`: 分组ID
- `method`: 请求方法
- `keyword`: 搜索关键词
- `status`: 接口状态
- `page`: 页码
- `size`: 每页数量

#### 4.4 创建接口
```
POST /api/projects/{project_id}/apis
```
**请求参数：**
```json
{
    "group_id": 1,
    "name": "用户登录接口",
    "method": "POST",
    "path": "/api/auth/login",
    "description": "用户登录接口描述",
    "tags": ["auth", "user"],
    "request_headers": [
        {
            "name": "Content-Type",
            "value": "application/json",
            "required": true,
            "description": "请求内容类型"
        }
    ],
    "query_params": [
        {
            "name": "version",
            "type": "string",
            "required": false,
            "default_value": "v1",
            "description": "API版本"
        }
    ],
    "path_params": [],
    "request_body": {
        "type": "json",
        "schema": {
            "type": "object",
            "properties": {
                "username": {
                    "type": "string",
                    "description": "用户名"
                },
                "password": {
                    "type": "string",
                    "description": "密码"
                }
            },
            "required": ["username", "password"]
        },
        "example": {
            "username": "admin",
            "password": "123456"
        }
    },
    "response_definition": {
        "200": {
            "description": "登录成功",
            "headers": {},
            "schema": {
                "type": "object",
                "properties": {
                    "code": {"type": "integer"},
                    "message": {"type": "string"},
                    "data": {
                        "type": "object",
                        "properties": {
                            "token": {"type": "string"},
                            "user": {"type": "object"}
                        }
                    }
                }
            },
            "example": {
                "code": 200,
                "message": "登录成功",
                "data": {
                    "token": "jwt_token_here",
                    "user": {
                        "id": 1,
                        "username": "admin"
                    }
                }
            }
        },
        "400": {
            "description": "请求参数错误",
            "schema": {
                "type": "object",
                "properties": {
                    "code": {"type": "integer"},
                    "message": {"type": "string"}
                }
            }
        }
    }
}
```

#### 4.5 获取接口详情
```
GET /api/apis/{id}
```

#### 4.6 更新接口
```
PUT /api/apis/{id}
```

#### 4.7 删除接口
```
DELETE /api/apis/{id}
```

#### 4.8 接口调试
```
POST /api/apis/{id}/debug
```
**请求参数：**
```json
{
    "environment_id": 1,
    "request_data": {
        "headers": {"Content-Type": "application/json"},
        "query_params": {"version": "v1"},
        "path_params": {},
        "body": {"username": "test", "password": "123456"}
    }
}
```

#### 4.9 批量导入接口 (Swagger/OpenAPI)
```
POST /api/projects/{project_id}/apis/import
```
**请求参数：**
```json
{
    "import_type": "swagger",  // swagger, openapi, postman
    "file_url": "string",      // 文件URL或上传的文件ID
    "group_id": 1,             // 目标分组ID
    "overwrite": false         // 是否覆盖同名接口
}
```

### 5. 测试用例管理接口

#### 5.1 获取测试用例列表
```
GET /api/projects/{project_id}/test-cases
```
**查询参数：**
- `api_id`: 关联接口ID
- `status`: 用例状态
- `priority`: 优先级
- `keyword`: 搜索关键词
- `page`: 页码
- `size`: 每页数量

#### 5.2 创建测试用例
```
POST /api/projects/{project_id}/test-cases
```
**请求参数：**
```json
{
    "api_id": 1,
    "name": "用户登录成功用例",
    "description": "测试用户正常登录流程",
    "priority": "high",
    "pre_operations": [
        {
            "type": "sql",
            "config": {
                "database": "test_db",
                "sql": "DELETE FROM users WHERE username = 'test_user'"
            }
        },
        {
            "type": "script",
            "config": {
                "language": "javascript",
                "code": "const token = await getAuthToken();\nsetVariable('auth_token', token);"
            }
        }
    ],
    "request_config": {
        "headers": {
            "Authorization": "Bearer {{auth_token}}"
        },
        "query_params": {},
        "path_params": {},
        "body": {
            "username": "{{username}}",
            "password": "{{password}}"
        }
    },
    "assertions": [
        {
            "type": "status_code",
            "expected": 200,
            "description": "响应状态码应为200"
        },
        {
            "type": "json_path",
            "path": "$.code",
            "operator": "equals",
            "expected": 200,
            "description": "响应code字段应为200"
        },
        {
            "type": "json_path",
            "path": "$.data.token",
            "operator": "not_empty",
            "description": "token字段不应为空"
        },
        {
            "type": "response_time",
            "operator": "less_than",
            "expected": 1000,
            "description": "响应时间应小于1000ms"
        }
    ],
    "post_operations": [
        {
            "type": "extract_variable",
            "config": {
                "variable_name": "user_token",
                "json_path": "$.data.token"
            }
        },
        {
            "type": "database_check",
            "config": {
                "database": "test_db",
                "sql": "SELECT COUNT(*) as count FROM user_sessions WHERE user_id = 1",
                "assertion": {
                    "path": "$.count",
                    "operator": "greater_than",
                    "expected": 0
                }
            }
        }
    ],
    "data_driven_config": {
        "enabled": true,
        "dataset_id": 1,
        "parameter_mapping": {
            "username": "user_name",
            "password": "user_password"
        }
    }
}
```

#### 5.3 获取测试用例详情
```
GET /api/test-cases/{id}
```

#### 5.4 更新测试用例
```
PUT /api/test-cases/{id}
```

#### 5.5 删除测试用例
```
DELETE /api/test-cases/{id}
```

#### 5.6 执行单个测试用例
```
POST /api/test-cases/{id}/execute
```
**请求参数：**
```json
{
    "environment_id": 1,
    "variables": {
        "custom_var": "value"
    }
}
```

#### 5.7 获取用例执行历史
```
GET /api/test-cases/{id}/executions
```

### 6. 测试计划管理接口

#### 6.1 获取测试计划列表
```
GET /api/projects/{project_id}/test-plans
```

#### 6.2 创建测试计划
```
POST /api/projects/{project_id}/test-plans
```
**请求参数：**
```json
{
    "name": "冒烟测试计划",
    "description": "每日冒烟测试",
    "environment_id": 1,
    "case_ids": [1, 2, 3, 4, 5],
    "schedule_type": "cron",
    "schedule_config": {
        "cron_expression": "0 9 * * 1-5",  // 工作日上午9点执行
        "timezone": "Asia/Shanghai"
    },
    "notification_config": {
        "enabled": true,
        "email": {
            "enabled": true,
            "recipients": ["<EMAIL>"],
            "on_success": false,
            "on_failure": true
        },
        "webhook": {
            "enabled": true,
            "url": "https://hooks.slack.com/xxx",
            "on_success": true,
            "on_failure": true
        }
    },
    "retry_config": {
        "enabled": true,
        "max_retries": 3,
        "retry_interval": 60,  // 重试间隔(秒)
        "retry_on_failure_only": true
    }
}
```

#### 6.3 执行测试计划
```
POST /api/test-plans/{id}/execute
```

#### 6.4 获取测试计划执行历史
```
GET /api/test-plans/{id}/executions
```

#### 6.5 获取测试计划执行详情
```
GET /api/test-plan-executions/{execution_id}
```

#### 6.6 停止测试计划执行
```
POST /api/test-plan-executions/{execution_id}/stop
```

### 7. Mock服务接口

#### 7.1 获取Mock规则列表
```
GET /api/projects/{project_id}/mock-rules
```

#### 7.2 创建Mock规则
```
POST /api/projects/{project_id}/mock-rules
```
**请求参数：**
```json
{
    "name": "用户信息Mock",
    "description": "模拟用户信息接口",
    "method": "GET",
    "path_pattern": "/api/users/*",
    "match_conditions": {
        "headers": {
            "Authorization": "required"
        },
        "query_params": {
            "id": "number"
        }
    },
    "response_status": 200,
    "response_headers": {
        "Content-Type": "application/json",
        "X-Mock": "true"
    },
    "response_body": "{\n  \"code\": 200,\n  \"message\": \"success\",\n  \"data\": {\n    \"id\": {{$randomInt(1,1000)}},\n    \"name\": \"{{$randomName}}\",\n    \"email\": \"{{$randomEmail}}\",\n    \"created_at\": \"{{$timestamp}}\"\n  }\n}",
    "response_delay": 100
}
```

#### 7.3 更新Mock规则
```
PUT /api/mock-rules/{id}
```

#### 7.4 删除Mock规则
```
DELETE /api/mock-rules/{id}
```

#### 7.5 启用/禁用Mock规则
```
PATCH /api/mock-rules/{id}/toggle
```

#### 7.6 获取Mock请求日志
```
GET /api/mock-rules/{id}/logs
```

### 8. 性能测试接口

#### 8.1 获取性能测试列表
```
GET /api/projects/{project_id}/performance-tests
```

#### 8.2 创建性能测试
```
POST /api/projects/{project_id}/performance-tests
```
**请求参数：**
```json
{
    "name": "用户登录性能测试",
    "description": "测试用户登录接口的性能表现",
    "target_api_id": 1,
    "environment_id": 1,
    "concurrent_users": 100,
    "duration_seconds": 300,
    "ramp_up_seconds": 60,
    "test_data_config": {
        "data_source": "dataset",
        "dataset_id": 1,
        "data_distribution": "random"  // sequential, random
    },
    "performance_thresholds": {
        "avg_response_time": 500,      // 平均响应时间阈值(ms)
        "p95_response_time": 1000,     // P95响应时间阈值(ms)
        "error_rate": 1.0,             // 错误率阈值(%)
        "min_tps": 50                  // 最小TPS阈值
    }
}
```

#### 8.3 执行性能测试
```
POST /api/performance-tests/{id}/execute
```

#### 8.4 获取性能测试执行历史
```
GET /api/performance-tests/{id}/executions
```

#### 8.5 获取性能测试报告
```
GET /api/performance-test-executions/{execution_id}/report
```

#### 8.6 停止性能测试
```
POST /api/performance-test-executions/{execution_id}/stop
```

### 9. 数据驱动测试接口

#### 9.1 获取测试数据集列表
```
GET /api/projects/{project_id}/test-datasets
```

#### 9.2 创建测试数据集
```
POST /api/projects/{project_id}/test-datasets
```
**请求参数：**
```json
{
    "name": "用户登录测试数据",
    "description": "用于用户登录测试的数据集",
    "data_type": "csv",
    "data_source": {
        "type": "upload",  // upload, sql, api
        "file_id": 123
    },
    "schema_definition": {
        "columns": [
            {
                "name": "username",
                "type": "string",
                "description": "用户名"
            },
            {
                "name": "password",
                "type": "string",
                "description": "密码"
            },
            {
                "name": "expected_result",
                "type": "string",
                "description": "期望结果"
            }
        ]
    }
}
```

#### 9.3 上传数据文件
```
POST /api/files/upload
```
**请求参数：** multipart/form-data
- `file`: 文件
- `usage_type`: 使用类型 (test_data)
- `project_id`: 项目ID

### 10. 系统管理接口

#### 10.1 获取用户列表
```
GET /api/admin/users
```

#### 10.2 创建用户
```
POST /api/admin/users
```

#### 10.3 获取操作日志
```
GET /api/admin/operation-logs
```

#### 10.4 获取系统配置
```
GET /api/admin/system-configs
```
**查询参数：**
- `category`: 配置分类 (basic, security, feature, storage, notification)
- `is_public`: 是否公开配置

**响应数据：**
```json
{
    "code": 200,
    "data": {
        "basic": {
            "system_name": "API测试平台",
            "system_version": "1.0.0",
            "system_logo": "logo_url",
            "system_description": "专业的API接口自动化测试平台",
            "company_name": "Your Company",
            "contact_email": "<EMAIL>",
            "copyright": "© 2024 Your Company. All rights reserved."
        },
        "security": {
            "password_min_length": 8,
            "password_complexity": "medium",
            "login_lock_enabled": true,
            "max_login_attempts": 5,
            "session_timeout": 120,
            "force_https": false,
            "ip_whitelist": ""
        },
        "feature": {
            "user_registration_enabled": true,
            "email_verification_enabled": true,
            "mock_service_enabled": true,
            "performance_test_enabled": true,
            "cicd_integration_enabled": true,
            "data_driven_test_enabled": true,
            "max_projects_per_user": 10,
            "max_concurrent_executions": 5,
            "max_file_size": 10,
            "allowed_file_types": "jpg,png,pdf,csv,json,xml"
        },
        "storage": {
            "storage_type": "local",
            "data_retention_days": 90,
            "local_storage_path": "/var/www/uploads",
            "cloud_access_key": "",
            "cloud_secret_key": "",
            "cloud_bucket": "",
            "cloud_region": ""
        }
    }
}
```

#### 10.5 更新系统配置
```
PUT /api/admin/system-configs
```
**请求参数：**
```json
{
    "configs": {
        "basic": {
            "system_name": "API测试平台",
            "system_description": "专业的API接口自动化测试平台"
        },
        "security": {
            "password_min_length": 8,
            "login_lock_enabled": true
        }
    },
    "change_reason": "更新系统基本信息和安全设置"
}
```

#### 10.6 获取单个配置项
```
GET /api/admin/system-configs/{key}
```

#### 10.7 更新单个配置项
```
PUT /api/admin/system-configs/{key}
```
**请求参数：**
```json
{
    "value": "new_value",
    "change_reason": "更新原因"
}
```

#### 10.8 恢复默认配置
```
POST /api/admin/system-configs/reset-default
```
**请求参数：**
```json
{
    "categories": ["basic", "security"],  // 可选，指定要恢复的分类
    "confirm": true
}
```

#### 10.9 获取配置历史
```
GET /api/admin/system-configs/history
```
**查询参数：**
- `config_key`: 配置键
- `operator_id`: 操作人ID
- `start_date`: 开始日期
- `end_date`: 结束日期
- `page`: 页码
- `size`: 每页数量

#### 10.10 恢复历史配置
```
POST /api/admin/system-configs/restore/{history_id}
```

#### 10.11 导出系统配置
```
GET /api/admin/system-configs/export
```
**查询参数：**
- `categories`: 要导出的分类，多个用逗号分隔
- `format`: 导出格式 (json, yaml)

#### 10.12 导入系统配置
```
POST /api/admin/system-configs/import
```
**请求参数：** multipart/form-data
- `file`: 配置文件
- `overwrite`: 是否覆盖现有配置
- `backup`: 是否备份当前配置

#### 10.13 获取系统状态
```
GET /api/admin/system/status
```
**响应数据：**
```json
{
    "code": 200,
    "data": {
        "uptime": "72小时15分钟",
        "cpu_usage": 45.2,
        "memory_usage": 68.5,
        "disk_usage": 32.1,
        "network_in": 1024000,
        "network_out": 2048000,
        "active_connections": 150,
        "database_status": "connected",
        "database_connections": 10,
        "redis_status": "connected",
        "redis_connections": 5,
        "queue_size": 25,
        "error_count": 2,
        "warning_count": 8,
        "last_updated": "2024-01-15 14:30:00"
    }
}
```

#### 10.14 清理系统缓存
```
POST /api/admin/system/clear-cache
```
**请求参数：**
```json
{
    "cache_types": ["redis", "file", "database"],  // 可选，指定要清理的缓存类型
    "confirm": true
}
```

#### 10.15 重启系统服务
```
POST /api/admin/system/restart
```
**请求参数：**
```json
{
    "services": ["web", "worker", "scheduler"],  // 可选，指定要重启的服务
    "confirm": true,
    "delay_seconds": 10  // 延迟重启时间
}
```

#### 10.16 获取系统监控数据
```
GET /api/admin/system/monitoring
```
**查询参数：**
- `start_time`: 开始时间
- `end_time`: 结束时间
- `interval`: 数据间隔 (1m, 5m, 15m, 1h, 1d)
- `metrics`: 指标类型，多个用逗号分隔 (cpu, memory, disk, network)

#### 10.17 系统健康检查
```
GET /api/health/check
```
**响应数据：**
```json
{
    "code": 200,
    "data": {
        "status": "healthy",
        "checks": {
            "database": {
                "status": "healthy",
                "response_time": 15,
                "details": "MySQL connection successful"
            },
            "redis": {
                "status": "healthy",
                "response_time": 5,
                "details": "Redis connection successful"
            },
            "disk_space": {
                "status": "warning",
                "usage": 85,
                "details": "Disk usage is high"
            },
            "memory": {
                "status": "healthy",
                "usage": 68,
                "details": "Memory usage is normal"
            }
        },
        "timestamp": "2024-01-15 14:30:00"
    }
}
```

### 11. 报告和统计接口

#### 11.1 获取项目统计数据
```
GET /api/projects/{project_id}/statistics
```
**响应数据：**
```json
{
    "code": 200,
    "data": {
        "api_count": 150,
        "case_count": 300,
        "execution_count": 1500,
        "success_rate": 92.5,
        "recent_executions": [
            {
                "date": "2024-01-15",
                "total": 50,
                "passed": 46,
                "failed": 4
            }
        ],
        "top_failed_apis": [
            {
                "api_name": "用户注册",
                "failure_count": 15,
                "failure_rate": 30.0
            }
        ]
    }
}
```

#### 11.2 生成测试报告
```
POST /api/test-plan-executions/{execution_id}/report
```
**请求参数：**
```json
{
    "format": "html",  // html, pdf, json
    "include_details": true,
    "include_charts": true
}
```

## 三、接口规范说明

### 1. 通用响应格式

所有API接口都应遵循统一的响应格式：

```json
{
    "code": 200,                    // 业务状态码
    "message": "success",           // 响应消息
    "data": {},                     // 响应数据
    "timestamp": 1640995200000,     // 时间戳
    "request_id": "uuid"            // 请求ID，用于链路追踪
}
```

### 2. HTTP状态码规范

- `200 OK`: 请求成功
- `201 Created`: 资源创建成功
- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 未授权
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `409 Conflict`: 资源冲突
- `422 Unprocessable Entity`: 数据验证失败
- `500 Internal Server Error`: 服务器内部错误

### 3. 业务状态码规范

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未登录或token过期
- `403`: 权限不足
- `404`: 资源不存在
- `409`: 资源冲突（如用户名已存在）
- `422`: 数据验证失败
- `500`: 服务器内部错误

### 4. 分页响应格式

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 100,           // 总记录数
        "page": 1,              // 当前页码
        "size": 20,             // 每页数量
        "pages": 5,             // 总页数
        "items": []             // 数据列表
    }
}
```

### 5. 错误响应格式

```json
{
    "code": 400,
    "message": "请求参数错误",
    "errors": [                 // 详细错误信息（可选）
        {
            "field": "username",
            "message": "用户名不能为空"
        }
    ],
    "timestamp": 1640995200000,
    "request_id": "uuid"
}
```

## 四、技术要求和建议

### 1. 认证授权

- 使用JWT (JSON Web Token) 进行用户认证
- Token有效期建议设置为2小时，提供refresh token机制
- 实现基于角色的权限控制 (RBAC)
- 敏感操作需要二次验证

### 2. 数据库设计

- 使用MySQL 8.0+或PostgreSQL 13+
- 所有表都应包含created_at和updated_at字段
- 使用软删除机制，避免物理删除重要数据
- 合理设置索引，优化查询性能
- 使用事务确保数据一致性

### 3. 接口设计

- 遵循RESTful API设计规范
- 使用合适的HTTP方法 (GET/POST/PUT/DELETE)
- URL命名使用小写字母和连字符
- 支持请求参数验证和数据校验
- 实现接口限流和防重复提交

### 4. 安全要求

- 所有密码使用bcrypt等安全哈希算法加密
- 实现SQL注入防护
- 对用户输入进行XSS过滤
- 敏感数据传输使用HTTPS
- 实现接口访问日志记录

### 5. 性能优化

- 数据库查询优化，避免N+1问题
- 实现Redis缓存机制
- 大数据量接口支持分页查询
- 文件上传支持分片上传
- 实现异步任务处理机制

### 6. 监控和日志

- 实现接口访问日志记录
- 集成应用性能监控 (APM)
- 实现错误日志收集和告警
- 提供健康检查接口
- 支持链路追踪

### 7. 部署和运维

- 支持Docker容器化部署
- 提供数据库迁移脚本
- 实现配置文件外部化
- 支持多环境配置
- 提供API文档 (Swagger/OpenAPI)

## 五、开发优先级建议

### 第一阶段 (核心功能)
1. 用户认证授权系统
2. 项目管理功能
3. 接口管理功能
4. 基础的测试用例管理

### 第二阶段 (测试执行)
1. 测试用例执行引擎
2. 测试计划管理
3. 环境配置管理
4. 基础报告功能

### 第三阶段 (高级功能)
1. Mock服务
2. 数据驱动测试
3. 性能测试
4. 高级报告和统计

### 第四阶段 (扩展功能)
1. CI/CD集成
2. 第三方工具集成
3. 高级监控和告警
4. 插件系统

## 六、前端需要的额外接口

### 1. 实时通信接口
```
WebSocket /ws/notifications
```
用于实时推送测试执行状态、系统通知等

### 2. 文件下载接口
```
GET /api/files/{file_id}/download
```
用于下载测试报告、导出数据等

### 3. 系统信息接口
```
GET /api/system/info
```
获取系统版本、状态等信息

### 4. 搜索建议接口
```
GET /api/search/suggestions
```
提供搜索自动完成功能

这个接口文档涵盖了接口自动化测试平台的所有核心功能，前端开发团队可以根据这个文档进行开发，后端团队也可以按照这个规范实现相应的接口。

## 七、新增功能模块

### 1. CI/CD集成模块

#### CI/CD集成配置表 (cicd_integrations)
```sql
CREATE TABLE cicd_integrations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '集成ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    name VARCHAR(200) NOT NULL COMMENT '集成名称',
    description TEXT COMMENT '集成描述',
    integration_type ENUM('gitlab', 'github', 'jenkins', 'azure') NOT NULL COMMENT '集成类型',
    repository_url VARCHAR(500) NOT NULL COMMENT '项目地址',
    branch VARCHAR(100) DEFAULT 'main' COMMENT '分支',
    trigger_conditions JSON COMMENT '触发条件',
    auth_config JSON COMMENT '认证配置',
    script_config TEXT COMMENT '集成脚本',
    webhook_url VARCHAR(500) COMMENT 'Webhook地址',
    webhook_secret VARCHAR(255) COMMENT 'Webhook密钥',
    status TINYINT DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    last_triggered_at DATETIME COMMENT '最后触发时间',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_project_id (project_id),
    INDEX idx_integration_type (integration_type),
    INDEX idx_creator_id (creator_id),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT
) COMMENT='CI/CD集成配置表';
```

#### CI/CD执行记录表 (cicd_execution_logs)
```sql
CREATE TABLE cicd_execution_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '执行记录ID',
    integration_id BIGINT NOT NULL COMMENT '集成ID',
    trigger_type ENUM('push', 'merge_request', 'tag', 'schedule', 'manual') NOT NULL COMMENT '触发类型',
    trigger_data JSON COMMENT '触发数据',
    execution_status ENUM('pending', 'running', 'success', 'failed', 'cancelled') NOT NULL COMMENT '执行状态',
    test_plan_id BIGINT COMMENT '关联测试计划ID',
    execution_result JSON COMMENT '执行结果',
    error_message TEXT COMMENT '错误信息',
    started_at DATETIME NOT NULL COMMENT '开始时间',
    finished_at DATETIME COMMENT '结束时间',
    INDEX idx_integration_id (integration_id),
    INDEX idx_trigger_type (trigger_type),
    INDEX idx_execution_status (execution_status),
    INDEX idx_started_at (started_at),
    FOREIGN KEY (integration_id) REFERENCES cicd_integrations(id) ON DELETE CASCADE,
    FOREIGN KEY (test_plan_id) REFERENCES test_plans(id) ON DELETE SET NULL
) COMMENT='CI/CD执行记录表';
```

### 2. 邮件配置模块

#### 邮件配置表 (email_configs)
```sql
CREATE TABLE email_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    enabled TINYINT DEFAULT 1 COMMENT '是否启用邮件服务',
    smtp_host VARCHAR(255) NOT NULL COMMENT 'SMTP服务器地址',
    smtp_port INT NOT NULL COMMENT 'SMTP端口',
    smtp_username VARCHAR(255) NOT NULL COMMENT 'SMTP用户名',
    smtp_password VARCHAR(255) NOT NULL COMMENT 'SMTP密码(加密存储)',
    sender_name VARCHAR(100) DEFAULT 'API测试平台' COMMENT '发件人名称',
    sender_email VARCHAR(255) NOT NULL COMMENT '发件人邮箱',
    security_type ENUM('none', 'tls', 'ssl') DEFAULT 'tls' COMMENT '安全连接类型',
    connection_timeout INT DEFAULT 30 COMMENT '连接超时时间(秒)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='邮件配置表';
```

#### 邮件模板表 (email_templates)
```sql
CREATE TABLE email_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '模板ID',
    template_type ENUM('test_complete', 'test_failed', 'system_maintenance', 'user_invite') NOT NULL COMMENT '模板类型',
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    subject VARCHAR(255) NOT NULL COMMENT '邮件主题模板',
    content TEXT NOT NULL COMMENT '邮件内容模板',
    variables JSON COMMENT '支持的变量列表',
    is_default TINYINT DEFAULT 0 COMMENT '是否为默认模板',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_template_type (template_type)
) COMMENT='邮件模板表';
```

#### 邮件发送记录表 (email_logs)
```sql
CREATE TABLE email_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    template_type VARCHAR(50) COMMENT '模板类型',
    subject VARCHAR(255) NOT NULL COMMENT '邮件主题',
    recipient VARCHAR(255) NOT NULL COMMENT '收件人邮箱',
    recipient_name VARCHAR(100) COMMENT '收件人姓名',
    content TEXT COMMENT '邮件内容',
    status ENUM('pending', 'sending', 'success', 'failed') NOT NULL COMMENT '发送状态',
    error_message TEXT COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    sent_at DATETIME COMMENT '发送时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_recipient (recipient),
    INDEX idx_status (status),
    INDEX idx_sent_at (sent_at),
    INDEX idx_template_type (template_type)
) COMMENT='邮件发送记录表';
```

#### 邮件通知规则表 (email_notification_rules)
```sql
CREATE TABLE email_notification_rules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '规则ID',
    project_id BIGINT COMMENT '项目ID(NULL表示全局规则)',
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    trigger_event ENUM('test_complete', 'test_failed', 'performance_alert', 'system_maintenance') NOT NULL COMMENT '触发事件',
    conditions JSON COMMENT '触发条件',
    recipients JSON NOT NULL COMMENT '收件人列表',
    template_type VARCHAR(50) NOT NULL COMMENT '邮件模板类型',
    enabled TINYINT DEFAULT 1 COMMENT '是否启用',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_project_id (project_id),
    INDEX idx_trigger_event (trigger_event),
    INDEX idx_enabled (enabled),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT
) COMMENT='邮件通知规则表';
```

### 3. 数据驱动测试增强

#### 更新测试数据集表结构
```sql
-- 为测试数据集表添加新字段
ALTER TABLE test_datasets
ADD COLUMN data_source_config JSON COMMENT '数据源配置',
ADD COLUMN refresh_interval INT DEFAULT 0 COMMENT '数据刷新间隔(分钟)',
ADD COLUMN last_refreshed_at DATETIME COMMENT '最后刷新时间';

-- 更新数据类型枚举
ALTER TABLE test_datasets
MODIFY COLUMN data_type ENUM('csv', 'json', 'sql', 'api', 'faker', 'xml') NOT NULL COMMENT '数据类型';
```

#### 数据生成规则表 (data_generation_rules)
```sql
CREATE TABLE data_generation_rules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '规则ID',
    dataset_id BIGINT NOT NULL COMMENT '数据集ID',
    field_name VARCHAR(100) NOT NULL COMMENT '字段名称',
    field_type ENUM('string', 'number', 'email', 'phone', 'name', 'datetime', 'uuid', 'boolean') NOT NULL COMMENT '字段类型',
    generation_config JSON COMMENT '生成配置',
    validation_rules JSON COMMENT '验证规则',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_dataset_id (dataset_id),
    FOREIGN KEY (dataset_id) REFERENCES test_datasets(id) ON DELETE CASCADE
) COMMENT='数据生成规则表';
```

## 八、新增API接口

### 1. CI/CD集成管理接口

#### 1.1 获取CI/CD集成列表
```
GET /api/projects/{project_id}/cicd-integrations
```

#### 1.2 创建CI/CD集成
```
POST /api/projects/{project_id}/cicd-integrations
```
**请求参数：**
```json
{
    "name": "主分支自动测试",
    "description": "主分支代码推送时自动执行测试",
    "integration_type": "gitlab",
    "repository_url": "https://gitlab.com/company/project.git",
    "branch": "main",
    "trigger_conditions": ["push", "merge_request"],
    "auth_config": {
        "username": "api_user",
        "token": "gitlab_token"
    },
    "script_config": "# GitLab CI配置\nstages:\n  - test\n...",
    "test_plan_id": 1
}
```

#### 1.3 更新CI/CD集成
```
PUT /api/cicd-integrations/{id}
```

#### 1.4 删除CI/CD集成
```
DELETE /api/cicd-integrations/{id}
```

#### 1.5 测试CI/CD集成连接
```
POST /api/cicd-integrations/{id}/test-connection
```

#### 1.6 启用/禁用CI/CD集成
```
PATCH /api/cicd-integrations/{id}/toggle
```

#### 1.7 获取CI/CD执行记录
```
GET /api/cicd-integrations/{id}/execution-logs
```

#### 1.8 CI/CD Webhook接口
```
POST /api/webhooks/cicd/{integration_id}
```
用于接收来自CI/CD平台的Webhook通知

### 2. 邮件配置管理接口

#### 2.1 获取邮件配置
```
GET /api/system/email/config
```
**响应数据：**
```json
{
    "code": 200,
    "data": {
        "enabled": true,
        "smtp_host": "smtp.qq.com",
        "smtp_port": 587,
        "smtp_username": "<EMAIL>",
        "sender_name": "API测试平台",
        "sender_email": "<EMAIL>",
        "security_type": "tls",
        "connection_timeout": 30
    }
}
```

#### 2.2 保存邮件配置
```
POST /api/system/email/config
```
**请求参数：**
```json
{
    "enabled": true,
    "smtp_host": "smtp.qq.com",
    "smtp_port": 587,
    "smtp_username": "<EMAIL>",
    "smtp_password": "password123",
    "sender_name": "API测试平台",
    "sender_email": "<EMAIL>",
    "security_type": "tls",
    "connection_timeout": 30
}
```

#### 2.3 测试邮件连接
```
POST /api/system/email/test-connection
```
**请求参数：**
```json
{
    "smtp_host": "smtp.qq.com",
    "smtp_port": 587,
    "smtp_username": "<EMAIL>",
    "smtp_password": "password123",
    "security_type": "tls"
}
```

#### 2.4 获取邮件模板列表
```
GET /api/system/email/templates
```
**响应数据：**
```json
{
    "code": 200,
    "data": [
        {
            "id": 1,
            "template_type": "test_complete",
            "template_name": "测试完成通知",
            "subject": "测试执行完成通知 - {{project_name}}",
            "content": "测试计划执行完成...",
            "variables": ["project_name", "test_plan_name", "success_rate"]
        }
    ]
}
```

#### 2.5 保存邮件模板
```
POST /api/system/email/templates
```
**请求参数：**
```json
{
    "templates": [
        {
            "template_type": "test_complete",
            "subject": "测试执行完成通知 - {{project_name}}",
            "content": "测试计划执行完成..."
        }
    ]
}
```

#### 2.6 发送测试邮件
```
POST /api/system/email/send-test
```
**请求参数：**
```json
{
    "recipient": "<EMAIL>",
    "template_type": "test_complete",
    "variables": {
        "project_name": "测试项目",
        "test_plan_name": "冒烟测试",
        "success_rate": "95.5"
    }
}
```

#### 2.7 获取邮件发送记录
```
GET /api/system/email/logs
```
**查询参数：**
- `page`: 页码
- `size`: 每页数量
- `status`: 发送状态
- `start_date`: 开始日期
- `end_date`: 结束日期

#### 2.8 获取邮件通知规则
```
GET /api/projects/{project_id}/email/notification-rules
```

#### 2.9 创建邮件通知规则
```
POST /api/projects/{project_id}/email/notification-rules
```
**请求参数：**
```json
{
    "rule_name": "测试失败通知",
    "trigger_event": "test_failed",
    "conditions": {
        "failure_rate": ">10%"
    },
    "recipients": [
        {
            "email": "<EMAIL>",
            "name": "管理员"
        }
    ],
    "template_type": "test_failed"
}
```

#### 2.10 更新邮件通知规则
```
PUT /api/email/notification-rules/{id}
```

#### 2.11 删除邮件通知规则
```
DELETE /api/email/notification-rules/{id}
```

### 3. 数据驱动测试增强接口

#### 3.1 获取测试数据集列表（增强版）
```
GET /api/projects/{project_id}/test-datasets
```
**查询参数：**
- `page`: 页码
- `size`: 每页数量
- `keyword`: 搜索关键词
- `data_type`: 数据类型筛选
- `creator_id`: 创建人筛选

**响应数据：**
```json
{
    "code": 200,
    "data": {
        "total": 50,
        "page": 1,
        "size": 20,
        "items": [
            {
                "id": 1,
                "name": "用户登录测试数据",
                "description": "用于用户登录测试的数据集",
                "data_type": "csv",
                "data_source_config": {
                    "type": "upload",
                    "file_id": 123
                },
                "refresh_interval": 60,
                "last_refreshed_at": "2024-01-15 10:30:00",
                "creator": {
                    "id": 1,
                    "username": "admin",
                    "real_name": "管理员"
                },
                "created_at": "2024-01-10 09:00:00",
                "updated_at": "2024-01-15 10:30:00"
            }
        ]
    }
}
```

#### 2.1 创建SQL数据源
```
POST /api/projects/{project_id}/test-datasets/sql
```
**请求参数：**
```json
{
    "name": "用户测试数据",
    "description": "从数据库获取用户测试数据",
    "data_source_config": {
        "host": "localhost",
        "port": 3306,
        "database": "test_db",
        "username": "test_user",
        "password": "test_password",
        "query": "SELECT username, password FROM test_users WHERE status = 'active'"
    },
    "refresh_interval": 60
}
```

#### 2.2 创建API数据源
```
POST /api/projects/{project_id}/test-datasets/api
```
**请求参数：**
```json
{
    "name": "API测试数据",
    "description": "从API接口获取测试数据",
    "data_source_config": {
        "method": "GET",
        "url": "https://api.example.com/test-data",
        "headers": {
            "Authorization": "Bearer token"
        },
        "response_path": "$.data.items"
    },
    "refresh_interval": 30
}
```

#### 2.3 创建Faker数据源
```
POST /api/projects/{project_id}/test-datasets/faker
```
**请求参数：**
```json
{
    "name": "模拟用户数据",
    "description": "使用Faker生成模拟用户数据",
    "data_source_config": {
        "count": 1000,
        "fields": [
            {
                "name": "username",
                "type": "string",
                "config": {"length": 8, "pattern": "[a-z0-9]+"}
            },
            {
                "name": "email",
                "type": "email",
                "config": {"domain": "example.com"}
            },
            {
                "name": "phone",
                "type": "phone",
                "config": {"format": "1##########"}
            }
        ]
    }
}
```

#### 2.4 刷新数据集
```
POST /api/test-datasets/{id}/refresh
```

#### 3.5 预览数据集
```
GET /api/test-datasets/{id}/preview
```
**查询参数：**
- `limit`: 预览条数 (默认: 10)

#### 3.6 删除测试数据集
```
DELETE /api/test-datasets/{id}
```

#### 3.7 更新测试数据集
```
PUT /api/test-datasets/{id}
```
**请求参数：**
```json
{
    "name": "更新后的数据集名称",
    "description": "更新后的描述",
    "data_source_config": {
        "type": "sql",
        "host": "localhost",
        "port": 3306,
        "database": "updated_db",
        "query": "SELECT * FROM updated_table"
    },
    "refresh_interval": 120
}
```

#### 3.8 获取数据生成规则
```
GET /api/test-datasets/{dataset_id}/generation-rules
```

#### 3.9 创建数据生成规则
```
POST /api/test-datasets/{dataset_id}/generation-rules
```
**请求参数：**
```json
{
    "field_name": "username",
    "field_type": "string",
    "generation_config": {
        "length": 8,
        "pattern": "[a-z0-9]+",
        "prefix": "user_"
    },
    "validation_rules": {
        "required": true,
        "unique": true
    }
}
```

#### 3.10 更新数据生成规则
```
PUT /api/data-generation-rules/{id}
```

#### 3.11 删除数据生成规则
```
DELETE /api/data-generation-rules/{id}
```

### 4. 个人设置相关接口

#### 4.1 获取个人信息
```
GET /api/profile
```

#### 4.2 更新个人信息
```
PUT /api/profile
```
**请求参数：**
```json
{
    "real_name": "张三",
    "email": "<EMAIL>",
    "avatar": "avatar_url"
}
```

#### 4.3 修改密码
```
POST /api/profile/change-password
```
**请求参数：**
```json
{
    "old_password": "old_password",
    "new_password": "new_password",
    "confirm_password": "new_password"
}
```

#### 4.4 获取个人通知设置
```
GET /api/profile/notification-settings
```

#### 4.5 更新个人通知设置
```
PUT /api/profile/notification-settings
```
**请求参数：**
```json
{
    "email_notifications": {
        "test_complete": true,
        "test_failed": true,
        "system_maintenance": false
    },
    "in_app_notifications": {
        "test_complete": true,
        "test_failed": true,
        "project_invite": true
    }
}
```

### 5. 文件管理接口

#### 5.1 上传文件
```
POST /api/files/upload
```
**请求参数：** multipart/form-data
- `file`: 文件
- `usage_type`: 使用类型 (avatar, test_data, report, attachment)
- `project_id`: 项目ID (可选)

#### 5.2 下载文件
```
GET /api/files/{file_id}/download
```

#### 5.3 删除文件
```
DELETE /api/files/{file_id}
```

#### 5.4 获取文件信息
```
GET /api/files/{file_id}
```

### 6. 系统监控接口

#### 6.1 系统健康检查
```
GET /api/health
```
**响应数据：**
```json
{
    "code": 200,
    "data": {
        "status": "healthy",
        "database": "connected",
        "redis": "connected",
        "disk_usage": "45%",
        "memory_usage": "68%",
        "uptime": "72h 15m 30s"
    }
}
```

#### 6.2 获取系统信息
```
GET /api/system/info
```
**响应数据：**
```json
{
    "code": 200,
    "data": {
        "version": "1.0.0",
        "build_time": "2024-01-15 10:30:00",
        "environment": "production",
        "features": ["mock", "performance", "cicd", "email"]
    }
}
```

## 九、重要更新说明

### 1. 邮件通知系统
- 新增完整的邮件配置管理功能
- 支持SMTP服务器配置和连接测试
- 提供邮件模板管理和变量替换
- 支持多种通知场景（测试完成、测试失败、系统维护）
- 记录完整的邮件发送日志
- 支持邮件通知规则配置

### 2. 数据驱动测试增强
- 新增SQL、API、Faker三种数据源类型
- 支持数据源自动刷新机制
- 增加数据生成规则配置
- 提供数据预览功能

### 3. CI/CD集成功能
- 支持GitLab CI、GitHub Actions、Jenkins、Azure DevOps
- 提供Webhook接收机制
- 支持多种触发条件配置
- 记录完整的执行日志

### 4. 前端功能完善
- 所有页面都已实现完整功能
- 支持完整的响应式设计
- 提供丰富的交互体验
- 包含完整的数据模拟
- 新增邮件配置页面 (`/system/email`)
- 新增CI/CD集成页面 (`/integration/cicd`)

### 5. 系统架构优化
- 完善的权限控制机制
- 统一的错误处理和日志记录
- 模块化的功能设计
- 可扩展的插件架构

这些更新确保了前端功能的完整性，并为后端开发提供了详细的接口规范。所有新增功能都与现有架构保持一致，便于后续开发和维护。
