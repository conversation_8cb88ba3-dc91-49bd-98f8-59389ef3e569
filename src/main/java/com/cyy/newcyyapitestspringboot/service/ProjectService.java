package com.cyy.newcyyapitestspringboot.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cyy.newcyyapitestspringboot.entity.Project;

/**
 * 项目服务接口
 */
public interface ProjectService extends IService<Project> {
    
    /**
     * 分页查询项目
     */
    Page<Project> pageProjects(Integer page, Integer size, String keyword, Long ownerId);
    
    /**
     * 创建项目
     */
    Project createProject(Project project);
    
    /**
     * 更新项目
     */
    Project updateProject(Long id, Project project);
    
    /**
     * 删除项目
     */
    void deleteProject(Long id);
    
    /**
     * 获取用户的项目列表
     */
    Page<Project> getUserProjects(Long userId, Integer page, Integer size);
}
