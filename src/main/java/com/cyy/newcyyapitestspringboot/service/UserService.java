package com.cyy.newcyyapitestspringboot.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cyy.newcyyapitestspringboot.entity.User;

/**
 * 用户服务接口
 */
public interface UserService extends IService<User> {
    
    /**
     * 根据用户名查找用户
     */
    User findByUsername(String username);
    
    /**
     * 根据邮箱查找用户
     */
    User findByEmail(String email);
    
    /**
     * 用户注册
     */
    User register(String username, String email, String password, String realName);
    
    /**
     * 用户登录
     */
    User login(String username, String password);
    
    /**
     * 更新最后登录信息
     */
    void updateLastLogin(Long userId, String ip);
    
    /**
     * 分页查询用户
     */
    Page<User> pageUsers(Integer page, Integer size, String keyword, String role);
    
    /**
     * 创建用户
     */
    User createUser(User user);
    
    /**
     * 更新用户
     */
    User updateUser(Long id, User user);
    
    /**
     * 删除用户
     */
    void deleteUser(Long id);
    
    /**
     * 启用/禁用用户
     */
    void toggleUserStatus(Long id, Integer status);
    
    /**
     * 重置密码
     */
    void resetPassword(Long id, String newPassword);
}
