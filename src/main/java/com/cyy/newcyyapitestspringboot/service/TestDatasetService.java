package com.cyy.newcyyapitestspringboot.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cyy.newcyyapitestspringboot.entity.TestDataset;

import java.util.List;
import java.util.Map;

/**
 * 测试数据集服务接口
 */
public interface TestDatasetService extends IService<TestDataset> {
    
    /**
     * 分页查询测试数据集
     */
    Page<TestDataset> pageTestDatasets(Integer page, Integer size, String keyword, Long projectId, String dataType);
    
    /**
     * 创建测试数据集
     */
    TestDataset createTestDataset(TestDataset testDataset);
    
    /**
     * 更新测试数据集
     */
    TestDataset updateTestDataset(Long id, TestDataset testDataset);
    
    /**
     * 删除测试数据集
     */
    void deleteTestDataset(Long id);
    
    /**
     * 获取数据集数据
     */
    List<Map<String, Object>> getDatasetData(Long id);
    
    /**
     * 刷新数据集
     */
    void refreshDataset(Long id);
    
    /**
     * 验证数据集格式
     */
    boolean validateDatasetFormat(String dataContent, String schemaDefinition);
}
