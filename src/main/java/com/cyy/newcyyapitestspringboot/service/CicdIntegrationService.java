package com.cyy.newcyyapitestspringboot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cyy.newcyyapitestspringboot.entity.CicdIntegration;

/**
 * CI/CD集成服务接口
 */
public interface CicdIntegrationService extends IService<CicdIntegration> {
    
    /**
     * 处理Webhook事件
     */
    void handleWebhookEvent(Long integrationId, String event, Object payload);
    
    /**
     * 验证Webhook签名
     */
    boolean verifyWebhookSignature(String signature, String payload, String secret);
    
    /**
     * 触发测试计划
     */
    void triggerTestPlans(String testPlanIds, String event);
}
