package com.cyy.newcyyapitestspringboot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyy.newcyyapitestspringboot.entity.Environment;
import com.cyy.newcyyapitestspringboot.mapper.EnvironmentMapper;
import com.cyy.newcyyapitestspringboot.service.EnvironmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 环境配置服务实现类
 */
@Slf4j
@Service
public class EnvironmentServiceImpl extends ServiceImpl<EnvironmentMapper, Environment> implements EnvironmentService {
    
    @Override
    public Page<Environment> pageEnvironments(Integer page, Integer size, String keyword, Long projectId) {
        Page<Environment> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<Environment> wrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(keyword)) {
            wrapper.and(w -> w.like(Environment::getName, keyword)
                    .or().like(Environment::getBaseUrl, keyword));
        }
        
        if (projectId != null) {
            wrapper.eq(Environment::getProjectId, projectId);
        }
        
        wrapper.orderByDesc(Environment::getIsDefault)
                .orderByDesc(Environment::getCreatedAt);
        
        return page(pageParam, wrapper);
    }
    
    @Override
    public List<Environment> getProjectEnvironments(Long projectId) {
        LambdaQueryWrapper<Environment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Environment::getProjectId, projectId);
        wrapper.orderByDesc(Environment::getIsDefault)
                .orderByDesc(Environment::getCreatedAt);
        
        return list(wrapper);
    }
    
    @Override
    public Environment createEnvironment(Environment environment) {
        // 如果是第一个环境，自动设为默认
        long count = count(new LambdaQueryWrapper<Environment>()
                .eq(Environment::getProjectId, environment.getProjectId()));
        
        if (count == 0) {
            environment.setIsDefault(1);
        } else {
            environment.setIsDefault(0);
        }
        
        save(environment);
        return environment;
    }
    
    @Override
    public Environment updateEnvironment(Long id, Environment environment) {
        Environment existingEnvironment = getById(id);
        if (existingEnvironment == null) {
            throw new RuntimeException("环境配置不存在");
        }
        
        environment.setId(id);
        updateById(environment);
        return environment;
    }
    
    @Override
    public void deleteEnvironment(Long id) {
        Environment environment = getById(id);
        if (environment == null) {
            throw new RuntimeException("环境配置不存在");
        }
        
        // 如果删除的是默认环境，需要设置其他环境为默认
        if (environment.getIsDefault() == 1) {
            List<Environment> otherEnvironments = list(new LambdaQueryWrapper<Environment>()
                    .eq(Environment::getProjectId, environment.getProjectId())
                    .ne(Environment::getId, id));
            
            if (!otherEnvironments.isEmpty()) {
                Environment newDefault = otherEnvironments.get(0);
                newDefault.setIsDefault(1);
                updateById(newDefault);
            }
        }
        
        removeById(id);
    }
    
    @Override
    public void setDefaultEnvironment(Long projectId, Long environmentId) {
        // 取消当前默认环境
        update(new LambdaUpdateWrapper<Environment>()
                .eq(Environment::getProjectId, projectId)
                .set(Environment::getIsDefault, 0));
        
        // 设置新的默认环境
        update(new LambdaUpdateWrapper<Environment>()
                .eq(Environment::getId, environmentId)
                .set(Environment::getIsDefault, 1));
    }
    
    @Override
    public Environment getDefaultEnvironment(Long projectId) {
        return getOne(new LambdaQueryWrapper<Environment>()
                .eq(Environment::getProjectId, projectId)
                .eq(Environment::getIsDefault, 1));
    }
}
