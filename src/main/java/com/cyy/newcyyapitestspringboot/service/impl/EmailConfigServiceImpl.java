package com.cyy.newcyyapitestspringboot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyy.newcyyapitestspringboot.entity.EmailConfig;
import com.cyy.newcyyapitestspringboot.mapper.EmailConfigMapper;
import com.cyy.newcyyapitestspringboot.service.EmailConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Properties;

/**
 * 邮件配置服务实现类
 */
@Slf4j
@Service
public class EmailConfigServiceImpl extends ServiceImpl<EmailConfigMapper, EmailConfig> implements EmailConfigService {
    
    @Override
    public Page<EmailConfig> pageEmailConfigs(Integer page, Integer size, String keyword) {
        Page<EmailConfig> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<EmailConfig> wrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(keyword)) {
            wrapper.and(w -> w.like(EmailConfig::getName, keyword)
                    .or().like(EmailConfig::getSmtpHost, keyword)
                    .or().like(EmailConfig::getFromEmail, keyword));
        }
        
        wrapper.orderByDesc(EmailConfig::getIsDefault)
                .orderByDesc(EmailConfig::getCreatedAt);
        
        return page(pageParam, wrapper);
    }
    
    @Override
    public EmailConfig createEmailConfig(EmailConfig emailConfig) {
        // 如果是第一个配置，自动设为默认
        long count = count();
        if (count == 0) {
            emailConfig.setIsDefault(1);
        } else {
            emailConfig.setIsDefault(0);
        }
        
        emailConfig.setIsEnabled(1);
        save(emailConfig);
        return emailConfig;
    }
    
    @Override
    public EmailConfig updateEmailConfig(Long id, EmailConfig emailConfig) {
        EmailConfig existingConfig = getById(id);
        if (existingConfig == null) {
            throw new RuntimeException("邮件配置不存在");
        }
        
        emailConfig.setId(id);
        updateById(emailConfig);
        return emailConfig;
    }
    
    @Override
    public void deleteEmailConfig(Long id) {
        EmailConfig config = getById(id);
        if (config == null) {
            throw new RuntimeException("邮件配置不存在");
        }
        
        // 如果删除的是默认配置，需要设置其他配置为默认
        if (config.getIsDefault() == 1) {
            EmailConfig otherConfig = getOne(new LambdaQueryWrapper<EmailConfig>()
                    .ne(EmailConfig::getId, id)
                    .last("LIMIT 1"));
            
            if (otherConfig != null) {
                otherConfig.setIsDefault(1);
                updateById(otherConfig);
            }
        }
        
        removeById(id);
    }
    
    @Override
    public void setDefaultEmailConfig(Long id) {
        // 取消当前默认配置
        update(new LambdaUpdateWrapper<EmailConfig>()
                .set(EmailConfig::getIsDefault, 0));
        
        // 设置新的默认配置
        update(new LambdaUpdateWrapper<EmailConfig>()
                .eq(EmailConfig::getId, id)
                .set(EmailConfig::getIsDefault, 1));
    }
    
    @Override
    public EmailConfig getDefaultEmailConfig() {
        return getOne(new LambdaQueryWrapper<EmailConfig>()
                .eq(EmailConfig::getIsDefault, 1)
                .eq(EmailConfig::getIsEnabled, 1));
    }
    
    @Override
    public void testEmailConfig(Long id, String testEmail) {
        EmailConfig config = getById(id);
        if (config == null) {
            throw new RuntimeException("邮件配置不存在");
        }
        
        try {
            JavaMailSender mailSender = createMailSender(config);
            
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(config.getFromEmail());
            message.setTo(testEmail);
            message.setSubject("邮件配置测试");
            message.setText("这是一封测试邮件，用于验证邮件配置是否正确。");
            
            mailSender.send(message);
            log.info("测试邮件发送成功: {}", testEmail);
        } catch (Exception e) {
            log.error("测试邮件发送失败: {}", e.getMessage());
            throw new RuntimeException("邮件发送失败: " + e.getMessage());
        }
    }
    
    @Override
    public void toggleEmailConfig(Long id, Integer enabled) {
        EmailConfig config = new EmailConfig();
        config.setId(id);
        config.setIsEnabled(enabled);
        updateById(config);
    }
    
    /**
     * 创建邮件发送器
     */
    private JavaMailSender createMailSender(EmailConfig config) {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        mailSender.setHost(config.getSmtpHost());
        mailSender.setPort(config.getSmtpPort());
        mailSender.setUsername(config.getSmtpUsername());
        mailSender.setPassword(config.getSmtpPassword());
        
        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "true");
        
        if (config.getUseSsl() == 1) {
            props.put("mail.smtp.ssl.enable", "true");
        }
        
        if (config.getUseTls() == 1) {
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.starttls.required", "true");
        }
        
        return mailSender;
    }
}
