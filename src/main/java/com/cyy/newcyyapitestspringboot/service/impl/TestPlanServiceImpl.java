package com.cyy.newcyyapitestspringboot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyy.newcyyapitestspringboot.entity.TestPlan;
import com.cyy.newcyyapitestspringboot.mapper.TestPlanMapper;
import com.cyy.newcyyapitestspringboot.service.TestPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 测试计划服务实现类
 */
@Slf4j
@Service
public class TestPlanServiceImpl extends ServiceImpl<TestPlanMapper, TestPlan> implements TestPlanService {
    
    @Override
    public Page<TestPlan> pageTestPlans(Integer page, Integer size, String keyword, Long projectId, String scheduleType) {
        Page<TestPlan> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<TestPlan> wrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(keyword)) {
            wrapper.and(w -> w.like(TestPlan::getName, keyword)
                    .or().like(TestPlan::getDescription, keyword));
        }
        
        if (projectId != null) {
            wrapper.eq(TestPlan::getProjectId, projectId);
        }
        
        if (StringUtils.hasText(scheduleType)) {
            wrapper.eq(TestPlan::getScheduleType, scheduleType);
        }
        
        wrapper.orderByDesc(TestPlan::getCreatedAt);
        
        return page(pageParam, wrapper);
    }
    
    @Override
    public TestPlan createTestPlan(TestPlan testPlan) {
        testPlan.setStatus(1);
        save(testPlan);
        return testPlan;
    }
    
    @Override
    public TestPlan updateTestPlan(Long id, TestPlan testPlan) {
        TestPlan existingPlan = getById(id);
        if (existingPlan == null) {
            throw new RuntimeException("测试计划不存在");
        }
        
        testPlan.setId(id);
        updateById(testPlan);
        return testPlan;
    }
    
    @Override
    public void deleteTestPlan(Long id) {
        removeById(id);
    }
    
    @Override
    public String executeTestPlan(Long id) {
        TestPlan testPlan = getById(id);
        if (testPlan == null) {
            throw new RuntimeException("测试计划不存在");
        }
        
        if (testPlan.getStatus() != 1) {
            throw new RuntimeException("测试计划未启用");
        }
        
        // 生成执行ID
        String executionId = UUID.randomUUID().toString();
        
        // 更新最后执行时间
        testPlan.setLastExecutionTime(LocalDateTime.now());
        updateById(testPlan);
        
        // TODO: 实现实际的测试计划执行逻辑
        // 这里应该调用测试执行引擎，执行计划中的所有测试用例
        
        log.info("开始执行测试计划: {}, 执行ID: {}", testPlan.getName(), executionId);
        
        return executionId;
    }
    
    @Override
    public void toggleTestPlan(Long id, Integer status) {
        TestPlan plan = new TestPlan();
        plan.setId(id);
        plan.setStatus(status);
        updateById(plan);
    }
    
    @Override
    public Page<Object> getExecutionHistory(Long planId, Integer page, Integer size) {
        // TODO: 实现测试计划执行历史查询
        // 这里应该查询测试执行记录表
        Page<Object> pageParam = new Page<>(page, size);
        
        log.info("查询测试计划执行历史: planId={}", planId);
        
        return pageParam;
    }
}
