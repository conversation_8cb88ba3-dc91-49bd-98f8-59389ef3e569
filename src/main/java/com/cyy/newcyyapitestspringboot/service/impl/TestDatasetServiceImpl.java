package com.cyy.newcyyapitestspringboot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyy.newcyyapitestspringboot.entity.TestDataset;
import com.cyy.newcyyapitestspringboot.mapper.TestDatasetMapper;
import com.cyy.newcyyapitestspringboot.service.TestDatasetService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 测试数据集服务实现类
 */
@Slf4j
@Service
public class TestDatasetServiceImpl extends ServiceImpl<TestDatasetMapper, TestDataset> implements TestDatasetService {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public Page<TestDataset> pageTestDatasets(Integer page, Integer size, String keyword, Long projectId, String dataType) {
        Page<TestDataset> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<TestDataset> wrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(keyword)) {
            wrapper.and(w -> w.like(TestDataset::getName, keyword)
                    .or().like(TestDataset::getDescription, keyword));
        }
        
        if (projectId != null) {
            wrapper.eq(TestDataset::getProjectId, projectId);
        }
        
        if (StringUtils.hasText(dataType)) {
            wrapper.eq(TestDataset::getDataType, dataType);
        }
        
        wrapper.orderByDesc(TestDataset::getCreatedAt);
        
        return page(pageParam, wrapper);
    }
    
    @Override
    public TestDataset createTestDataset(TestDataset testDataset) {
        testDataset.setLastRefreshedAt(LocalDateTime.now());
        save(testDataset);
        return testDataset;
    }
    
    @Override
    public TestDataset updateTestDataset(Long id, TestDataset testDataset) {
        TestDataset existingDataset = getById(id);
        if (existingDataset == null) {
            throw new RuntimeException("测试数据集不存在");
        }
        
        testDataset.setId(id);
        updateById(testDataset);
        return testDataset;
    }
    
    @Override
    public void deleteTestDataset(Long id) {
        removeById(id);
    }
    
    @Override
    public List<Map<String, Object>> getDatasetData(Long id) {
        TestDataset dataset = getById(id);
        if (dataset == null) {
            throw new RuntimeException("测试数据集不存在");
        }
        
        try {
            String dataContent = dataset.getDataContent();
            if (!StringUtils.hasText(dataContent)) {
                return new ArrayList<>();
            }
            
            // 根据数据类型解析数据
            switch (dataset.getDataType()) {
                case "json":
                    return parseJsonData(dataContent);
                case "csv":
                    return parseCsvData(dataContent);
                case "database":
                    return queryDatabaseData(dataset.getDataSourceConfig());
                default:
                    return new ArrayList<>();
            }
        } catch (Exception e) {
            log.error("解析数据集数据失败: {}", e.getMessage());
            throw new RuntimeException("解析数据集数据失败: " + e.getMessage());
        }
    }
    
    @Override
    public void refreshDataset(Long id) {
        TestDataset dataset = getById(id);
        if (dataset == null) {
            throw new RuntimeException("测试数据集不存在");
        }
        
        // 根据数据源类型刷新数据
        if ("database".equals(dataset.getDataType())) {
            // TODO: 实现数据库数据刷新
            log.info("刷新数据库数据集: {}", dataset.getName());
        } else if ("api".equals(dataset.getDataType())) {
            // TODO: 实现API数据刷新
            log.info("刷新API数据集: {}", dataset.getName());
        }
        
        // 更新刷新时间
        dataset.setLastRefreshedAt(LocalDateTime.now());
        updateById(dataset);
    }
    
    @Override
    public boolean validateDatasetFormat(String dataContent, String schemaDefinition) {
        try {
            if (!StringUtils.hasText(dataContent)) {
                return false;
            }
            
            // 尝试解析JSON格式
            objectMapper.readValue(dataContent, new TypeReference<List<Map<String, Object>>>() {});
            
            // TODO: 根据schema定义验证数据格式
            
            return true;
        } catch (Exception e) {
            log.error("数据格式验证失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 解析JSON数据
     */
    private List<Map<String, Object>> parseJsonData(String jsonData) throws Exception {
        return objectMapper.readValue(jsonData, new TypeReference<List<Map<String, Object>>>() {});
    }
    
    /**
     * 解析CSV数据
     */
    private List<Map<String, Object>> parseCsvData(String csvData) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        String[] lines = csvData.split("\n");
        if (lines.length < 2) {
            return result;
        }
        
        // 第一行作为表头
        String[] headers = lines[0].split(",");
        
        // 解析数据行
        for (int i = 1; i < lines.length; i++) {
            String[] values = lines[i].split(",");
            Map<String, Object> row = new java.util.HashMap<>();
            
            for (int j = 0; j < Math.min(headers.length, values.length); j++) {
                row.put(headers[j].trim(), values[j].trim());
            }
            
            result.add(row);
        }
        
        return result;
    }
    
    /**
     * 查询数据库数据
     */
    private List<Map<String, Object>> queryDatabaseData(String dataSourceConfig) {
        // TODO: 实现数据库查询
        log.info("查询数据库数据: {}", dataSourceConfig);
        return new ArrayList<>();
    }
}
