package com.cyy.newcyyapitestspringboot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyy.newcyyapitestspringboot.entity.Project;
import com.cyy.newcyyapitestspringboot.entity.ProjectMember;
import com.cyy.newcyyapitestspringboot.mapper.ProjectMapper;
import com.cyy.newcyyapitestspringboot.mapper.ProjectMemberMapper;
import com.cyy.newcyyapitestspringboot.service.ProjectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 项目服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectServiceImpl extends ServiceImpl<ProjectMapper, Project> implements ProjectService {

    private final ProjectMemberMapper projectMemberMapper;
    
    @Override
    public Page<Project> pageProjects(Integer page, Integer size, String keyword, Long ownerId) {
        Page<Project> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(keyword)) {
            wrapper.and(w -> w.like(Project::getName, keyword)
                    .or().like(Project::getDescription, keyword));
        }
        
        if (ownerId != null) {
            wrapper.eq(Project::getOwnerId, ownerId);
        }
        
        wrapper.orderByDesc(Project::getCreatedAt);
        
        return page(pageParam, wrapper);
    }
    
    @Override
    public Project createProject(Project project) {
        project.setStatus(1);
        project.setApiCount(0);
        project.setCaseCount(0);
        save(project);
        return project;
    }
    
    @Override
    public Project updateProject(Long id, Project project) {
        Project existingProject = getById(id);
        if (existingProject == null) {
            throw new RuntimeException("项目不存在");
        }
        
        project.setId(id);
        updateById(project);
        return project;
    }
    
    @Override
    public void deleteProject(Long id) {
        removeById(id);
    }
    
    @Override
    public Page<Project> getUserProjects(Long userId, Integer page, Integer size) {
        Page<Project> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Project::getOwnerId, userId);
        wrapper.orderByDesc(Project::getCreatedAt);

        return page(pageParam, wrapper);
    }

    @Override
    public List<ProjectMember> getProjectMembers(Long projectId) {
        LambdaQueryWrapper<ProjectMember> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjectMember::getProjectId, projectId);
        return projectMemberMapper.selectList(wrapper);
    }

    @Override
    public void addProjectMember(Long projectId, Long userId, String role) {
        // 检查是否已经是项目成员
        LambdaQueryWrapper<ProjectMember> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjectMember::getProjectId, projectId)
               .eq(ProjectMember::getUserId, userId);

        ProjectMember existingMember = projectMemberMapper.selectOne(wrapper);
        if (existingMember != null) {
            throw new RuntimeException("用户已经是项目成员");
        }

        ProjectMember member = new ProjectMember();
        member.setProjectId(projectId);
        member.setUserId(userId);
        member.setRole(role);
        projectMemberMapper.insert(member);
    }

    @Override
    public void removeProjectMember(Long projectId, Long userId) {
        LambdaQueryWrapper<ProjectMember> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjectMember::getProjectId, projectId)
               .eq(ProjectMember::getUserId, userId);

        projectMemberMapper.delete(wrapper);
    }
}
