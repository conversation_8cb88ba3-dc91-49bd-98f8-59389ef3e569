package com.cyy.newcyyapitestspringboot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyy.newcyyapitestspringboot.entity.SystemConfig;
import com.cyy.newcyyapitestspringboot.mapper.SystemConfigMapper;
import com.cyy.newcyyapitestspringboot.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统配置服务实现类
 */
@Slf4j
@Service
public class SystemConfigServiceImpl extends ServiceImpl<SystemConfigMapper, SystemConfig> implements SystemConfigService {
    
    @Override
    public String getConfigValue(String configKey) {
        SystemConfig config = getOne(new LambdaQueryWrapper<SystemConfig>()
                .eq(SystemConfig::getConfigKey, configKey));
        return config != null ? config.getConfigValue() : null;
    }
    
    @Override
    public String getConfigValue(String configKey, String defaultValue) {
        String value = getConfigValue(configKey);
        return value != null ? value : defaultValue;
    }
    
    @Override
    public void setConfigValue(String configKey, String configValue) {
        SystemConfig config = getOne(new LambdaQueryWrapper<SystemConfig>()
                .eq(SystemConfig::getConfigKey, configKey));
        
        if (config != null) {
            config.setConfigValue(configValue);
            updateById(config);
        } else {
            config = new SystemConfig();
            config.setConfigKey(configKey);
            config.setConfigValue(configValue);
            config.setConfigType("string");
            config.setConfigCategory("basic");
            config.setIsPublic(0);
            config.setIsReadonly(0);
            config.setSortOrder(0);
            save(config);
        }
    }
    
    @Override
    public Map<String, Object> getConfigsByCategory(String category) {
        List<SystemConfig> configs = list(new LambdaQueryWrapper<SystemConfig>()
                .eq(SystemConfig::getConfigCategory, category));
        
        Map<String, Object> result = new HashMap<>();
        for (SystemConfig config : configs) {
            String key = config.getConfigKey();
            if (key.contains(".")) {
                key = key.substring(key.indexOf(".") + 1);
            }
            result.put(key, convertValue(config.getConfigValue(), config.getConfigType()));
        }
        
        return result;
    }
    
    @Override
    public Map<String, Map<String, Object>> getAllConfigs() {
        List<SystemConfig> configs = list();
        
        return configs.stream()
                .collect(Collectors.groupingBy(
                        SystemConfig::getConfigCategory,
                        Collectors.toMap(
                                config -> {
                                    String key = config.getConfigKey();
                                    return key.contains(".") ? key.substring(key.indexOf(".") + 1) : key;
                                },
                                config -> convertValue(config.getConfigValue(), config.getConfigType())
                        )
                ));
    }
    
    @Override
    public void updateConfigs(Map<String, Map<String, Object>> configs) {
        for (Map.Entry<String, Map<String, Object>> categoryEntry : configs.entrySet()) {
            String category = categoryEntry.getKey();
            Map<String, Object> categoryConfigs = categoryEntry.getValue();
            
            for (Map.Entry<String, Object> configEntry : categoryConfigs.entrySet()) {
                String key = configEntry.getKey();
                Object value = configEntry.getValue();
                String configKey = category + "." + key;
                
                setConfigValue(configKey, value != null ? value.toString() : null);
            }
        }
    }
    
    @Override
    public Map<String, Object> getPublicConfigs() {
        List<SystemConfig> configs = list(new LambdaQueryWrapper<SystemConfig>()
                .eq(SystemConfig::getIsPublic, 1));
        
        Map<String, Object> result = new HashMap<>();
        for (SystemConfig config : configs) {
            result.put(config.getConfigKey(), convertValue(config.getConfigValue(), config.getConfigType()));
        }
        
        return result;
    }
    
    @Override
    public void resetToDefault() {
        // 这里可以实现重置为默认配置的逻辑
        // 由于默认配置已经在数据库初始化时插入，这里可以重新插入或更新
        log.info("重置系统配置为默认值");
    }
    
    /**
     * 根据配置类型转换值
     */
    private Object convertValue(String value, String type) {
        if (value == null) {
            return null;
        }
        
        return switch (type) {
            case "number" -> {
                try {
                    if (value.contains(".")) {
                        yield Double.parseDouble(value);
                    } else {
                        yield Long.parseLong(value);
                    }
                } catch (NumberFormatException e) {
                    yield value;
                }
            }
            case "boolean" -> Boolean.parseBoolean(value);
            case "json" -> value; // JSON字符串保持原样，由前端解析
            default -> value;
        };
    }
}
