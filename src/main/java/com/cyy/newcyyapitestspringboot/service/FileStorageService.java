package com.cyy.newcyyapitestspringboot.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cyy.newcyyapitestspringboot.entity.FileStorage;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 文件存储服务接口
 */
public interface FileStorageService extends IService<FileStorage> {
    
    /**
     * 分页查询文件
     */
    Page<FileStorage> pageFiles(Integer page, Integer size, String keyword, Long projectId, String usageType);
    
    /**
     * 上传文件
     */
    FileStorage uploadFile(MultipartFile file, String usageType, Long projectId) throws Exception;

    /**
     * 加载文件资源
     */
    Resource loadFileAsResource(String filePath) throws Exception;

    /**
     * 删除文件
     */
    boolean deleteFile(Long fileId) throws Exception;
    
    /**
     * 获取文件URL
     */
    String getFileUrl(Long id);
    
    /**
     * 检查文件是否存在
     */
    boolean fileExists(String fileHash);
}
