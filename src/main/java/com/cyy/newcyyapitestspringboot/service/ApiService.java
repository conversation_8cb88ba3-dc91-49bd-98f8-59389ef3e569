package com.cyy.newcyyapitestspringboot.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cyy.newcyyapitestspringboot.entity.Api;

import java.util.Map;

/**
 * 接口服务接口
 */
public interface ApiService extends IService<Api> {
    
    /**
     * 分页查询接口
     */
    Page<Api> pageApis(Integer page, Integer size, String keyword, Long projectId, Long groupId);
    
    /**
     * 创建接口
     */
    Api createApi(Api api);
    
    /**
     * 更新接口
     */
    Api updateApi(Long id, Api api);
    
    /**
     * 删除接口
     */
    void deleteApi(Long id);
    
    /**
     * 获取项目的接口列表
     */
    Page<Api> getProjectApis(Long projectId, Integer page, Integer size);

    /**
     * 接口调试
     */
    Map<String, Object> debugApi(Long apiId, Map<String, Object> debugRequest);

    /**
     * 批量导入接口
     */
    Map<String, Object> importApis(Long projectId, Map<String, Object> importRequest);

    /**
     * 检查接口路径是否重复
     */
    boolean checkPathExists(Long projectId, String method, String path, Long excludeId);
}
