package com.cyy.newcyyapitestspringboot.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cyy.newcyyapitestspringboot.entity.EmailConfig;

/**
 * 邮件配置服务接口
 */
public interface EmailConfigService extends IService<EmailConfig> {
    
    /**
     * 分页查询邮件配置
     */
    Page<EmailConfig> pageEmailConfigs(Integer page, Integer size, String keyword);
    
    /**
     * 创建邮件配置
     */
    EmailConfig createEmailConfig(EmailConfig emailConfig);
    
    /**
     * 更新邮件配置
     */
    EmailConfig updateEmailConfig(Long id, EmailConfig emailConfig);
    
    /**
     * 删除邮件配置
     */
    void deleteEmailConfig(Long id);
    
    /**
     * 设置默认邮件配置
     */
    void setDefaultEmailConfig(Long id);
    
    /**
     * 获取默认邮件配置
     */
    EmailConfig getDefaultEmailConfig();
    
    /**
     * 测试邮件配置
     */
    void testEmailConfig(Long id, String testEmail);
    
    /**
     * 启用/禁用邮件配置
     */
    void toggleEmailConfig(Long id, Integer enabled);
}
