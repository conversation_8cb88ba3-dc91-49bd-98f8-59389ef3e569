package com.cyy.newcyyapitestspringboot.service;

import java.util.Map;

/**
 * 通知服务接口
 */
public interface NotificationService {
    
    /**
     * 发送邮件通知
     */
    void sendEmailNotification(String to, String subject, String content, Map<String, Object> variables);
    
    /**
     * 发送钉钉通知
     */
    void sendDingTalkNotification(String webhook, String message, Map<String, Object> variables);
    
    /**
     * 发送企业微信通知
     */
    void sendWeChatWorkNotification(String webhook, String message, Map<String, Object> variables);
    
    /**
     * 发送Slack通知
     */
    void sendSlackNotification(String webhook, String message, Map<String, Object> variables);
    
    /**
     * 发送测试执行结果通知
     */
    void sendTestExecutionNotification(String executionId, String status, Map<String, Object> result);
    
    /**
     * 发送测试计划完成通知
     */
    void sendTestPlanCompletionNotification(Long planId, String status, Map<String, Object> summary);
    
    /**
     * 发送系统告警通知
     */
    void sendSystemAlertNotification(String alertType, String message, Map<String, Object> details);
}
