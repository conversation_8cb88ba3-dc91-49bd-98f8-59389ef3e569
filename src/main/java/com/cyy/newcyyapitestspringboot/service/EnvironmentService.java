package com.cyy.newcyyapitestspringboot.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cyy.newcyyapitestspringboot.entity.Environment;

import java.util.List;

/**
 * 环境配置服务接口
 */
public interface EnvironmentService extends IService<Environment> {
    
    /**
     * 分页查询环境配置
     */
    Page<Environment> pageEnvironments(Integer page, Integer size, String keyword, Long projectId);
    
    /**
     * 获取项目的环境列表
     */
    List<Environment> getProjectEnvironments(Long projectId);
    
    /**
     * 创建环境配置
     */
    Environment createEnvironment(Environment environment);
    
    /**
     * 更新环境配置
     */
    Environment updateEnvironment(Long id, Environment environment);
    
    /**
     * 删除环境配置
     */
    void deleteEnvironment(Long id);
    
    /**
     * 设置默认环境
     */
    void setDefaultEnvironment(Long projectId, Long environmentId);
    
    /**
     * 获取默认环境
     */
    Environment getDefaultEnvironment(Long projectId);
}
