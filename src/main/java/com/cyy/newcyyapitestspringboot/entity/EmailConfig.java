package com.cyy.newcyyapitestspringboot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 邮件配置实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("email_configs")
public class EmailConfig {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("name")
    private String name;
    
    @TableField("smtp_host")
    private String smtpHost;
    
    @TableField("smtp_port")
    private Integer smtpPort;
    
    @TableField("smtp_username")
    private String smtpUsername;
    
    @TableField("smtp_password")
    private String smtpPassword;
    
    @TableField("from_email")
    private String fromEmail;
    
    @TableField("from_name")
    private String fromName;
    
    @TableField("use_ssl")
    private Integer useSsl;
    
    @TableField("use_tls")
    private Integer useTls;
    
    @TableField("is_default")
    private Integer isDefault;
    
    @TableField("is_enabled")
    private Integer isEnabled;
    
    @TableField("test_email")
    private String testEmail;
    
    @TableField("creator_id")
    private Long creatorId;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
