package com.cyy.newcyyapitestspringboot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 接口分组实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("api_groups")
public class ApiGroup {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("project_id")
    private Long projectId;
    
    @TableField("name")
    private String name;
    
    @TableField("description")
    private String description;
    
    @TableField("parent_id")
    private Long parentId;
    
    @TableField("sort_order")
    private Integer sortOrder;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
