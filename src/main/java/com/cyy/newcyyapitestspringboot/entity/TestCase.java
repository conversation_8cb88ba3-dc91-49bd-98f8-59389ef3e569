package com.cyy.newcyyapitestspringboot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 测试用例实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("test_cases")
public class TestCase {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("project_id")
    private Long projectId;
    
    @TableField("api_id")
    private Long apiId;
    
    @TableField("name")
    private String name;
    
    @TableField("description")
    private String description;
    
    @TableField("priority")
    private String priority;
    
    @TableField("status")
    private Integer status;
    
    @TableField("pre_operations")
    private String preOperations;
    
    @TableField("request_config")
    private String requestConfig;
    
    @TableField("assertions")
    private String assertions;
    
    @TableField("post_operations")
    private String postOperations;
    
    @TableField("data_driven_config")
    private String dataDrivenConfig;
    
    @TableField("creator_id")
    private Long creatorId;
    
    @TableField("last_execution_status")
    private String lastExecutionStatus;
    
    @TableField("last_execution_time")
    private LocalDateTime lastExecutionTime;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
