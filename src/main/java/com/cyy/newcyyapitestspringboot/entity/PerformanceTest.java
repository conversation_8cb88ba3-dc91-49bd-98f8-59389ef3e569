package com.cyy.newcyyapitestspringboot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 性能测试实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("performance_tests")
public class PerformanceTest {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("project_id")
    private Long projectId;
    
    @TableField("name")
    private String name;
    
    @TableField("description")
    private String description;
    
    @TableField("test_type")
    private String testType;
    
    @TableField("target_url")
    private String targetUrl;
    
    @TableField("concurrent_users")
    private Integer concurrentUsers;
    
    @TableField("duration")
    private Integer duration;
    
    @TableField("ramp_up_time")
    private Integer rampUpTime;
    
    @TableField("test_data")
    private String testData;
    
    @TableField("assertions")
    private String assertions;
    
    @TableField("environment_id")
    private Long environmentId;
    
    @TableField("creator_id")
    private Long creatorId;
    
    @TableField("status")
    private Integer status;
    
    @TableField("last_execution_time")
    private LocalDateTime lastExecutionTime;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
