package com.cyy.newcyyapitestspringboot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 测试计划实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("test_plans")
public class TestPlan {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("project_id")
    private Long projectId;
    
    @TableField("name")
    private String name;
    
    @TableField("description")
    private String description;
    
    @TableField("environment_id")
    private Long environmentId;
    
    @TableField("schedule_type")
    private String scheduleType;
    
    @TableField("schedule_config")
    private String scheduleConfig;
    
    @TableField("notification_config")
    private String notificationConfig;
    
    @TableField("retry_config")
    private String retryConfig;
    
    @TableField("creator_id")
    private Long creatorId;
    
    @TableField("status")
    private Integer status;
    
    @TableField("last_execution_time")
    private LocalDateTime lastExecutionTime;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
