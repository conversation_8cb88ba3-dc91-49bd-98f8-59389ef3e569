package com.cyy.newcyyapitestspringboot.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyy.newcyyapitestspringboot.common.PageResult;
import com.cyy.newcyyapitestspringboot.common.Result;
import com.cyy.newcyyapitestspringboot.entity.User;
import com.cyy.newcyyapitestspringboot.service.UserService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 用户管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/users")
@RequiredArgsConstructor
public class UserController {
    
    private final UserService userService;
    
    /**
     * 分页查询用户
     */
    @GetMapping
    public Result<PageResult<User>> getUsers(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String role) {
        
        Page<User> userPage = userService.pageUsers(page, size, keyword, role);
        PageResult<User> result = PageResult.of(
                userPage.getTotal(),
                (int) userPage.getCurrent(),
                (int) userPage.getSize(),
                userPage.getRecords()
        );
        
        return Result.success(result);
    }
    
    /**
     * 根据ID获取用户
     */
    @GetMapping("/{id}")
    public Result<User> getUser(@PathVariable Long id) {
        User user = userService.getById(id);
        if (user == null) {
            return Result.notFound();
        }
        return Result.success(user);
    }
    
    /**
     * 创建用户
     */
    @PostMapping
    public Result<User> createUser(@Valid @RequestBody CreateUserRequest request) {
        try {
            User user = new User();
            user.setUsername(request.getUsername());
            user.setEmail(request.getEmail());
            user.setPassword(request.getPassword());
            user.setRealName(request.getRealName());
            user.setRole(request.getRole());
            user.setAvatar(request.getAvatar());
            
            User createdUser = userService.createUser(user);
            return Result.success("用户创建成功", createdUser);
        } catch (Exception e) {
            log.error("创建用户失败: {}", e.getMessage());
            return Result.error("创建用户失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新用户
     */
    @PutMapping("/{id}")
    public Result<User> updateUser(@PathVariable Long id, @Valid @RequestBody UpdateUserRequest request) {
        try {
            User user = new User();
            user.setUsername(request.getUsername());
            user.setEmail(request.getEmail());
            user.setRealName(request.getRealName());
            user.setRole(request.getRole());
            user.setAvatar(request.getAvatar());
            
            if (request.getPassword() != null && !request.getPassword().isEmpty()) {
                user.setPassword(request.getPassword());
            }
            
            User updatedUser = userService.updateUser(id, user);
            return Result.success("用户更新成功", updatedUser);
        } catch (Exception e) {
            log.error("更新用户失败: {}", e.getMessage());
            return Result.error("更新用户失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteUser(@PathVariable Long id) {
        try {
            userService.deleteUser(id);
            return Result.success("用户删除成功");
        } catch (Exception e) {
            log.error("删除用户失败: {}", e.getMessage());
            return Result.error("删除用户失败: " + e.getMessage());
        }
    }
    
    /**
     * 启用/禁用用户
     */
    @PutMapping("/{id}/status")
    public Result<String> toggleUserStatus(@PathVariable Long id, @Valid @RequestBody ToggleStatusRequest request) {
        try {
            userService.toggleUserStatus(id, request.getStatus());
            String message = request.getStatus() == 1 ? "用户已启用" : "用户已禁用";
            return Result.success(message);
        } catch (Exception e) {
            log.error("切换用户状态失败: {}", e.getMessage());
            return Result.error("操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置用户密码
     */
    @PutMapping("/{id}/reset-password")
    public Result<String> resetPassword(@PathVariable Long id, @Valid @RequestBody ResetPasswordRequest request) {
        try {
            userService.resetPassword(id, request.getNewPassword());
            return Result.success("密码重置成功");
        } catch (Exception e) {
            log.error("重置密码失败: {}", e.getMessage());
            return Result.error("重置密码失败: " + e.getMessage());
        }
    }
    
    @Data
    public static class CreateUserRequest {
        @NotBlank(message = "用户名不能为空")
        private String username;
        
        @NotBlank(message = "邮箱不能为空")
        @Email(message = "邮箱格式不正确")
        private String email;
        
        @NotBlank(message = "密码不能为空")
        private String password;
        
        @NotBlank(message = "真实姓名不能为空")
        private String realName;
        
        @NotBlank(message = "角色不能为空")
        private String role;
        
        private String avatar;
    }
    
    @Data
    public static class UpdateUserRequest {
        @NotBlank(message = "用户名不能为空")
        private String username;
        
        @NotBlank(message = "邮箱不能为空")
        @Email(message = "邮箱格式不正确")
        private String email;
        
        private String password;
        
        @NotBlank(message = "真实姓名不能为空")
        private String realName;
        
        @NotBlank(message = "角色不能为空")
        private String role;
        
        private String avatar;
    }
    
    @Data
    public static class ToggleStatusRequest {
        @NotNull(message = "状态不能为空")
        private Integer status;
    }
    
    @Data
    public static class ResetPasswordRequest {
        @NotBlank(message = "新密码不能为空")
        private String newPassword;
    }
}
