package com.cyy.newcyyapitestspringboot.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyy.newcyyapitestspringboot.common.PageResult;
import com.cyy.newcyyapitestspringboot.common.Result;
import com.cyy.newcyyapitestspringboot.entity.FileStorage;
import com.cyy.newcyyapitestspringboot.service.FileStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件管理控制器
 */
@Slf4j
@RestController
@RequestMapping("${api.prefix}/files")
@RequiredArgsConstructor
public class FileController {

    private final FileStorageService fileStorageService;

    /**
     * 分页查询文件
     */
    @GetMapping
    public Result<PageResult<FileStorage>> getFiles(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long projectId,
            @RequestParam(required = false) String usageType) {

        Page<FileStorage> filePage = fileStorageService.pageFiles(page, size, keyword, projectId, usageType);
        PageResult<FileStorage> result = PageResult.of(
                filePage.getTotal(),
                (int) filePage.getCurrent(),
                (int) filePage.getSize(),
                filePage.getRecords()
        );

        return Result.success(result);
    }

    /**
     * 根据ID获取文件信息
     */
    @GetMapping("/{id}")
    public Result<FileStorage> getFile(@PathVariable Long id) {
        FileStorage file = fileStorageService.getById(id);
        if (file == null) {
            return Result.notFound();
        }
        return Result.success(file);
    }

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    public Result<Map<String, Object>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(required = false) Long projectId,
            @RequestParam(defaultValue = "attachment") String usageType,
            @RequestParam Long uploaderId) {

        try {
            FileStorage fileStorage = fileStorageService.uploadFile(file, usageType, projectId);

            Map<String, Object> result = new HashMap<>();
            result.put("id", fileStorage.getId());
            result.put("original_name", fileStorage.getOriginalName());
            result.put("file_size", fileStorage.getFileSize());
            result.put("mime_type", fileStorage.getMimeType());
            result.put("url", fileStorageService.getFileUrl(fileStorage.getId()));

            return Result.success("文件上传成功", result);
        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage());
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件
     */
    @GetMapping("/download/{id}")
    public ResponseEntity<Resource> downloadFile(@PathVariable Long id) {
        try {
            FileStorage fileStorage = fileStorageService.getById(id);
            if (fileStorage == null) {
                return ResponseEntity.notFound().build();
            }

            Resource resource = fileStorageService.loadFileAsResource(fileStorage.getFilePath());

            String encodedFileName = URLEncoder.encode(fileStorage.getOriginalName(), StandardCharsets.UTF_8);

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(fileStorage.getMimeType()))
                    .header(HttpHeaders.CONTENT_DISPOSITION,
                            "attachment; filename=\"" + encodedFileName + "\"")
                    .body(resource);
        } catch (Exception e) {
            log.error("文件下载失败: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 预览文件（用于图片等）
     */
    @GetMapping("/preview/{id}")
    public ResponseEntity<Resource> previewFile(@PathVariable Long id) {
        try {
            FileStorage fileStorage = fileStorageService.getById(id);
            if (fileStorage == null) {
                return ResponseEntity.notFound().build();
            }

            Resource resource = fileStorageService.loadFileAsResource(fileStorage.getFilePath());

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(fileStorage.getMimeType()))
                    .body(resource);
        } catch (Exception e) {
            log.error("文件预览失败: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteFile(@PathVariable Long id) {
        try {
            fileStorageService.deleteFile(id);
            return Result.success("文件删除成功");
        } catch (Exception e) {
            log.error("文件删除失败: {}", e.getMessage());
            return Result.error("文件删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量上传文件
     */
    @PostMapping("/batch-upload")
    public Result<Map<String, Object>> batchUploadFiles(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(required = false) Long projectId,
            @RequestParam(defaultValue = "attachment") String usageType,
            @RequestParam Long uploaderId) {

        try {
            Map<String, Object> result = new HashMap<>();
            result.put("total", files.length);
            result.put("success", 0);
            result.put("failed", 0);
            result.put("files", new HashMap<String, Object>());

            for (MultipartFile file : files) {
                try {
                    FileStorage fileStorage = fileStorageService.uploadFile(file, usageType, projectId);

                    Map<String, Object> fileInfo = new HashMap<>();
                    fileInfo.put("id", fileStorage.getId());
                    fileInfo.put("url", fileStorageService.getFileUrl(fileStorage.getId()));

                    ((Map<String, Object>) result.get("files")).put(file.getOriginalFilename(), fileInfo);
                    result.put("success", (Integer) result.get("success") + 1);
                } catch (Exception e) {
                    result.put("failed", (Integer) result.get("failed") + 1);
                    log.error("文件上传失败: {}, 错误: {}", file.getOriginalFilename(), e.getMessage());
                }
            }

            return Result.success("批量上传完成", result);
        } catch (Exception e) {
            log.error("批量上传失败: {}", e.getMessage());
            return Result.error("批量上传失败: " + e.getMessage());
        }
    }
}
