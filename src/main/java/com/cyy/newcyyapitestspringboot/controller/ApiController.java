package com.cyy.newcyyapitestspringboot.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyy.newcyyapitestspringboot.common.PageResult;
import com.cyy.newcyyapitestspringboot.common.Result;
import com.cyy.newcyyapitestspringboot.entity.Api;
import com.cyy.newcyyapitestspringboot.service.ApiService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 接口管理控制器
 */
@Slf4j
@RestController
@RequestMapping("${api.prefix}/projects/{projectId}/apis")
@RequiredArgsConstructor
public class ApiController {
    
    private final ApiService apiService;
    
    /**
     * 分页查询接口
     */
    @GetMapping
    public Result<PageResult<Api>> getApis(
            @PathVariable Long projectId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long groupId) {
        
        Page<Api> apiPage = apiService.pageApis(page, size, keyword, projectId, groupId);
        PageResult<Api> result = PageResult.of(
                apiPage.getTotal(),
                (int) apiPage.getCurrent(),
                (int) apiPage.getSize(),
                apiPage.getRecords()
        );
        
        return Result.success(result);
    }
    
    /**
     * 根据ID获取接口
     */
    @GetMapping("/{id}")
    public Result<Api> getApi(@PathVariable Long projectId, @PathVariable Long id) {
        Api api = apiService.getById(id);
        if (api == null || !api.getProjectId().equals(projectId)) {
            return Result.notFound();
        }
        return Result.success(api);
    }
    
    /**
     * 创建接口
     */
    @PostMapping
    public Result<Api> createApi(@PathVariable Long projectId, @Valid @RequestBody CreateApiRequest request) {
        try {
            Api api = new Api();
            api.setProjectId(projectId);
            api.setGroupId(request.getGroupId());
            api.setName(request.getName());
            api.setMethod(request.getMethod());
            api.setPath(request.getPath());
            api.setDescription(request.getDescription());
            api.setTags(request.getTags());
            api.setRequestHeaders(request.getRequestHeaders());
            api.setQueryParams(request.getQueryParams());
            api.setPathParams(request.getPathParams());
            api.setRequestBody(request.getRequestBody());
            api.setResponseDefinition(request.getResponseDefinition());
            api.setCreatorId(request.getCreatorId());
            
            Api createdApi = apiService.createApi(api);
            return Result.success("接口创建成功", createdApi);
        } catch (Exception e) {
            log.error("创建接口失败: {}", e.getMessage());
            return Result.error("创建接口失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新接口
     */
    @PutMapping("/{id}")
    public Result<Api> updateApi(@PathVariable Long projectId, @PathVariable Long id, 
                                @Valid @RequestBody UpdateApiRequest request) {
        try {
            Api api = new Api();
            api.setGroupId(request.getGroupId());
            api.setName(request.getName());
            api.setMethod(request.getMethod());
            api.setPath(request.getPath());
            api.setDescription(request.getDescription());
            api.setTags(request.getTags());
            api.setRequestHeaders(request.getRequestHeaders());
            api.setQueryParams(request.getQueryParams());
            api.setPathParams(request.getPathParams());
            api.setRequestBody(request.getRequestBody());
            api.setResponseDefinition(request.getResponseDefinition());
            
            Api updatedApi = apiService.updateApi(id, api);
            return Result.success("接口更新成功", updatedApi);
        } catch (Exception e) {
            log.error("更新接口失败: {}", e.getMessage());
            return Result.error("更新接口失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除接口
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteApi(@PathVariable Long projectId, @PathVariable Long id) {
        try {
            apiService.deleteApi(id);
            return Result.success("接口删除成功");
        } catch (Exception e) {
            log.error("删除接口失败: {}", e.getMessage());
            return Result.error("删除接口失败: " + e.getMessage());
        }
    }
    
    @Data
    public static class CreateApiRequest {
        private Long groupId;
        
        @NotBlank(message = "接口名称不能为空")
        private String name;
        
        @NotBlank(message = "请求方法不能为空")
        private String method;
        
        @NotBlank(message = "接口路径不能为空")
        private String path;
        
        private String description;
        private String tags;
        private String requestHeaders;
        private String queryParams;
        private String pathParams;
        private String requestBody;
        private String responseDefinition;
        
        @NotNull(message = "创建人ID不能为空")
        private Long creatorId;
    }
    
    @Data
    public static class UpdateApiRequest {
        private Long groupId;
        
        @NotBlank(message = "接口名称不能为空")
        private String name;
        
        @NotBlank(message = "请求方法不能为空")
        private String method;
        
        @NotBlank(message = "接口路径不能为空")
        private String path;
        
        private String description;
        private String tags;
        private String requestHeaders;
        private String queryParams;
        private String pathParams;
        private String requestBody;
        private String responseDefinition;
    }
}
