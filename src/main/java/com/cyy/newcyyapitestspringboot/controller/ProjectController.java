package com.cyy.newcyyapitestspringboot.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyy.newcyyapitestspringboot.common.PageResult;
import com.cyy.newcyyapitestspringboot.common.Result;
import com.cyy.newcyyapitestspringboot.entity.Project;
import com.cyy.newcyyapitestspringboot.service.ProjectService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 项目管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/projects")
@RequiredArgsConstructor
public class ProjectController {
    
    private final ProjectService projectService;
    
    /**
     * 分页查询项目
     */
    @GetMapping
    public Result<PageResult<Project>> getProjects(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long ownerId) {
        
        Page<Project> projectPage = projectService.pageProjects(page, size, keyword, ownerId);
        PageResult<Project> result = PageResult.of(
                projectPage.getTotal(),
                (int) projectPage.getCurrent(),
                (int) projectPage.getSize(),
                projectPage.getRecords()
        );
        
        return Result.success(result);
    }
    
    /**
     * 根据ID获取项目
     */
    @GetMapping("/{id}")
    public Result<Project> getProject(@PathVariable Long id) {
        Project project = projectService.getById(id);
        if (project == null) {
            return Result.notFound();
        }
        return Result.success(project);
    }
    
    /**
     * 创建项目
     */
    @PostMapping
    public Result<Project> createProject(@Valid @RequestBody CreateProjectRequest request) {
        try {
            Project project = new Project();
            project.setName(request.getName());
            project.setDescription(request.getDescription());
            project.setOwnerId(request.getOwnerId());
            
            Project createdProject = projectService.createProject(project);
            return Result.success("项目创建成功", createdProject);
        } catch (Exception e) {
            log.error("创建项目失败: {}", e.getMessage());
            return Result.error("创建项目失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新项目
     */
    @PutMapping("/{id}")
    public Result<Project> updateProject(@PathVariable Long id, @Valid @RequestBody UpdateProjectRequest request) {
        try {
            Project project = new Project();
            project.setName(request.getName());
            project.setDescription(request.getDescription());
            
            Project updatedProject = projectService.updateProject(id, project);
            return Result.success("项目更新成功", updatedProject);
        } catch (Exception e) {
            log.error("更新项目失败: {}", e.getMessage());
            return Result.error("更新项目失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除项目
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteProject(@PathVariable Long id) {
        try {
            projectService.deleteProject(id);
            return Result.success("项目删除成功");
        } catch (Exception e) {
            log.error("删除项目失败: {}", e.getMessage());
            return Result.error("删除项目失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户的项目列表
     */
    @GetMapping("/my")
    public Result<PageResult<Project>> getUserProjects(
            @RequestParam Long userId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        
        Page<Project> projectPage = projectService.getUserProjects(userId, page, size);
        PageResult<Project> result = PageResult.of(
                projectPage.getTotal(),
                (int) projectPage.getCurrent(),
                (int) projectPage.getSize(),
                projectPage.getRecords()
        );
        
        return Result.success(result);
    }
    
    @Data
    public static class CreateProjectRequest {
        @NotBlank(message = "项目名称不能为空")
        private String name;
        
        private String description;
        
        @NotNull(message = "负责人ID不能为空")
        private Long ownerId;
    }
    
    @Data
    public static class UpdateProjectRequest {
        @NotBlank(message = "项目名称不能为空")
        private String name;
        
        private String description;
    }
}
