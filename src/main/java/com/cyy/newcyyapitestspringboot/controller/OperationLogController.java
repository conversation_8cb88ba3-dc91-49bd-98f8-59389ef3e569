package com.cyy.newcyyapitestspringboot.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyy.newcyyapitestspringboot.common.PageResult;
import com.cyy.newcyyapitestspringboot.common.Result;
import com.cyy.newcyyapitestspringboot.entity.OperationLog;
import com.cyy.newcyyapitestspringboot.service.OperationLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 操作日志控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/operation-logs")
@RequiredArgsConstructor
public class OperationLogController {
    
    private final OperationLogService operationLogService;
    
    /**
     * 分页查询操作日志
     */
    @GetMapping
    public Result<PageResult<OperationLog>> getOperationLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String operationType,
            @RequestParam(required = false) String resourceType,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        
        Page<OperationLog> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<OperationLog> wrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(keyword)) {
            wrapper.and(w -> w.like(OperationLog::getAction, keyword)
                    .or().like(OperationLog::getResourceName, keyword));
        }

        if (StringUtils.hasText(operationType)) {
            wrapper.eq(OperationLog::getAction, operationType);
        }
        
        if (StringUtils.hasText(resourceType)) {
            wrapper.eq(OperationLog::getResourceType, resourceType);
        }
        
        if (userId != null) {
            wrapper.eq(OperationLog::getUserId, userId);
        }
        
        if (StringUtils.hasText(startTime)) {
            LocalDateTime start = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            wrapper.ge(OperationLog::getCreatedAt, start);
        }
        
        if (StringUtils.hasText(endTime)) {
            LocalDateTime end = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            wrapper.le(OperationLog::getCreatedAt, end);
        }
        
        wrapper.orderByDesc(OperationLog::getCreatedAt);
        
        Page<OperationLog> logPage = operationLogService.page(pageParam, wrapper);
        PageResult<OperationLog> result = PageResult.of(
                logPage.getTotal(),
                (int) logPage.getCurrent(),
                (int) logPage.getSize(),
                logPage.getRecords()
        );
        
        return Result.success(result);
    }
    
    /**
     * 根据ID获取操作日志
     */
    @GetMapping("/{id}")
    public Result<OperationLog> getOperationLog(@PathVariable Long id) {
        OperationLog log = operationLogService.getById(id);
        if (log == null) {
            return Result.notFound();
        }
        return Result.success(log);
    }
    
    /**
     * 清理操作日志
     */
    @DeleteMapping("/cleanup")
    public Result<String> cleanupLogs(@RequestParam Integer days) {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(days);
            
            LambdaQueryWrapper<OperationLog> wrapper = new LambdaQueryWrapper<>();
            wrapper.lt(OperationLog::getCreatedAt, cutoffTime);
            
            operationLogService.remove(wrapper);
            
            return Result.success("操作日志清理成功");
        } catch (Exception e) {
            log.error("清理操作日志失败: {}", e.getMessage());
            return Result.error("清理操作日志失败: " + e.getMessage());
        }
    }
}
