package com.cyy.newcyyapitestspringboot.controller;

import com.cyy.newcyyapitestspringboot.common.Result;
import com.cyy.newcyyapitestspringboot.entity.User;
import com.cyy.newcyyapitestspringboot.service.UserService;
import com.cyy.newcyyapitestspringboot.util.JwtUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 */
@Slf4j
@RestController
@RequestMapping("${api.prefix}/auth")
@RequiredArgsConstructor
public class AuthController {
    
    private final UserService userService;
    private final JwtUtil jwtUtil;
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@Valid @RequestBody LoginRequest request, HttpServletRequest httpRequest) {
        try {
            User user = userService.login(request.getUsername(), request.getPassword());
            
            // 更新最后登录信息
            String clientIp = getClientIp(httpRequest);
            userService.updateLastLogin(user.getId(), clientIp);
            
            // 生成JWT令牌
            String accessToken = jwtUtil.generateAccessToken(user.getUsername(), user.getId(), user.getRole());
            String refreshToken = jwtUtil.generateRefreshToken(user.getUsername());
            
            Map<String, Object> result = new HashMap<>();
            result.put("token", accessToken);
            result.put("refresh_token", refreshToken);
            result.put("expires_in", 7200); // 2小时
            result.put("user", getUserInfo(user));
            
            return Result.success("登录成功", result);
        } catch (Exception e) {
            log.error("登录失败: {}", e.getMessage());
            return Result.error("登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result<Map<String, Object>> register(@Valid @RequestBody RegisterRequest request) {
        try {
            User user = userService.register(request.getUsername(), request.getEmail(), 
                    request.getPassword(), request.getRealName());
            
            Map<String, Object> result = new HashMap<>();
            result.put("user", getUserInfo(user));
            
            return Result.success("注册成功", result);
        } catch (Exception e) {
            log.error("注册失败: {}", e.getMessage());
            return Result.error("注册失败: " + e.getMessage());
        }
    }
    
    /**
     * 刷新令牌
     */
    @PostMapping("/refresh")
    public Result<Map<String, Object>> refresh(@Valid @RequestBody RefreshTokenRequest request) {
        try {
            log.info("收到刷新令牌请求: {}", request.getRefreshToken() != null ? "有令牌" : "无令牌");

            String refreshToken = request.getRefreshToken();
            if (refreshToken == null || refreshToken.trim().isEmpty()) {
                log.warn("刷新令牌为空");
                return Result.error("刷新令牌不能为空");
            }

            if (!jwtUtil.isValidToken(refreshToken)) {
                log.warn("刷新令牌无效");
                return Result.unauthorized();
            }

            String username = jwtUtil.getUsernameFromToken(refreshToken);
            User user = userService.findByUsername(username);

            if (user == null) {
                log.warn("用户不存在: {}", username);
                return Result.unauthorized();
            }

            // 生成新的访问令牌
            String newAccessToken = jwtUtil.generateAccessToken(user.getUsername(), user.getId(), user.getRole());

            Map<String, Object> result = new HashMap<>();
            result.put("token", newAccessToken);
            result.put("expires_in", 7200);

            log.info("令牌刷新成功，用户: {}", username);
            return Result.success("令牌刷新成功", result);
        } catch (Exception e) {
            log.error("令牌刷新失败: {}", e.getMessage(), e);
            return Result.error("令牌刷新失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    public Result<Map<String, Object>> getCurrentUser(HttpServletRequest request) {
        try {
            String token = extractToken(request);
            if (token == null) {
                return Result.unauthorized();
            }
            
            String username = jwtUtil.getUsernameFromToken(token);
            User user = userService.findByUsername(username);
            
            if (user == null) {
                return Result.unauthorized();
            }
            
            return Result.success(getUserInfo(user));
        } catch (Exception e) {
            log.error("获取用户信息失败: {}", e.getMessage());
            return Result.unauthorized();
        }
    }
    
    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public Result<String> logout() {
        // 这里可以实现令牌黑名单逻辑
        return Result.success("登出成功");
    }
    
    private String extractToken(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
    
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
    
    private Map<String, Object> getUserInfo(User user) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getId());
        userInfo.put("username", user.getUsername());
        userInfo.put("email", user.getEmail());
        userInfo.put("real_name", user.getRealName());
        userInfo.put("avatar", user.getAvatar());
        userInfo.put("role", user.getRole());
        userInfo.put("status", user.getStatus());
        userInfo.put("last_login_time", user.getLastLoginTime());
        return userInfo;
    }
    
    @Data
    public static class LoginRequest {
        @NotBlank(message = "用户名不能为空")
        private String username;

        @NotBlank(message = "密码不能为空")
        private String password;

        private Boolean remember = false; // 是否记住登录状态
    }
    
    @Data
    public static class RegisterRequest {
        @NotBlank(message = "用户名不能为空")
        @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
        private String username;
        
        @NotBlank(message = "邮箱不能为空")
        @Email(message = "邮箱格式不正确")
        private String email;
        
        @NotBlank(message = "密码不能为空")
        @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
        private String password;
        
        @NotBlank(message = "真实姓名不能为空")
        private String realName;
    }
    
    @Data
    public static class RefreshTokenRequest {
        @NotBlank(message = "刷新令牌不能为空")
        @JsonProperty("refresh_token")
        private String refreshToken;
    }
}
