package com.cyy.newcyyapitestspringboot.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyy.newcyyapitestspringboot.common.PageResult;
import com.cyy.newcyyapitestspringboot.common.Result;
import com.cyy.newcyyapitestspringboot.entity.ApiGroup;
import com.cyy.newcyyapitestspringboot.service.ApiGroupService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.List;

/**
 * 接口分组控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/projects/{projectId}/api-groups")
@RequiredArgsConstructor
public class ApiGroupController {

    private final ApiGroupService apiGroupService;

    /**
     * 分页查询接口分组
     */
    @GetMapping
    public Result<PageResult<ApiGroup>> getApiGroups(
            @PathVariable Long projectId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword) {

        Page<ApiGroup> groupPage = apiGroupService.pageApiGroups(page, size, keyword, projectId);
        PageResult<ApiGroup> result = PageResult.of(
                groupPage.getTotal(),
                (int) groupPage.getCurrent(),
                (int) groupPage.getSize(),
                groupPage.getRecords()
        );

        return Result.success(result);
    }

    /**
     * 获取项目的分组树
     */
    @GetMapping("/tree")
    public Result<List<ApiGroup>> getProjectGroupTree(@PathVariable Long projectId) {
        List<ApiGroup> groups = apiGroupService.getProjectGroupTree(projectId);
        return Result.success(groups);
    }

    /**
     * 根据ID获取接口分组
     */
    @GetMapping("/{id}")
    public Result<ApiGroup> getApiGroup(@PathVariable Long projectId, @PathVariable Long id) {
        ApiGroup group = apiGroupService.getById(id);
        if (group == null || !group.getProjectId().equals(projectId)) {
            return Result.notFound();
        }
        return Result.success(group);
    }

    /**
     * 创建接口分组
     */
    @PostMapping
    public Result<ApiGroup> createApiGroup(@PathVariable Long projectId,
                                          @Valid @RequestBody CreateApiGroupRequest request) {
        try {
            ApiGroup group = new ApiGroup();
            group.setProjectId(projectId);
            group.setName(request.getName());
            group.setDescription(request.getDescription());
            group.setParentId(request.getParentId());
            group.setSortOrder(request.getSortOrder());

            ApiGroup createdGroup = apiGroupService.createApiGroup(group);
            return Result.success("接口分组创建成功", createdGroup);
        } catch (Exception e) {
            log.error("创建接口分组失败: {}", e.getMessage());
            return Result.error("创建接口分组失败: " + e.getMessage());
        }
    }

    /**
     * 更新接口分组
     */
    @PutMapping("/{id}")
    public Result<ApiGroup> updateApiGroup(@PathVariable Long projectId, @PathVariable Long id,
                                          @Valid @RequestBody UpdateApiGroupRequest request) {
        try {
            ApiGroup group = new ApiGroup();
            group.setName(request.getName());
            group.setDescription(request.getDescription());
            group.setSortOrder(request.getSortOrder());

            ApiGroup updatedGroup = apiGroupService.updateApiGroup(id, group);
            return Result.success("接口分组更新成功", updatedGroup);
        } catch (Exception e) {
            log.error("更新接口分组失败: {}", e.getMessage());
            return Result.error("更新接口分组失败: " + e.getMessage());
        }
    }

    /**
     * 删除接口分组
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteApiGroup(@PathVariable Long projectId, @PathVariable Long id) {
        try {
            apiGroupService.deleteApiGroup(id);
            return Result.success("接口分组删除成功");
        } catch (Exception e) {
            log.error("删除接口分组失败: {}", e.getMessage());
            return Result.error("删除接口分组失败: " + e.getMessage());
        }
    }

    /**
     * 移动分组
     */
    @PutMapping("/{id}/move")
    public Result<String> moveGroup(@PathVariable Long projectId, @PathVariable Long id,
                                 @Valid @RequestBody MoveGroupRequest request) {
        try {
            apiGroupService.moveGroup(id, request.getTargetParentId(), request.getSortOrder());
            return Result.success("分组移动成功");
        } catch (Exception e) {
            log.error("移动分组失败: {}", e.getMessage());
            return Result.error("移动分组失败: " + e.getMessage());
        }
    }

    @Data
    public static class CreateApiGroupRequest {
        @NotBlank(message = "分组名称不能为空")
        private String name;

        private String description;
        private Long parentId;
        private Integer sortOrder;
    }

    @Data
    public static class UpdateApiGroupRequest {
        @NotBlank(message = "分组名称不能为空")
        private String name;

        private String description;
        private Integer sortOrder;
    }

    @Data
    public static class MoveGroupRequest {
        private Long targetParentId;
        private Integer sortOrder;
    }
}
