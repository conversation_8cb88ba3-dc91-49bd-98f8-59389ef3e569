package com.cyy.newcyyapitestspringboot.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyy.newcyyapitestspringboot.common.PageResult;
import com.cyy.newcyyapitestspringboot.common.Result;
import com.cyy.newcyyapitestspringboot.entity.TestCase;
import com.cyy.newcyyapitestspringboot.service.TestCaseService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试用例控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/projects/{projectId}/test-cases")
@RequiredArgsConstructor
public class TestCaseController {

    private final TestCaseService testCaseService;

    /**
     * 分页查询测试用例
     */
    @GetMapping
    public Result<PageResult<TestCase>> getTestCases(
            @PathVariable Long projectId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long apiId,
            @RequestParam(required = false) String priority) {

        Page<TestCase> casePage = testCaseService.pageTestCases(page, size, keyword, projectId, apiId, priority);
        PageResult<TestCase> result = PageResult.of(
                casePage.getTotal(),
                (int) casePage.getCurrent(),
                (int) casePage.getSize(),
                casePage.getRecords()
        );

        return Result.success(result);
    }

    /**
     * 根据ID获取测试用例
     */
    @GetMapping("/{id}")
    public Result<TestCase> getTestCase(@PathVariable Long projectId, @PathVariable Long id) {
        TestCase testCase = testCaseService.getById(id);
        if (testCase == null || !testCase.getProjectId().equals(projectId)) {
            return Result.notFound();
        }
        return Result.success(testCase);
    }

    /**
     * 创建测试用例
     */
    @PostMapping
    public Result<TestCase> createTestCase(@PathVariable Long projectId,
                                          @Valid @RequestBody CreateTestCaseRequest request) {
        try {
            TestCase testCase = new TestCase();
            testCase.setProjectId(projectId);
            testCase.setApiId(request.getApiId());
            testCase.setName(request.getName());
            testCase.setDescription(request.getDescription());
            testCase.setPriority(request.getPriority());
            testCase.setPreOperations(request.getPreOperations());
            testCase.setRequestConfig(request.getRequestConfig());
            testCase.setAssertions(request.getAssertions());
            testCase.setPostOperations(request.getPostOperations());
            testCase.setDataDrivenConfig(request.getDataDrivenConfig());
            testCase.setCreatorId(request.getCreatorId());

            TestCase createdCase = testCaseService.createTestCase(testCase);
            return Result.success("测试用例创建成功", createdCase);
        } catch (Exception e) {
            log.error("创建测试用例失败: {}", e.getMessage());
            return Result.error("创建测试用例失败: " + e.getMessage());
        }
    }

    /**
     * 更新测试用例
     */
    @PutMapping("/{id}")
    public Result<TestCase> updateTestCase(@PathVariable Long projectId, @PathVariable Long id,
                                          @Valid @RequestBody UpdateTestCaseRequest request) {
        try {
            TestCase testCase = new TestCase();
            testCase.setApiId(request.getApiId());
            testCase.setName(request.getName());
            testCase.setDescription(request.getDescription());
            testCase.setPriority(request.getPriority());
            testCase.setPreOperations(request.getPreOperations());
            testCase.setRequestConfig(request.getRequestConfig());
            testCase.setAssertions(request.getAssertions());
            testCase.setPostOperations(request.getPostOperations());
            testCase.setDataDrivenConfig(request.getDataDrivenConfig());

            TestCase updatedCase = testCaseService.updateTestCase(id, testCase);
            return Result.success("测试用例更新成功", updatedCase);
        } catch (Exception e) {
            log.error("更新测试用例失败: {}", e.getMessage());
            return Result.error("更新测试用例失败: " + e.getMessage());
        }
    }

    /**
     * 删除测试用例
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteTestCase(@PathVariable Long projectId, @PathVariable Long id) {
        try {
            testCaseService.deleteTestCase(id);
            return Result.success("测试用例删除成功");
        } catch (Exception e) {
            log.error("删除测试用例失败: {}", e.getMessage());
            return Result.error("删除测试用例失败: " + e.getMessage());
        }
    }

    /**
     * 执行测试用例
     */
    @PostMapping("/{id}/execute")
    public Result<Map<String, Object>> executeTestCase(@PathVariable Long projectId, @PathVariable Long id,
                                                       @Valid @RequestBody ExecuteTestCaseRequest request) {
        try {
            String executionId = testCaseService.executeTestCase(id, request.getEnvironmentId());

            Map<String, Object> result = new HashMap<>();
            result.put("execution_id", executionId);
            result.put("status", "running");

            return Result.success("测试用例执行已启动", result);
        } catch (Exception e) {
            log.error("执行测试用例失败: {}", e.getMessage());
            return Result.error("执行测试用例失败: " + e.getMessage());
        }
    }

    /**
     * 批量执行测试用例
     */
    @PostMapping("/batch-execute")
    public Result<Map<String, Object>> batchExecuteTestCases(@PathVariable Long projectId,
                                                             @Valid @RequestBody BatchExecuteRequest request) {
        try {
            String executionId = testCaseService.batchExecuteTestCases(request.getCaseIds(), request.getEnvironmentId());

            Map<String, Object> result = new HashMap<>();
            result.put("execution_id", executionId);
            result.put("status", "running");
            result.put("total_cases", request.getCaseIds().length);

            return Result.success("批量执行已启动", result);
        } catch (Exception e) {
            log.error("批量执行测试用例失败: {}", e.getMessage());
            return Result.error("批量执行测试用例失败: " + e.getMessage());
        }
    }

    /**
     * 复制测试用例
     */
    @PostMapping("/{id}/copy")
    public Result<TestCase> copyTestCase(@PathVariable Long projectId, @PathVariable Long id,
                                        @Valid @RequestBody CopyTestCaseRequest request) {
        try {
            TestCase copiedCase = testCaseService.copyTestCase(id, request.getNewName());
            return Result.success("测试用例复制成功", copiedCase);
        } catch (Exception e) {
            log.error("复制测试用例失败: {}", e.getMessage());
            return Result.error("复制测试用例失败: " + e.getMessage());
        }
    }

    @Data
    public static class CreateTestCaseRequest {
        private Long apiId;

        @NotBlank(message = "用例名称不能为空")
        private String name;

        private String description;
        private String priority = "medium";
        private String preOperations;
        private String requestConfig;
        private String assertions;
        private String postOperations;
        private String dataDrivenConfig;

        @NotNull(message = "创建人ID不能为空")
        private Long creatorId;
    }

    @Data
    public static class UpdateTestCaseRequest {
        private Long apiId;

        @NotBlank(message = "用例名称不能为空")
        private String name;

        private String description;
        private String priority;
        private String preOperations;
        private String requestConfig;
        private String assertions;
        private String postOperations;
        private String dataDrivenConfig;
    }

    @Data
    public static class ExecuteTestCaseRequest {
        @NotNull(message = "环境ID不能为空")
        private Long environmentId;
    }

    @Data
    public static class BatchExecuteRequest {
        @NotNull(message = "用例ID列表不能为空")
        private Long[] caseIds;

        @NotNull(message = "环境ID不能为空")
        private Long environmentId;
    }

    @Data
    public static class CopyTestCaseRequest {
        @NotBlank(message = "新用例名称不能为空")
        private String newName;
    }
}
