package com.cyy.newcyyapitestspringboot.controller;

import com.cyy.newcyyapitestspringboot.common.Result;
import com.cyy.newcyyapitestspringboot.service.NotificationService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.Map;

/**
 * 通知配置控制器
 */
@Slf4j
@RestController
@RequestMapping("${api.prefix}/admin/notifications")
@RequiredArgsConstructor
public class NotificationController {

    private final NotificationService notificationService;

    /**
     * 获取通知配置
     */
    @GetMapping("/config")
    public Result<Map<String, Object>> getNotificationConfig() {
        try {
            Map<String, Object> config = new HashMap<>();

            // 邮件配置
            Map<String, Object> emailConfig = new HashMap<>();
            emailConfig.put("enabled", true);
            emailConfig.put("default_recipients", new String[]{"<EMAIL>"});
            emailConfig.put("templates", Map.of(
                "test_execution", "测试执行完成通知模板",
                "test_plan", "测试计划完成通知模板",
                "system_alert", "系统告警通知模板"
            ));
            config.put("email", emailConfig);

            // 钉钉配置
            Map<String, Object> dingTalkConfig = new HashMap<>();
            dingTalkConfig.put("enabled", false);
            dingTalkConfig.put("webhook_url", "");
            dingTalkConfig.put("secret", "");
            config.put("dingtalk", dingTalkConfig);

            // 企业微信配置
            Map<String, Object> wechatConfig = new HashMap<>();
            wechatConfig.put("enabled", false);
            wechatConfig.put("webhook_url", "");
            config.put("wechat_work", wechatConfig);

            // Slack配置
            Map<String, Object> slackConfig = new HashMap<>();
            slackConfig.put("enabled", false);
            slackConfig.put("webhook_url", "");
            config.put("slack", slackConfig);

            return Result.success(config);
        } catch (Exception e) {
            log.error("获取通知配置失败: {}", e.getMessage());
            return Result.error("获取通知配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新通知配置
     */
    @PutMapping("/config")
    public Result<String> updateNotificationConfig(@Valid @RequestBody UpdateNotificationConfigRequest request) {
        try {
            // TODO: 实现通知配置更新逻辑
            log.info("更新通知配置: {}", request);
            return Result.success("通知配置更新成功");
        } catch (Exception e) {
            log.error("更新通知配置失败: {}", e.getMessage());
            return Result.error("更新通知配置失败: " + e.getMessage());
        }
    }

    /**
     * 测试邮件通知
     */
    @PostMapping("/test/email")
    public Result<String> testEmailNotification(@Valid @RequestBody TestEmailNotificationRequest request) {
        try {
            Map<String, Object> variables = new HashMap<>();
            variables.put("test_user", "测试用户");
            variables.put("test_time", System.currentTimeMillis());

            notificationService.sendEmailNotification(
                request.getEmail(),
                "邮件通知测试",
                "这是一封测试邮件，用于验证邮件通知配置是否正确。\n\n发送时间: ${test_time}\n发送人: ${test_user}",
                variables
            );

            return Result.success("测试邮件发送成功");
        } catch (Exception e) {
            log.error("测试邮件通知失败: {}", e.getMessage());
            return Result.error("测试邮件通知失败: " + e.getMessage());
        }
    }

    /**
     * 测试钉钉通知
     */
    @PostMapping("/test/dingtalk")
    public Result<String> testDingTalkNotification(@Valid @RequestBody TestDingTalkNotificationRequest request) {
        try {
            Map<String, Object> variables = new HashMap<>();
            variables.put("test_time", System.currentTimeMillis());

            notificationService.sendDingTalkNotification(
                request.getWebhookUrl(),
                "钉钉通知测试\n\n这是一条测试消息，用于验证钉钉通知配置是否正确。\n\n发送时间: ${test_time}",
                variables
            );

            return Result.success("测试钉钉通知发送成功");
        } catch (Exception e) {
            log.error("测试钉钉通知失败: {}", e.getMessage());
            return Result.error("测试钉钉通知失败: " + e.getMessage());
        }
    }

    /**
     * 测试企业微信通知
     */
    @PostMapping("/test/wechat-work")
    public Result<String> testWeChatWorkNotification(@Valid @RequestBody TestWeChatWorkNotificationRequest request) {
        try {
            Map<String, Object> variables = new HashMap<>();
            variables.put("test_time", System.currentTimeMillis());

            notificationService.sendWeChatWorkNotification(
                request.getWebhookUrl(),
                "企业微信通知测试\n\n这是一条测试消息，用于验证企业微信通知配置是否正确。\n\n发送时间: ${test_time}",
                variables
            );

            return Result.success("测试企业微信通知发送成功");
        } catch (Exception e) {
            log.error("测试企业微信通知失败: {}", e.getMessage());
            return Result.error("测试企业微信通知失败: " + e.getMessage());
        }
    }

    /**
     * 测试Slack通知
     */
    @PostMapping("/test/slack")
    public Result<String> testSlackNotification(@Valid @RequestBody TestSlackNotificationRequest request) {
        try {
            Map<String, Object> variables = new HashMap<>();
            variables.put("test_time", System.currentTimeMillis());

            notificationService.sendSlackNotification(
                request.getWebhookUrl(),
                "Slack通知测试\n\n这是一条测试消息，用于验证Slack通知配置是否正确。\n\n发送时间: ${test_time}",
                variables
            );

            return Result.success("测试Slack通知发送成功");
        } catch (Exception e) {
            log.error("测试Slack通知失败: {}", e.getMessage());
            return Result.error("测试Slack通知失败: " + e.getMessage());
        }
    }

    /**
     * 获取通知历史
     */
    @GetMapping("/history")
    public Result<Map<String, Object>> getNotificationHistory(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status) {

        try {
            // TODO: 实现通知历史查询逻辑

            Map<String, Object> result = new HashMap<>();
            result.put("total", 156);
            result.put("page", page);
            result.put("size", size);
            result.put("records", new Object[]{
                Map.of("id", 1, "type", "email", "recipient", "<EMAIL>", "subject", "测试执行完成", "status", "success", "sent_at", System.currentTimeMillis()),
                Map.of("id", 2, "type", "dingtalk", "recipient", "钉钉群", "subject", "系统告警", "status", "success", "sent_at", System.currentTimeMillis() - 3600000)
            });

            return Result.success(result);
        } catch (Exception e) {
            log.error("获取通知历史失败: {}", e.getMessage());
            return Result.error("获取通知历史失败: " + e.getMessage());
        }
    }

    @Data
    public static class UpdateNotificationConfigRequest {
        private Map<String, Object> email;
        private Map<String, Object> dingtalk;
        private Map<String, Object> wechatWork;
        private Map<String, Object> slack;
    }

    @Data
    public static class TestEmailNotificationRequest {
        @NotBlank(message = "邮箱地址不能为空")
        private String email;
    }

    @Data
    public static class TestDingTalkNotificationRequest {
        @NotBlank(message = "Webhook URL不能为空")
        private String webhookUrl;

        private String secret;
    }

    @Data
    public static class TestWeChatWorkNotificationRequest {
        @NotBlank(message = "Webhook URL不能为空")
        private String webhookUrl;
    }

    @Data
    public static class TestSlackNotificationRequest {
        @NotBlank(message = "Webhook URL不能为空")
        private String webhookUrl;
    }
}
