server:
  port: 8880

api:
  prefix: /api

spring:
  application:
    name: api-test-platform
  main:
    allow-circular-references: true
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************
    username: root
    password: cyy3344274520
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin123
        allow: 127.0.0.1

  # Redis配置
  data:
    redis:
      host: **************
      port: 6379
      password: Gm2pnXSjxJ5mfJZA
      database: 0
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  # 邮件配置
  mail:
    host: smtp.qq.com
    port: 587
    username: 
    password: 
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      update-strategy: NOT_NULL
      insert-strategy: NOT_NULL
      select-strategy: NOT_EMPTY
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.cyy.newcyyapitestspringboot.entity

# 日志配置
logging:
  level:
    com.cyy.newcyyapitestspringboot: DEBUG
    com.baomidou.mybatisplus: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: logs/api-test-platform.log

# 应用配置
app:
  # JWT配置
  jwt:
    secret: api-test-platform-jwt-secret-key-2024
    expiration: 7200 # 2小时（秒）
    refresh-expiration: 604800 # 7天（秒）
  
  # 文件存储配置
  file:
    upload-path: ./uploads/
    max-size: 104857600 # 100MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,csv,json,xml,txt
  
  # 系统配置
  system:
    name: API测试平台
    version: 1.0.0
    description: 专业的API接口自动化测试平台
  
  # 安全配置
  security:
    password-min-length: 8
    max-login-attempts: 5
    lock-duration: 300000 # 5分钟

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env
  endpoint:
    health:
      show-details: when-authorized
  info:
    env:
      enabled: true

# 跨域配置
cors:
  allowed-origins: http://localhost:5174,http://127.0.0.1:5174
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true
  max-age: 3600
