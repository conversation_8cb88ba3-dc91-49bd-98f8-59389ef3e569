2025-06-26 09:30:35.905 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 21.0.1 with PID 7384 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 09:30:35.910 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 09:30:35.911 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 09:30:38.168 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 09:30:38.171 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 09:30:38.227 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 36 ms. Found 0 Redis repository interfaces.
2025-06-26 09:30:39.339 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8880 (http)
2025-06-26 09:30:39.366 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 09:30:39.367 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 09:30:39.442 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 09:30:39.443 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3471 ms
2025-06-26 09:30:39.716 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 09:30:40.129 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@2e929182, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 09:30:40.131 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 09:30:41.512 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration': Injection of autowired dependencies failed
2025-06-26 09:30:41.514 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-06-26 09:30:41.515 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-26 09:30:41.547 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-26 09:30:41.563 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration': Injection of autowired dependencies failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:515)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1459)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.cyy.newcyyapitestspringboot.NewCyyApiTestSpringbootApplication.main(NewCyyApiTestSpringbootApplication.java:17)
Caused by: java.lang.RuntimeException: Could not postProcess org.springframework.security.config.annotation.web.builders.WebSecurity@402f61f5 of type class org.springframework.security.config.annotation.web.builders.WebSecurity
	at org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor.postProcess(AutowireBeanFactoryObjectPostProcessor.java:71)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration.setFilterChainProxySecurityConfigurer(WebSecurityConfiguration.java:159)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredMethodElement.inject(AutowiredAnnotationBeanPostProcessor.java:854)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mvcHandlerMappingIntrospectorRequestTransformer': Cannot resolve reference to bean 'mvcHandlerMappingIntrospector' while setting constructor argument
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:377)
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveValueIfNecessary(BeanDefinitionValueResolver.java:135)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveConstructorArguments(ConstructorResolver.java:691)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:206)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:227)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1598)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1556)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:563)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$1.getIfUnique(DefaultListableBeanFactory.java:489)
	at org.springframework.security.config.annotation.web.builders.WebSecurity.setApplicationContext(WebSecurity.java:433)
	at org.springframework.context.support.ApplicationContextAwareProcessor.invokeAwareInterfaces(ApplicationContextAwareProcessor.java:110)
	at org.springframework.context.support.ApplicationContextAwareProcessor.postProcessBeforeInitialization(ApplicationContextAwareProcessor.java:85)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:419)
	at org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor.initializeBeanIfNeeded(AutowireBeanFactoryObjectPostProcessor.java:98)
	at org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor.postProcess(AutowireBeanFactoryObjectPostProcessor.java:67)
	... 25 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'fileStorageController' method 
com.cyy.newcyyapitestspringboot.controller.FileStorageController#uploadFile(MultipartFile, String, Long)
to {POST [/api/files/upload]}: There is already 'fileController' bean method
com.cyy.newcyyapitestspringboot.controller.FileController#uploadFile(MultipartFile, Long, String, Long) mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1826)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:365)
	... 48 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'fileStorageController' method 
com.cyy.newcyyapitestspringboot.controller.FileStorageController#uploadFile(MultipartFile, String, Long)
to {POST [/api/files/upload]}: There is already 'fileController' bean method
com.cyy.newcyyapitestspringboot.controller.FileController#uploadFile(MultipartFile, Long, String, Long) mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1826)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:747)
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1426)
	at org.springframework.beans.factory.BeanFactoryUtils.beansOfTypeIncludingAncestors(BeanFactoryUtils.java:372)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.initHandlerMappings(HandlerMappingIntrospector.java:159)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.afterPropertiesSet(HandlerMappingIntrospector.java:146)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1873)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1822)
	... 55 common frames omitted
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'fileStorageController' method 
com.cyy.newcyyapitestspringboot.controller.FileStorageController#uploadFile(MultipartFile, String, Long)
to {POST [/api/files/upload]}: There is already 'fileController' bean method
com.cyy.newcyyapitestspringboot.controller.FileController#uploadFile(MultipartFile, Long, String, Long) mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:676)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:637)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:331)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:507)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:84)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$2(AbstractHandlerMethodMapping.java:298)
	at java.base/java.util.LinkedHashMap.forEach(LinkedHashMap.java:986)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:296)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:265)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:224)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:212)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1873)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1822)
	... 68 common frames omitted
2025-06-26 09:33:52.701 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 17.0.6 with PID 24332 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 09:33:52.706 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 09:33:52.708 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 09:33:54.188 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 09:33:54.193 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 09:33:54.264 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 49 ms. Found 0 Redis repository interfaces.
2025-06-26 09:33:55.027 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8880 (http)
2025-06-26 09:33:55.042 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 09:33:55.042 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 09:33:55.140 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 09:33:55.140 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2354 ms
2025-06-26 09:33:55.307 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 09:33:55.537 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@31d4b3e8, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 09:33:55.539 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 09:33:57.306 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 09:33:57.363 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 8ae47fc9-9cb6-4763-869c-174ef48d951f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 09:33:57.374 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 09:33:57.514 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 09:33:58.104 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8880 (http) with context path '/'
2025-06-26 09:33:58.127 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 6.673 seconds (process running for 7.088)
2025-06-26 09:33:58.165 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8880
	External: 	http://************:8880
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 09:35:20.129 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 17.0.6 with PID 2008 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 09:35:20.132 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 09:35:20.132 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 09:35:21.266 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 09:35:21.269 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 09:35:21.322 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-06-26 09:35:21.552 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Could not enhance configuration class [com.cyy.newcyyapitestspringboot.config.SecurityConfig]. Consider declaring @Configuration(proxyBeanMethods=false) without inter-bean references between @Bean methods on the configuration class, avoiding the need for CGLIB enhancement.
2025-06-26 09:35:21.573 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-26 09:35:21.601 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Could not enhance configuration class [com.cyy.newcyyapitestspringboot.config.SecurityConfig]. Consider declaring @Configuration(proxyBeanMethods=false) without inter-bean references between @Bean methods on the configuration class, avoiding the need for CGLIB enhancement.
	at org.springframework.context.annotation.ConfigurationClassEnhancer.enhance(ConfigurationClassEnhancer.java:136)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.enhanceConfigurationClasses(ConfigurationClassPostProcessor.java:539)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanFactory(ConfigurationClassPostProcessor.java:311)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:363)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:153)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:609)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.cyy.newcyyapitestspringboot.NewCyyApiTestSpringbootApplication.main(NewCyyApiTestSpringbootApplication.java:17)
Caused by: org.springframework.cglib.core.CodeGenerationException: java.lang.IllegalStateException-->Unable to load cache item
	at org.springframework.context.annotation.ConfigurationClassEnhancer.createClass(ConfigurationClassEnhancer.java:202)
	at org.springframework.context.annotation.ConfigurationClassEnhancer.enhance(ConfigurationClassEnhancer.java:128)
	... 13 common frames omitted
Caused by: java.lang.IllegalStateException: Unable to load cache item
	at org.springframework.cglib.core.internal.LoadingCache.createEntry(LoadingCache.java:75)
	at org.springframework.cglib.core.internal.LoadingCache.get(LoadingCache.java:34)
	at org.springframework.cglib.core.AbstractClassGenerator$ClassLoaderData.get(AbstractClassGenerator.java:129)
	at org.springframework.cglib.core.AbstractClassGenerator.create(AbstractClassGenerator.java:321)
	at org.springframework.cglib.proxy.Enhancer.createHelper(Enhancer.java:562)
	at org.springframework.cglib.proxy.Enhancer.createClass(Enhancer.java:407)
	at org.springframework.context.annotation.ConfigurationClassEnhancer.createClass(ConfigurationClassEnhancer.java:198)
	... 14 common frames omitted
Caused by: java.lang.NoClassDefFoundError: JwtAuthenticationEntryPoint
	at java.base/java.lang.Class.getDeclaredConstructors0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredConstructors(Class.java:3373)
	at java.base/java.lang.Class.getDeclaredConstructors(Class.java:2555)
	at org.springframework.cglib.proxy.Enhancer.generateClass(Enhancer.java:655)
	at org.springframework.cglib.transform.TransformingClassGenerator.generateClass(TransformingClassGenerator.java:35)
	at org.springframework.cglib.core.DefaultGeneratorStrategy.generate(DefaultGeneratorStrategy.java:26)
	at org.springframework.cglib.core.ClassLoaderAwareGeneratorStrategy.lambda$new$0(ClassLoaderAwareGeneratorStrategy.java:41)
	at org.springframework.cglib.core.ClassLoaderAwareGeneratorStrategy.generate(ClassLoaderAwareGeneratorStrategy.java:76)
	at org.springframework.cglib.core.AbstractClassGenerator.generate(AbstractClassGenerator.java:370)
	at org.springframework.cglib.proxy.Enhancer.generate(Enhancer.java:575)
	at org.springframework.cglib.core.AbstractClassGenerator$ClassLoaderData.lambda$new$1(AbstractClassGenerator.java:107)
	at org.springframework.cglib.core.internal.LoadingCache.lambda$createEntry$1(LoadingCache.java:52)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.springframework.cglib.core.internal.LoadingCache.createEntry(LoadingCache.java:57)
	... 20 common frames omitted
Caused by: java.lang.ClassNotFoundException: JwtAuthenticationEntryPoint
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
	... 34 common frames omitted
2025-06-26 09:37:47.971 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 17.0.6 with PID 27224 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 09:37:47.972 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 09:37:47.973 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 09:37:48.966 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 09:37:48.971 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 09:37:49.021 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-06-26 09:37:49.208 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Could not enhance configuration class [com.cyy.newcyyapitestspringboot.config.SecurityConfig]. Consider declaring @Configuration(proxyBeanMethods=false) without inter-bean references between @Bean methods on the configuration class, avoiding the need for CGLIB enhancement.
2025-06-26 09:37:49.226 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-26 09:37:49.242 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Could not enhance configuration class [com.cyy.newcyyapitestspringboot.config.SecurityConfig]. Consider declaring @Configuration(proxyBeanMethods=false) without inter-bean references between @Bean methods on the configuration class, avoiding the need for CGLIB enhancement.
	at org.springframework.context.annotation.ConfigurationClassEnhancer.enhance(ConfigurationClassEnhancer.java:136)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.enhanceConfigurationClasses(ConfigurationClassPostProcessor.java:539)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanFactory(ConfigurationClassPostProcessor.java:311)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:363)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:153)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:609)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.cyy.newcyyapitestspringboot.NewCyyApiTestSpringbootApplication.main(NewCyyApiTestSpringbootApplication.java:17)
Caused by: org.springframework.cglib.core.CodeGenerationException: java.lang.IllegalStateException-->Unable to load cache item
	at org.springframework.context.annotation.ConfigurationClassEnhancer.createClass(ConfigurationClassEnhancer.java:202)
	at org.springframework.context.annotation.ConfigurationClassEnhancer.enhance(ConfigurationClassEnhancer.java:128)
	... 13 common frames omitted
Caused by: java.lang.IllegalStateException: Unable to load cache item
	at org.springframework.cglib.core.internal.LoadingCache.createEntry(LoadingCache.java:75)
	at org.springframework.cglib.core.internal.LoadingCache.get(LoadingCache.java:34)
	at org.springframework.cglib.core.AbstractClassGenerator$ClassLoaderData.get(AbstractClassGenerator.java:129)
	at org.springframework.cglib.core.AbstractClassGenerator.create(AbstractClassGenerator.java:321)
	at org.springframework.cglib.proxy.Enhancer.createHelper(Enhancer.java:562)
	at org.springframework.cglib.proxy.Enhancer.createClass(Enhancer.java:407)
	at org.springframework.context.annotation.ConfigurationClassEnhancer.createClass(ConfigurationClassEnhancer.java:198)
	... 14 common frames omitted
Caused by: java.lang.NoClassDefFoundError: JwtAuthenticationEntryPoint
	at java.base/java.lang.Class.getDeclaredConstructors0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredConstructors(Class.java:3373)
	at java.base/java.lang.Class.getDeclaredConstructors(Class.java:2555)
	at org.springframework.cglib.proxy.Enhancer.generateClass(Enhancer.java:655)
	at org.springframework.cglib.transform.TransformingClassGenerator.generateClass(TransformingClassGenerator.java:35)
	at org.springframework.cglib.core.DefaultGeneratorStrategy.generate(DefaultGeneratorStrategy.java:26)
	at org.springframework.cglib.core.ClassLoaderAwareGeneratorStrategy.lambda$new$0(ClassLoaderAwareGeneratorStrategy.java:41)
	at org.springframework.cglib.core.ClassLoaderAwareGeneratorStrategy.generate(ClassLoaderAwareGeneratorStrategy.java:76)
	at org.springframework.cglib.core.AbstractClassGenerator.generate(AbstractClassGenerator.java:370)
	at org.springframework.cglib.proxy.Enhancer.generate(Enhancer.java:575)
	at org.springframework.cglib.core.AbstractClassGenerator$ClassLoaderData.lambda$new$1(AbstractClassGenerator.java:107)
	at org.springframework.cglib.core.internal.LoadingCache.lambda$createEntry$1(LoadingCache.java:52)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.springframework.cglib.core.internal.LoadingCache.createEntry(LoadingCache.java:57)
	... 20 common frames omitted
Caused by: java.lang.ClassNotFoundException: JwtAuthenticationEntryPoint
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
	... 34 common frames omitted
2025-06-26 09:42:13.309 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 17.0.6 with PID 30508 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 09:42:13.508 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 09:42:13.560 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 09:42:15.869 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 09:42:15.874 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 09:42:15.955 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 39 ms. Found 0 Redis repository interfaces.
2025-06-26 09:42:16.924 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8880 (http)
2025-06-26 09:42:16.953 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 09:42:16.957 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 09:42:17.118 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 09:42:17.124 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3324 ms
2025-06-26 09:42:17.427 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 09:42:17.788 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@49122853, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 09:42:17.791 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 09:42:19.576 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration': Injection of autowired dependencies failed
2025-06-26 09:42:19.579 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-06-26 09:42:19.582 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-26 09:42:19.665 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-26 09:42:19.680 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration': Injection of autowired dependencies failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:515)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1459)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.cyy.newcyyapitestspringboot.NewCyyApiTestSpringbootApplication.main(NewCyyApiTestSpringbootApplication.java:17)
Caused by: java.lang.RuntimeException: Could not postProcess org.springframework.security.config.annotation.web.builders.WebSecurity@5c997de8 of type class org.springframework.security.config.annotation.web.builders.WebSecurity
	at org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor.postProcess(AutowireBeanFactoryObjectPostProcessor.java:71)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration.setFilterChainProxySecurityConfigurer(WebSecurityConfiguration.java:159)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredMethodElement.inject(AutowiredAnnotationBeanPostProcessor.java:854)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mvcHandlerMappingIntrospectorRequestTransformer': Cannot resolve reference to bean 'mvcHandlerMappingIntrospector' while setting constructor argument
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:377)
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveValueIfNecessary(BeanDefinitionValueResolver.java:135)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveConstructorArguments(ConstructorResolver.java:691)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:206)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:227)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1598)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1556)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:563)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$1.getIfUnique(DefaultListableBeanFactory.java:489)
	at org.springframework.security.config.annotation.web.builders.WebSecurity.setApplicationContext(WebSecurity.java:433)
	at org.springframework.context.support.ApplicationContextAwareProcessor.invokeAwareInterfaces(ApplicationContextAwareProcessor.java:110)
	at org.springframework.context.support.ApplicationContextAwareProcessor.postProcessBeforeInitialization(ApplicationContextAwareProcessor.java:85)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:419)
	at org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor.initializeBeanIfNeeded(AutowireBeanFactoryObjectPostProcessor.java:98)
	at org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor.postProcess(AutowireBeanFactoryObjectPostProcessor.java:67)
	... 27 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'fileStorageController' method 
com.cyy.newcyyapitestspringboot.controller.FileStorageController#uploadFile(MultipartFile, String, Long)
to {POST [/api/files/upload]}: There is already 'fileController' bean method
com.cyy.newcyyapitestspringboot.controller.FileController#uploadFile(MultipartFile, Long, String, Long) mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1826)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:365)
	... 50 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'fileStorageController' method 
com.cyy.newcyyapitestspringboot.controller.FileStorageController#uploadFile(MultipartFile, String, Long)
to {POST [/api/files/upload]}: There is already 'fileController' bean method
com.cyy.newcyyapitestspringboot.controller.FileController#uploadFile(MultipartFile, Long, String, Long) mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1826)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:747)
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1426)
	at org.springframework.beans.factory.BeanFactoryUtils.beansOfTypeIncludingAncestors(BeanFactoryUtils.java:372)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.initHandlerMappings(HandlerMappingIntrospector.java:159)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.afterPropertiesSet(HandlerMappingIntrospector.java:146)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1873)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1822)
	... 57 common frames omitted
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'fileStorageController' method 
com.cyy.newcyyapitestspringboot.controller.FileStorageController#uploadFile(MultipartFile, String, Long)
to {POST [/api/files/upload]}: There is already 'fileController' bean method
com.cyy.newcyyapitestspringboot.controller.FileController#uploadFile(MultipartFile, Long, String, Long) mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:676)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:637)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:331)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:507)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:84)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$2(AbstractHandlerMethodMapping.java:298)
	at java.base/java.util.LinkedHashMap.forEach(LinkedHashMap.java:721)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:296)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:265)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:224)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:212)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1873)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1822)
	... 70 common frames omitted
2025-06-26 09:43:43.102 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 17.0.6 with PID 24476 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 09:43:43.107 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 09:43:43.108 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 09:43:44.094 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 09:43:44.097 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 09:43:44.142 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
2025-06-26 09:43:44.753 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8880 (http)
2025-06-26 09:43:44.765 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 09:43:44.765 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 09:43:44.840 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 09:43:44.841 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1696 ms
2025-06-26 09:43:44.999 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 09:43:45.201 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@31ab4859, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 09:43:45.203 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 09:43:46.755 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 09:43:46.808 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: fbbd79d9-9480-4e50-b4fa-9ef89b8dcfef

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 09:43:46.816 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 09:43:46.930 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 09:43:47.397 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-06-26 09:43:47.409 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-06-26 09:43:47.472 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-26 09:43:47.485 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8880 was already in use.

Action:

Identify and stop the process that's listening on port 8880 or configure this application to listen on another port.

2025-06-26 09:44:55.591 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 17.0.6 with PID 23236 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 09:44:55.595 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 09:44:55.596 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 09:44:56.738 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 09:44:56.741 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 09:44:56.787 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Redis repository interfaces.
2025-06-26 09:44:57.415 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8881 (http)
2025-06-26 09:44:57.427 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 09:44:57.428 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 09:44:57.510 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 09:44:57.511 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1875 ms
2025-06-26 09:44:57.660 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 09:44:57.861 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@740c4868, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 09:44:57.862 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 09:44:59.711 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 09:44:59.767 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 2923e638-ac4b-4e38-a268-d52df6552f76

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 09:44:59.777 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 09:44:59.875 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 09:45:00.299 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8881 (http) with context path '/'
2025-06-26 09:45:00.316 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 5.168 seconds (process running for 5.536)
2025-06-26 09:45:00.349 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8881
	External: 	http://************:8881
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 09:46:46.867 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 17.0.6 with PID 17212 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 09:46:46.869 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 09:46:46.870 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 09:46:47.979 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 09:46:47.982 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 09:46:48.028 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Redis repository interfaces.
2025-06-26 09:46:48.980 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8881 (http)
2025-06-26 09:46:49.001 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 09:46:49.032 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 09:46:49.183 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 09:46:49.185 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2271 ms
2025-06-26 09:46:49.414 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 09:46:49.653 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@61957d9c, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 09:46:49.655 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 09:46:51.502 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 09:46:51.544 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 0ce5f0fc-0bf0-48dc-a349-ab54c3e65612

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 09:46:51.554 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 09:46:51.670 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 09:46:52.213 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8881 (http) with context path '/'
2025-06-26 09:46:52.229 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 5.844 seconds (process running for 6.248)
2025-06-26 09:46:52.264 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8881
	External: 	http://************:8881
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 09:51:09.193 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication v0.0.1-SNAPSHOT using Java 17.0.6 with PID 20920 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\new-cyy-apiTest-springboot-0.0.1-SNAPSHOT.jar started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 09:51:09.201 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 09:51:09.215 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 09:51:12.186 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 09:51:12.191 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 09:51:12.259 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2025-06-26 09:51:13.647 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8881 (http)
2025-06-26 09:51:13.678 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 09:51:13.679 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 09:51:13.782 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 09:51:13.783 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4443 ms
2025-06-26 09:51:14.094 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 09:51:14.701 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@64f3991e, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 09:51:14.717 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 09:51:18.519 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 09:51:18.607 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 8a286e55-9b6c-4ad7-9394-607c2c48ab2b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 09:51:18.633 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 09:51:18.844 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 09:51:19.607 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8881 (http) with context path '/'
2025-06-26 09:51:19.636 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 11.734 seconds (process running for 12.751)
2025-06-26 09:51:19.709 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8881
	External: 	http://************:8881
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 09:52:34.982 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 17.0.6 with PID 29768 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 09:52:34.984 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 09:52:34.985 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 09:52:36.290 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 09:52:36.293 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 09:52:36.348 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
2025-06-26 09:52:37.082 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8881 (http)
2025-06-26 09:52:37.102 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 09:52:37.102 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 09:52:37.213 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 09:52:37.214 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2178 ms
2025-06-26 09:52:37.784 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 09:52:38.030 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@3d6778d5, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 09:52:38.032 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 09:52:41.933 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 09:52:42.047 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 4e9fcefb-08c7-42fc-b65d-94239a73db9f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 09:52:42.066 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 09:52:42.264 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 09:52:43.697 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8881 (http) with context path '/'
2025-06-26 09:52:43.815 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 9.503 seconds (process running for 10.223)
2025-06-26 09:52:43.896 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8881
	External: 	http://************:8881
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 09:53:59.650 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 21.0.1 with PID 28864 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 09:53:59.651 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 09:53:59.652 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 09:54:02.075 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 09:54:02.078 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 09:54:02.140 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
2025-06-26 09:54:03.128 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8881 (http)
2025-06-26 09:54:03.149 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 09:54:03.150 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 09:54:03.217 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 09:54:03.218 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3400 ms
2025-06-26 09:54:03.517 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 09:54:04.110 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@63b4b9c6, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 09:54:04.112 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 09:54:06.389 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 09:54:06.452 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: ea5f3e82-4e40-408d-9e2f-398c03ae5534

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 09:54:06.462 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 09:54:06.598 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 09:54:07.656 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8881 (http) with context path '/'
2025-06-26 09:54:07.689 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 10.075 seconds (process running for 11.543)
2025-06-26 09:54:07.698 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8881
	External: 	http://************:8881
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 09:54:08.113 [RMI TCP Connection(3)-************] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 09:54:08.113 [RMI TCP Connection(3)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 09:54:08.115 [RMI TCP Connection(3)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-26 09:54:08.182 [RMI TCP Connection(2)-************] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-26 09:54:08.893 [RMI TCP Connection(2)-************] WARN  o.s.boot.actuate.mail.MailHealthIndicator - Mail health check failed
jakarta.mail.AuthenticationFailedException: failed to connect, no password specified?
	at jakarta.mail.Service.connect(Service.java:379)
	at org.springframework.mail.javamail.JavaMailSenderImpl.connectTransport(JavaMailSenderImpl.java:480)
	at org.springframework.mail.javamail.JavaMailSenderImpl.testConnection(JavaMailSenderImpl.java:360)
	at org.springframework.boot.actuate.mail.MailHealthIndicator.doHealthCheck(MailHealthIndicator.java:52)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:82)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:76)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:66)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:281)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:803)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:802)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1472)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1310)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1405)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:714)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:598)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:844)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:721)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:720)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 10:09:20.172 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 17.0.6 with PID 30592 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 10:09:20.175 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 10:09:20.176 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 10:09:21.443 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 10:09:21.446 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 10:09:21.493 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2025-06-26 10:09:22.202 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8881 (http)
2025-06-26 10:09:22.217 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 10:09:22.217 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 10:09:22.302 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 10:09:22.303 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2086 ms
2025-06-26 10:09:22.460 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 10:09:22.695 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@722b2728, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 10:09:22.696 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 10:09:24.519 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 10:09:24.557 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 2a703656-efe9-42cd-81a6-f27684fc0f08

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 10:09:24.564 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 10:09:24.663 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 10:09:25.122 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-06-26 10:09:25.133 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-06-26 10:09:25.203 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-26 10:09:25.217 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8881 was already in use.

Action:

Identify and stop the process that's listening on port 8881 or configure this application to listen on another port.

2025-06-26 10:09:46.822 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-26 10:09:46.849 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-26 10:09:47.022 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-26 10:09:47.034 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-26 10:10:46.653 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 17.0.6 with PID 14120 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 10:10:46.658 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 10:10:46.659 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 10:10:47.761 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 10:10:47.763 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 10:10:47.814 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
2025-06-26 10:10:48.558 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8882 (http)
2025-06-26 10:10:48.572 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 10:10:48.573 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 10:10:48.684 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 10:10:48.685 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1987 ms
2025-06-26 10:10:48.871 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 10:10:49.134 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@5f3f3d00, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 10:10:49.136 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 10:10:50.897 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 10:10:50.944 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 4561a7d3-c186-41e5-b5ca-093691d6e69c

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 10:10:50.954 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 10:10:51.090 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 10:10:51.695 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8882 (http) with context path '/'
2025-06-26 10:10:51.716 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 5.58 seconds (process running for 6.014)
2025-06-26 10:10:51.806 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8882
	External: 	http://************:8882
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 10:17:02.471 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 17.0.6 with PID 24236 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 10:17:02.474 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 10:17:02.475 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 10:17:03.565 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 10:17:03.567 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 10:17:03.618 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-06-26 10:17:04.282 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8882 (http)
2025-06-26 10:17:04.296 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 10:17:04.296 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 10:17:04.380 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 10:17:04.381 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1871 ms
2025-06-26 10:17:04.547 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 10:17:04.753 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@722b2728, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 10:17:04.754 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 10:17:06.445 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 10:17:06.486 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 0905f496-bbdf-4a7c-b0bc-ccbb0851ef55

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 10:17:06.496 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 10:17:06.593 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 10:17:07.213 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8882 (http) with context path '/'
2025-06-26 10:17:07.239 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 5.288 seconds (process running for 5.778)
2025-06-26 10:17:07.316 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8882
	External: 	http://************:8882
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 10:17:40.146 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-26 10:17:40.413 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-26 10:17:40.444 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-06-26 10:18:47.343 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 21.0.1 with PID 29020 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 10:18:47.345 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 10:18:47.345 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 10:18:49.972 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 10:18:49.977 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 10:18:50.056 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 48 ms. Found 0 Redis repository interfaces.
2025-06-26 10:18:51.168 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8882 (http)
2025-06-26 10:18:51.190 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 10:18:51.191 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 10:18:51.258 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 10:18:51.259 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3845 ms
2025-06-26 10:18:51.462 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 10:18:51.714 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@68821f6, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 10:18:51.716 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 10:18:53.625 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 10:18:53.697 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 16d87b3c-7137-4581-b7d2-99d0bb9b00b3

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 10:18:53.708 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 10:18:53.875 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 10:18:54.562 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8882 (http) with context path '/'
2025-06-26 10:18:54.575 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 7.886 seconds (process running for 9.225)
2025-06-26 10:18:54.581 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8882
	External: 	http://************:8882
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 10:26:12.799 [http-nio-8882-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 10:26:12.804 [http-nio-8882-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 10:26:12.869 [http-nio-8882-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 65 ms
2025-06-26 10:26:12.958 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /api/projects/1/test-datasets?page=1&size=20&keyword=&type=
2025-06-26 10:26:13.062 [http-nio-8882-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/projects/1/test-datasets?page=1&size=20&keyword=&type=
2025-06-26 10:26:13.075 [http-nio-8882-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 10:26:13.094 [http-nio-8882-exec-2] ERROR c.c.n.security.JwtAuthenticationEntryPoint - 未授权访问: Full authentication is required to access this resource
2025-06-26 10:29:55.612 [http-nio-8882-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /api/projects/1/mock-rules?page=1&size=20&keyword=&method=&status=
2025-06-26 10:29:55.633 [http-nio-8882-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/projects/1/mock-rules?page=1&size=20&keyword=&method=&status=
2025-06-26 10:29:55.636 [http-nio-8882-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 10:29:55.642 [http-nio-8882-exec-5] ERROR c.c.n.security.JwtAuthenticationEntryPoint - 未授权访问: Full authentication is required to access this resource
2025-06-26 10:38:19.578 [http-nio-8882-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/projects/1/mock-rules?page=1&size=20&keyword=&method=&status=
2025-06-26 10:38:19.611 [http-nio-8882-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 10:38:19.631 [http-nio-8882-exec-7] ERROR c.c.n.security.JwtAuthenticationEntryPoint - 未授权访问: Full authentication is required to access this resource
2025-06-26 10:47:23.487 [http-nio-8882-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /api/auth/login
2025-06-26 10:47:23.519 [http-nio-8882-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 10:47:23.529 [http-nio-8882-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 10:47:23.605 [http-nio-8882-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 10:47:24.308 [http-nio-8882-exec-10] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-26 10:47:25.135 [http-nio-8882-exec-10] ERROR c.c.n.controller.AuthController - 登录失败: 密码错误
2025-06-26 10:48:30.155 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-26 10:48:30.171 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-26 10:48:30.234 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-26 10:48:30.250 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-26 10:48:40.033 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 21.0.1 with PID 29568 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 10:48:40.035 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 10:48:40.036 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 10:48:41.480 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 10:48:41.483 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 10:48:41.527 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Redis repository interfaces.
2025-06-26 10:48:42.446 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8882 (http)
2025-06-26 10:48:42.469 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 10:48:42.470 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 10:48:42.546 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 10:48:42.546 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2455 ms
2025-06-26 10:48:42.829 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 10:48:43.175 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@3d98729a, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 10:48:43.177 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 10:48:45.181 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 10:48:45.232 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: ef5d60a7-997a-4eb9-a22c-20f4795fd219

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 10:48:45.241 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 10:48:45.356 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 10:48:46.132 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8882 (http) with context path '/'
2025-06-26 10:48:46.146 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 6.791 seconds (process running for 8.455)
2025-06-26 10:48:46.155 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8882
	External: 	http://************:8882
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 10:48:49.297 [http-nio-8882-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 10:48:49.297 [http-nio-8882-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 10:48:49.299 [http-nio-8882-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-26 10:48:49.336 [http-nio-8882-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 10:48:49.386 [http-nio-8882-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 10:48:49.401 [http-nio-8882-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 10:51:48.493 [http-nio-8882-exec-2] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-26 10:51:48.871 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-26 10:51:49.417 [http-nio-8882-exec-2] ERROR c.c.n.controller.AuthController - 登录失败: 密码错误
2025-06-26 10:51:49.483 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-26 10:51:49.499 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-26 10:51:49.504 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-26 10:51:56.627 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 21.0.1 with PID 27676 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 10:51:56.630 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 10:51:56.631 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 10:51:57.986 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 10:51:57.989 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 10:51:58.038 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Redis repository interfaces.
2025-06-26 10:51:59.081 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8882 (http)
2025-06-26 10:51:59.099 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 10:51:59.100 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 10:51:59.169 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 10:51:59.170 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2463 ms
2025-06-26 10:51:59.436 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 10:51:59.740 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@6ffbf0ac, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 10:51:59.742 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 10:52:01.447 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 10:52:01.492 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: f4293c53-656b-45c8-9deb-cb9e726e2c4a

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 10:52:01.500 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 10:52:01.619 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 10:52:02.295 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8882 (http) with context path '/'
2025-06-26 10:52:02.308 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 6.625 seconds (process running for 8.148)
2025-06-26 10:52:02.317 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8882
	External: 	http://************:8882
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 10:52:16.834 [http-nio-8882-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 10:52:16.834 [http-nio-8882-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 10:52:16.835 [http-nio-8882-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-26 10:52:16.863 [http-nio-8882-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 10:52:16.894 [http-nio-8882-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 10:52:16.905 [http-nio-8882-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 10:52:17.282 [http-nio-8882-exec-2] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-26 10:53:27.198 [http-nio-8882-exec-2] ERROR c.c.n.controller.AuthController - 登录失败: 密码错误
2025-06-26 10:53:35.808 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 10:53:35.810 [http-nio-8882-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 10:53:35.812 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 10:53:41.272 [http-nio-8882-exec-1] ERROR c.c.n.controller.AuthController - 登录失败: 密码错误
2025-06-26 10:55:25.573 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-26 10:55:25.580 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-26 10:55:25.608 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-26 10:55:25.616 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-26 10:55:35.411 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 21.0.1 with PID 29052 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 10:55:35.413 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 10:55:35.414 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 10:55:36.876 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 10:55:36.882 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 10:55:36.947 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 41 ms. Found 0 Redis repository interfaces.
2025-06-26 10:55:37.876 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8882 (http)
2025-06-26 10:55:37.896 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 10:55:37.896 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 10:55:37.953 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 10:55:37.953 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2482 ms
2025-06-26 10:55:38.186 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 10:55:38.585 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@7b2d58e6, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 10:55:38.587 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 10:55:40.439 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 10:55:40.490 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 66dabf3f-e817-4d73-aeb2-3eb17ec8cc14

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 10:55:40.504 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 10:55:40.657 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 10:55:41.599 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8882 (http) with context path '/'
2025-06-26 10:55:41.618 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 6.889 seconds (process running for 8.552)
2025-06-26 10:55:41.631 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8882
	External: 	http://************:8882
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 10:55:50.806 [http-nio-8882-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 10:55:50.807 [http-nio-8882-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 10:55:50.809 [http-nio-8882-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-26 10:55:50.841 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 10:55:50.884 [http-nio-8882-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 10:55:50.898 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 10:55:51.230 [http-nio-8882-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-26 10:56:07.626 [http-nio-8882-exec-1] INFO  c.c.n.service.impl.UserServiceImpl - $2a$10$aZxrc7Y7lj3vO3LWQztkl.Ga9vVLdQIBKwLvdAtQEkvSKVJlY5LMm
2025-06-26 10:56:55.299 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-26 10:56:56.126 [http-nio-8882-exec-1] ERROR c.c.n.controller.AuthController - 登录失败: 密码错误
2025-06-26 10:56:56.161 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-26 10:56:56.179 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-26 10:56:56.184 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-26 10:57:06.097 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 21.0.1 with PID 32016 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 10:57:06.099 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 10:57:06.099 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 10:57:07.311 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 10:57:07.314 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 10:57:07.365 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-06-26 10:57:08.120 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8882 (http)
2025-06-26 10:57:08.135 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 10:57:08.135 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 10:57:08.184 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 10:57:08.184 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2029 ms
2025-06-26 10:57:08.356 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 10:57:08.592 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@5eee3da9, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 10:57:08.594 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 10:57:10.241 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 10:57:10.285 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: ef86747e-263a-4d06-8137-aa5ddab0fc73

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 10:57:10.293 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 10:57:10.401 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 10:57:11.180 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8882 (http) with context path '/'
2025-06-26 10:57:11.193 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 5.737 seconds (process running for 7.098)
2025-06-26 10:57:11.201 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8882
	External: 	http://************:8882
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 10:57:13.509 [http-nio-8882-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 10:57:13.509 [http-nio-8882-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 10:57:13.512 [http-nio-8882-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-26 10:57:13.637 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 10:57:13.711 [http-nio-8882-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 10:57:13.726 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 10:57:14.132 [http-nio-8882-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-26 10:59:16.757 [http-nio-8882-exec-1] ERROR c.c.n.controller.AuthController - 登录失败: 密码错误
2025-06-26 10:59:17.036 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-26 10:59:17.045 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-26 10:59:17.073 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-26 10:59:17.079 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-26 10:59:27.894 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 21.0.1 with PID 26132 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 10:59:27.896 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 10:59:27.897 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 10:59:29.625 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 10:59:29.628 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 10:59:29.674 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2025-06-26 10:59:30.465 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8882 (http)
2025-06-26 10:59:30.479 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 10:59:30.479 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 10:59:30.530 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 10:59:30.531 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2561 ms
2025-06-26 10:59:30.728 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 10:59:31.023 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@4bc21e34, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 10:59:31.025 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 10:59:32.769 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 10:59:32.818 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 497471a8-c689-4384-b7e4-bbaaaa9a7f9d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 10:59:32.829 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 10:59:32.933 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 10:59:33.603 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8882 (http) with context path '/'
2025-06-26 10:59:33.619 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 6.411 seconds (process running for 7.907)
2025-06-26 10:59:33.630 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8882
	External: 	http://************:8882
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 10:59:38.839 [http-nio-8882-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 10:59:38.839 [http-nio-8882-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 10:59:38.841 [http-nio-8882-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-26 10:59:38.871 [http-nio-8882-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 10:59:38.903 [http-nio-8882-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 10:59:38.913 [http-nio-8882-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 10:59:39.320 [http-nio-8882-exec-2] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-26 11:00:47.369 [http-nio-8882-exec-2] INFO  c.c.n.service.impl.UserServiceImpl - 加密后的密码: $2a$10$m6WhQayHJZc1x8m2UbJe8O7DD3XCmj2sTlIIf5Ahaz4RXAN.pNt8m
2025-06-26 11:00:48.450 [http-nio-8882-exec-2] ERROR c.c.n.controller.AuthController - 登录失败: 密码错误
2025-06-26 11:01:01.473 [http-nio-8882-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:01:01.475 [http-nio-8882-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:01:01.476 [http-nio-8882-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:01:01.696 [http-nio-8882-exec-4] INFO  c.c.n.service.impl.UserServiceImpl - 加密后的密码: $2a$10$UE1Zd6GfcwoOmiUzUZASEOPeTAWq3U5kXWVAd1NoTPAXlWYgAmBcu
2025-06-26 11:01:01.970 [http-nio-8882-exec-4] ERROR c.c.n.controller.AuthController - 登录失败: The signing key's size is 296 bits which is not secure enough for the HS512 algorithm.  The JWT JWA Specification (RFC 7518, Section 3.2) states that keys used with HS512 MUST have a size >= 512 bits (the key size must be greater than or equal to the hash output size).  Consider using the io.jsonwebtoken.security.Keys class's 'secretKeyFor(SignatureAlgorithm.HS512)' method to create a key guaranteed to be secure enough for HS512.  See https://tools.ietf.org/html/rfc7518#section-3.2 for more information.
2025-06-26 11:01:21.295 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-26 11:01:21.299 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-26 11:01:21.321 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-26 11:01:21.327 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-26 11:01:26.618 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 21.0.1 with PID 9076 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 11:01:26.620 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 11:01:26.620 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 11:01:27.831 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 11:01:27.833 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 11:01:27.881 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-06-26 11:01:28.534 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8882 (http)
2025-06-26 11:01:28.546 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 11:01:28.546 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 11:01:28.597 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 11:01:28.597 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1936 ms
2025-06-26 11:01:28.741 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 11:01:28.989 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@3ba46845, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 11:01:28.991 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 11:01:30.611 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 11:01:30.648 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: a354c244-cbee-4c7b-b5bf-b03a7857d393

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 11:01:30.654 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 11:01:30.741 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 11:01:31.234 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8882 (http) with context path '/'
2025-06-26 11:01:31.246 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 5.208 seconds (process running for 5.98)
2025-06-26 11:01:31.253 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8882
	External: 	http://************:8882
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 11:01:31.429 [RMI TCP Connection(1)-************] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 11:01:31.432 [RMI TCP Connection(1)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 11:01:31.436 [RMI TCP Connection(1)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-26 11:01:31.495 [RMI TCP Connection(2)-************] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-26 11:01:32.280 [RMI TCP Connection(2)-************] WARN  o.s.boot.actuate.mail.MailHealthIndicator - Mail health check failed
jakarta.mail.AuthenticationFailedException: failed to connect, no password specified?
	at jakarta.mail.Service.connect(Service.java:379)
	at org.springframework.mail.javamail.JavaMailSenderImpl.connectTransport(JavaMailSenderImpl.java:480)
	at org.springframework.mail.javamail.JavaMailSenderImpl.testConnection(JavaMailSenderImpl.java:360)
	at org.springframework.boot.actuate.mail.MailHealthIndicator.doHealthCheck(MailHealthIndicator.java:52)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:82)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:76)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:66)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:281)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:803)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:802)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1472)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1310)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1405)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:714)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:598)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:844)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:721)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:720)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:01:41.100 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:01:41.128 [http-nio-8882-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:01:41.133 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:01:41.474 [http-nio-8882-exec-1] INFO  c.c.n.service.impl.UserServiceImpl - 加密后的密码: $2a$10$4zPfgMKkeqqhs7GE.vHJI.eMCun8qa.NUf1kFC.5HKVd1b9TcawhC
2025-06-26 11:01:41.733 [http-nio-8882-exec-1] ERROR c.c.n.controller.AuthController - 登录失败: The signing key's size is 296 bits which is not secure enough for the HS512 algorithm.  The JWT JWA Specification (RFC 7518, Section 3.2) states that keys used with HS512 MUST have a size >= 512 bits (the key size must be greater than or equal to the hash output size).  Consider using the io.jsonwebtoken.security.Keys class's 'secretKeyFor(SignatureAlgorithm.HS512)' method to create a key guaranteed to be secure enough for HS512.  See https://tools.ietf.org/html/rfc7518#section-3.2 for more information.
2025-06-26 11:09:10.478 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-26 11:09:10.487 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-26 11:09:10.551 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-26 11:09:10.564 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-26 11:09:19.008 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 21.0.1 with PID 26724 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 11:09:19.011 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 11:09:19.012 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 11:09:20.627 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 11:09:20.629 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 11:09:20.714 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 69 ms. Found 0 Redis repository interfaces.
2025-06-26 11:09:21.386 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8882 (http)
2025-06-26 11:09:21.402 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 11:09:21.402 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 11:09:21.451 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 11:09:21.451 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2378 ms
2025-06-26 11:09:21.591 [main] INFO  com.cyy.newcyyapitestspringboot.util.JwtUtil - JWT signing key initialized successfully
2025-06-26 11:09:21.664 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 11:09:21.896 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@430d3021, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 11:09:21.898 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 11:09:23.534 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 11:09:23.580 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: b0af734d-f6cc-4af1-8889-1dae7ab5249e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 11:09:23.586 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 11:09:23.686 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 11:09:24.290 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8882 (http) with context path '/'
2025-06-26 11:09:24.301 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 5.832 seconds (process running for 6.813)
2025-06-26 11:09:24.308 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8882
	External: 	http://************:8882
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 11:09:24.809 [RMI TCP Connection(3)-************] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 11:09:24.809 [RMI TCP Connection(3)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 11:09:24.811 [RMI TCP Connection(3)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-26 11:09:24.879 [RMI TCP Connection(4)-************] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-26 11:09:25.576 [RMI TCP Connection(4)-************] WARN  o.s.boot.actuate.mail.MailHealthIndicator - Mail health check failed
jakarta.mail.AuthenticationFailedException: failed to connect, no password specified?
	at jakarta.mail.Service.connect(Service.java:379)
	at org.springframework.mail.javamail.JavaMailSenderImpl.connectTransport(JavaMailSenderImpl.java:480)
	at org.springframework.mail.javamail.JavaMailSenderImpl.testConnection(JavaMailSenderImpl.java:360)
	at org.springframework.boot.actuate.mail.MailHealthIndicator.doHealthCheck(MailHealthIndicator.java:52)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:82)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:76)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:66)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:281)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:803)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:802)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1472)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1310)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1405)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:714)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:598)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:844)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:721)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:720)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:09:32.651 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:09:32.683 [http-nio-8882-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:09:32.690 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:09:33.090 [http-nio-8882-exec-1] INFO  c.c.n.service.impl.UserServiceImpl - 加密后的密码: $2a$10$nHH6JecmVs4QYZowvp9od.98FFJUEc5DQNIMTqwIZ3ufFu4LWCyga
2025-06-26 11:12:38.916 [http-nio-8882-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:12:38.918 [http-nio-8882-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:12:38.919 [http-nio-8882-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:12:39.124 [http-nio-8882-exec-3] INFO  c.c.n.service.impl.UserServiceImpl - 加密后的密码: $2a$10$oKaAU1ZdOGehxixD5t7OF.jc0s/GzBsX8tr3CK2rPX9ln2gsAo6Fa
2025-06-26 11:12:44.435 [http-nio-8882-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /api/auth/logout
2025-06-26 11:12:44.442 [http-nio-8882-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/logout
2025-06-26 11:12:44.534 [http-nio-8882-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/logout
2025-06-26 11:14:00.630 [http-nio-8882-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:14:00.632 [http-nio-8882-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:14:00.633 [http-nio-8882-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:14:00.848 [http-nio-8882-exec-8] INFO  c.c.n.service.impl.UserServiceImpl - 加密后的密码: $2a$10$rF3eIghqlWlnO8UKFM6j6.xJZnTUhA3oNsuthqX0bSaqWEcY2VFcO
2025-06-26 11:14:02.890 [http-nio-8882-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/logout
2025-06-26 11:14:02.929 [http-nio-8882-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/logout
2025-06-26 11:14:13.904 [http-nio-8882-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:14:13.905 [http-nio-8882-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:14:13.905 [http-nio-8882-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:14:14.048 [http-nio-8882-exec-10] INFO  c.c.n.service.impl.UserServiceImpl - 加密后的密码: $2a$10$PFvXUczGj19PEAFmHgA0Qe3/B9abaDnwbuX0O8nvKQL/6rJck7bse
2025-06-26 11:14:18.607 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/logout
2025-06-26 11:14:18.670 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/logout
2025-06-26 11:18:35.644 [http-nio-8882-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:18:35.648 [http-nio-8882-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:18:35.649 [http-nio-8882-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:18:35.866 [http-nio-8882-exec-3] INFO  c.c.n.service.impl.UserServiceImpl - 加密后的密码: $2a$10$SMpI1qExUupom5rv8RX1HeXx3Jb.qPk72n8fL/ElXDLQgSLJ9WLAq
2025-06-26 11:18:38.239 [http-nio-8882-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/logout
2025-06-26 11:18:38.280 [http-nio-8882-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/logout
2025-06-26 11:23:05.520 [http-nio-8882-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:23:05.534 [http-nio-8882-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:23:05.537 [http-nio-8882-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:23:05.801 [http-nio-8882-exec-7] INFO  c.c.n.service.impl.UserServiceImpl - 加密后的密码: $2a$10$.iXKW4ZqBvS.1qnMeqzIp.eLhmAnzd/w5QAad12Gr41DMK.OCHZdu
2025-06-26 11:23:10.267 [http-nio-8882-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /api/auth/login
2025-06-26 11:23:10.272 [http-nio-8882-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:23:10.336 [http-nio-8882-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:23:10.503 [http-nio-8882-exec-8] INFO  c.c.n.service.impl.UserServiceImpl - 加密后的密码: $2a$10$4r4U.3QhwuzFM6l6cSxswu7N31VAWowD.3VNA/mKj7AMXmf/Bt7Nm
2025-06-26 11:23:11.662 [http-nio-8882-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:23:11.738 [http-nio-8882-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:23:11.869 [http-nio-8882-exec-2] INFO  c.c.n.service.impl.UserServiceImpl - 加密后的密码: $2a$10$5HTtZ2jKlBJAEqDRnBsO7uzFgcJcknz47X6boju6I3z/9OAxbQSia
2025-06-26 11:23:12.867 [http-nio-8882-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:23:12.905 [http-nio-8882-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:23:13.058 [http-nio-8882-exec-10] INFO  c.c.n.service.impl.UserServiceImpl - 加密后的密码: $2a$10$sgfTs6yD5jZXNIVatQI4UO1gYZQUDh5Z/f3HqpXgkivue60xpUzaO
2025-06-26 11:25:33.718 [http-nio-8882-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:25:33.756 [http-nio-8882-exec-5] ERROR com.cyy.newcyyapitestspringboot.util.JwtUtil - Invalid JWT token: JWT expired 133721 milliseconds ago at 2025-06-26T03:23:20.000Z. Current time: 2025-06-26T03:25:33.721Z. Allowed clock skew: 0 milliseconds.
2025-06-26 11:25:33.757 [http-nio-8882-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:25:33.757 [http-nio-8882-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:25:33.965 [http-nio-8882-exec-5] INFO  c.c.n.service.impl.UserServiceImpl - 加密后的密码: $2a$10$QIehq8Anbvf3W8iV3QeyQebCquXhOp1Dfm3Xh.kg8rFWxLlUfBrD6
2025-06-26 11:42:03.085 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 21.0.1 with PID 12044 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 11:42:03.090 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 11:42:03.091 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 11:42:04.819 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 11:42:04.823 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 11:42:04.872 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2025-06-26 11:42:05.707 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8882 (http)
2025-06-26 11:42:05.731 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 11:42:05.731 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 11:42:05.818 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 11:42:05.818 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2673 ms
2025-06-26 11:42:06.072 [main] INFO  com.cyy.newcyyapitestspringboot.util.JwtUtil - JWT signing key initialized successfully
2025-06-26 11:42:06.166 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 11:42:06.606 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@5a7e81, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 11:42:06.608 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 11:42:09.568 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 11:42:09.697 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: bc081511-d010-42ac-b95f-52f705a482ea

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 11:42:09.758 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 11:42:10.074 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 11:42:10.973 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8882 (http) with context path '/'
2025-06-26 11:42:10.993 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 8.776 seconds (process running for 10.58)
2025-06-26 11:42:11.001 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8882
	External: 	http://************:8882
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 11:42:11.959 [RMI TCP Connection(2)-************] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 11:42:11.960 [RMI TCP Connection(2)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 11:42:11.963 [RMI TCP Connection(2)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-26 11:42:12.143 [RMI TCP Connection(1)-************] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-26 11:42:12.997 [RMI TCP Connection(1)-************] WARN  o.s.boot.actuate.mail.MailHealthIndicator - Mail health check failed
jakarta.mail.AuthenticationFailedException: failed to connect, no password specified?
	at jakarta.mail.Service.connect(Service.java:379)
	at org.springframework.mail.javamail.JavaMailSenderImpl.connectTransport(JavaMailSenderImpl.java:480)
	at org.springframework.mail.javamail.JavaMailSenderImpl.testConnection(JavaMailSenderImpl.java:360)
	at org.springframework.boot.actuate.mail.MailHealthIndicator.doHealthCheck(MailHealthIndicator.java:52)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:82)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:76)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:66)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:281)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:803)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:802)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1472)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1310)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1405)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:714)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:598)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:844)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:721)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:720)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:42:55.966 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /api/auth/login
2025-06-26 11:42:56.034 [http-nio-8882-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:42:56.044 [http-nio-8882-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:42:56.057 [http-nio-8882-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:42:56.457 [http-nio-8882-exec-2] ERROR c.c.n.controller.AuthController - 登录失败: 用户不存在
2025-06-26 11:43:32.998 [http-nio-8882-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:43:32.999 [http-nio-8882-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:43:33.000 [http-nio-8882-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:43:33.211 [http-nio-8882-exec-3] INFO  c.c.n.service.impl.UserServiceImpl - 加密后的密码: $2a$10$L3O/csXwvieL7f.sZFuVyOL6elAyAAZIwLLBi3KAjrMbMuu1m8nMu
2025-06-26 11:43:41.342 [http-nio-8882-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /api/auth/logout
2025-06-26 11:43:41.348 [http-nio-8882-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/logout
2025-06-26 11:43:41.394 [http-nio-8882-exec-4] ERROR com.cyy.newcyyapitestspringboot.util.JwtUtil - Invalid JWT token: JWT expired 1392 milliseconds ago at 2025-06-26T03:43:40.000Z. Current time: 2025-06-26T03:43:41.392Z. Allowed clock skew: 0 milliseconds.
2025-06-26 11:43:41.394 [http-nio-8882-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:43:41.395 [http-nio-8882-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/logout
2025-06-26 11:44:52.217 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-26 11:44:52.230 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-26 11:44:52.280 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-26 11:44:52.298 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-26 11:47:31.518 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Starting NewCyyApiTestSpringbootApplication using Java 21.0.1 with PID 19876 (D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot\target\classes started by cyy in D:\Autometer-Api\new-cyy-apiTest\new-cyy-apiTest-springboot)
2025-06-26 11:47:31.526 [main] DEBUG c.c.n.NewCyyApiTestSpringbootApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-26 11:47:31.527 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 11:47:34.684 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 11:47:34.688 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 11:47:34.741 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-06-26 11:47:35.536 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8882 (http)
2025-06-26 11:47:35.553 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 11:47:35.554 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 11:47:35.620 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 11:47:35.621 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3788 ms
2025-06-26 11:47:35.844 [main] INFO  com.cyy.newcyyapitestspringboot.util.JwtUtil - JWT signing key initialized successfully
2025-06-26 11:47:35.902 [main] DEBUG c.c.n.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-26 11:47:36.129 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@e706aa, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-26 11:47:36.130 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-06-26 11:47:37.724 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-06-26 11:47:37.778 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 53622b54-46f1-48aa-9e20-ea991833ac45

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-26 11:47:37.789 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-26 11:47:37.921 [main] DEBUG o.s.security.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-26 11:47:38.489 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8882 (http) with context path '/'
2025-06-26 11:47:38.500 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - Started NewCyyApiTestSpringbootApplication in 12.407 seconds (process running for 13.438)
2025-06-26 11:47:38.506 [main] INFO  c.c.n.NewCyyApiTestSpringbootApplication - 
----------------------------------------------------------
	Application 'api-test-platform' is running! Access URLs:
	Local: 		http://localhost:8882
	External: 	http://************:8882
	Profile(s): 	[]
----------------------------------------------------------
2025-06-26 11:47:39.140 [RMI TCP Connection(4)-************] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 11:47:39.141 [RMI TCP Connection(4)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 11:47:39.143 [RMI TCP Connection(4)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-26 11:47:39.279 [RMI TCP Connection(1)-************] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-26 11:47:39.999 [RMI TCP Connection(1)-************] WARN  o.s.boot.actuate.mail.MailHealthIndicator - Mail health check failed
jakarta.mail.AuthenticationFailedException: failed to connect, no password specified?
	at jakarta.mail.Service.connect(Service.java:379)
	at org.springframework.mail.javamail.JavaMailSenderImpl.connectTransport(JavaMailSenderImpl.java:480)
	at org.springframework.mail.javamail.JavaMailSenderImpl.testConnection(JavaMailSenderImpl.java:360)
	at org.springframework.boot.actuate.mail.MailHealthIndicator.doHealthCheck(MailHealthIndicator.java:52)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:82)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:76)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:66)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:281)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:803)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:802)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1472)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1310)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1405)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:714)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:598)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:844)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:721)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:720)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:48:35.697 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:48:35.747 [http-nio-8882-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:48:35.756 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:48:36.125 [http-nio-8882-exec-1] ERROR c.c.n.controller.AuthController - 登录失败: 用户不存在
2025-06-26 11:48:40.143 [http-nio-8882-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:48:40.145 [http-nio-8882-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:48:40.146 [http-nio-8882-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:48:40.198 [http-nio-8882-exec-2] ERROR c.c.n.controller.AuthController - 登录失败: 用户不存在
2025-06-26 11:48:58.366 [http-nio-8882-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:48:58.367 [http-nio-8882-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:48:58.368 [http-nio-8882-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:48:58.525 [http-nio-8882-exec-3] INFO  c.c.n.service.impl.UserServiceImpl - 加密后的密码: $2a$10$zYt8vVmWwGOf/o0mJG9vP.hGn9C2pkrGnZ8yognL064PnKGz8HqGu
2025-06-26 11:51:25.500 [http-nio-8882-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /api/projects
2025-06-26 11:51:25.507 [http-nio-8882-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/projects
2025-06-26 11:51:25.542 [http-nio-8882-exec-6] ERROR com.cyy.newcyyapitestspringboot.util.JwtUtil - Invalid JWT token: JWT expired 140541 milliseconds ago at 2025-06-26T03:49:05.000Z. Current time: 2025-06-26T03:51:25.541Z. Allowed clock skew: 0 milliseconds.
2025-06-26 11:51:25.543 [http-nio-8882-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:51:25.546 [http-nio-8882-exec-6] ERROR c.c.n.security.JwtAuthenticationEntryPoint - 未授权访问: Full authentication is required to access this resource
2025-06-26 11:51:25.556 [http-nio-8882-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /api/auth/refresh
2025-06-26 11:51:25.560 [http-nio-8882-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/refresh
2025-06-26 11:51:25.563 [http-nio-8882-exec-8] ERROR com.cyy.newcyyapitestspringboot.util.JwtUtil - Invalid JWT token: JWT expired 140562 milliseconds ago at 2025-06-26T03:49:05.000Z. Current time: 2025-06-26T03:51:25.562Z. Allowed clock skew: 0 milliseconds.
2025-06-26 11:51:25.563 [http-nio-8882-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:51:25.564 [http-nio-8882-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/refresh
2025-06-26 11:51:25.586 [http-nio-8882-exec-8] ERROR c.c.n.exception.GlobalExceptionHandler - 参数校验失败: 刷新令牌不能为空
2025-06-26 11:51:30.942 [http-nio-8882-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-26 11:51:30.944 [http-nio-8882-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:51:30.946 [http-nio-8882-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-26 11:51:31.163 [http-nio-8882-exec-9] INFO  c.c.n.service.impl.UserServiceImpl - 加密后的密码: $2a$10$6lsQvPrTSTVe3N9LR51jfuLU9hhu0u2XSk9ePKlWU11CmJkjbFYpq
2025-06-26 11:52:05.284 [http-nio-8882-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/projects
2025-06-26 11:52:05.286 [http-nio-8882-exec-10] ERROR com.cyy.newcyyapitestspringboot.util.JwtUtil - Invalid JWT token: JWT expired 27286 milliseconds ago at 2025-06-26T03:51:38.000Z. Current time: 2025-06-26T03:52:05.286Z. Allowed clock skew: 0 milliseconds.
2025-06-26 11:52:05.287 [http-nio-8882-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:52:05.287 [http-nio-8882-exec-10] ERROR c.c.n.security.JwtAuthenticationEntryPoint - 未授权访问: Full authentication is required to access this resource
2025-06-26 11:52:05.301 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/refresh
2025-06-26 11:52:05.303 [http-nio-8882-exec-1] ERROR com.cyy.newcyyapitestspringboot.util.JwtUtil - Invalid JWT token: JWT expired 27303 milliseconds ago at 2025-06-26T03:51:38.000Z. Current time: 2025-06-26T03:52:05.303Z. Allowed clock skew: 0 milliseconds.
2025-06-26 11:52:05.303 [http-nio-8882-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-26 11:52:05.303 [http-nio-8882-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/refresh
2025-06-26 11:52:05.305 [http-nio-8882-exec-1] ERROR c.c.n.exception.GlobalExceptionHandler - 参数校验失败: 刷新令牌不能为空
