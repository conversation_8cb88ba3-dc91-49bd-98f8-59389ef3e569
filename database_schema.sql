-- =====================================================
-- 接口自动化测试平台 - MySQL 8.0 数据库建表脚本
-- 生成时间: 2024-01-15
-- 版本: 1.0.0
-- =====================================================

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库
CREATE DATABASE IF NOT EXISTS interface_test DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE interface_test;

-- =====================================================
-- 1. 用户管理模块
-- =====================================================

-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    real_name VARCHAR(50) COMMENT '真实姓名',
    avatar VARCHAR(255) COMMENT '头像URL',
    role ENUM('admin', 'developer', 'tester') DEFAULT 'tester' COMMENT '角色',
    status TINYINT DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email)
) COMMENT='用户表';

-- 操作日志表
CREATE TABLE operation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    user_id BIGINT NOT NULL COMMENT '操作用户ID',
    action VARCHAR(50) NOT NULL COMMENT '操作动作',
    resource_type VARCHAR(50) NOT NULL COMMENT '资源类型',
    resource_id BIGINT COMMENT '资源ID',
    resource_name VARCHAR(255) COMMENT '资源名称',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    request_data JSON COMMENT '请求数据',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource_type (resource_type),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT='操作日志表';

-- =====================================================
-- 2. 项目管理模块
-- =====================================================

-- 项目表
CREATE TABLE projects (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '项目ID',
    name VARCHAR(100) NOT NULL COMMENT '项目名称',
    description TEXT COMMENT '项目描述',
    owner_id BIGINT NOT NULL COMMENT '负责人ID',
    status TINYINT DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    api_count INT DEFAULT 0 COMMENT '接口总数',
    case_count INT DEFAULT 0 COMMENT '用例总数',
    last_test_result JSON COMMENT '最近执行结果',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_owner_id (owner_id),
    INDEX idx_name (name),
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE RESTRICT
) COMMENT='项目表';

-- 项目成员表
CREATE TABLE project_members (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role ENUM('admin', 'developer', 'tester') DEFAULT 'tester' COMMENT '项目角色',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    UNIQUE KEY uk_project_user (project_id, user_id),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT='项目成员表';

-- 环境配置表
CREATE TABLE environments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '环境ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    name VARCHAR(50) NOT NULL COMMENT '环境名称',
    base_url VARCHAR(255) NOT NULL COMMENT '基础URL',
    global_variables JSON COMMENT '全局变量',
    global_headers JSON COMMENT '全局请求头',
    auth_config JSON COMMENT '认证配置',
    is_default TINYINT DEFAULT 0 COMMENT '是否默认环境',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_project_id (project_id),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
) COMMENT='环境配置表';

-- =====================================================
-- 3. 接口管理模块
-- =====================================================

-- 接口分组表
CREATE TABLE api_groups (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分组ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    name VARCHAR(100) NOT NULL COMMENT '分组名称',
    description TEXT COMMENT '分组描述',
    parent_id BIGINT COMMENT '父分组ID',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_project_id (project_id),
    INDEX idx_parent_id (parent_id),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES api_groups(id) ON DELETE CASCADE
) COMMENT='接口分组表';

-- 接口表
CREATE TABLE apis (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '接口ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    group_id BIGINT COMMENT '分组ID',
    name VARCHAR(200) NOT NULL COMMENT '接口名称',
    method ENUM('GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS') NOT NULL COMMENT '请求方法',
    path VARCHAR(500) NOT NULL COMMENT '接口路径',
    description TEXT COMMENT '接口描述',
    tags JSON COMMENT '标签',
    status TINYINT DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    request_headers JSON COMMENT '请求头定义',
    query_params JSON COMMENT 'Query参数定义',
    path_params JSON COMMENT 'Path参数定义',
    request_body JSON COMMENT '请求体定义',
    response_definition JSON COMMENT '响应定义',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_project_id (project_id),
    INDEX idx_group_id (group_id),
    INDEX idx_method_path (method, path),
    INDEX idx_creator_id (creator_id),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES api_groups(id) ON DELETE SET NULL,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT
) COMMENT='接口表';

-- =====================================================
-- 4. 系统配置模块
-- =====================================================

-- 系统配置表
CREATE TABLE system_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    config_category ENUM('basic', 'security', 'feature', 'storage', 'notification') DEFAULT 'basic' COMMENT '配置分类',
    description TEXT COMMENT '配置描述',
    is_public TINYINT DEFAULT 0 COMMENT '是否公开(1:公开 0:私有)',
    is_readonly TINYINT DEFAULT 0 COMMENT '是否只读(1:只读 0:可编辑)',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_key (config_key),
    INDEX idx_config_category (config_category)
) COMMENT='系统配置表';

-- 文件存储表
CREATE TABLE file_storage (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文件ID',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    stored_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    file_hash VARCHAR(64) COMMENT '文件哈希值',
    uploader_id BIGINT NOT NULL COMMENT '上传者ID',
    project_id BIGINT COMMENT '关联项目ID',
    usage_type ENUM('avatar', 'test_data', 'report', 'attachment') NOT NULL COMMENT '使用类型',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_uploader_id (uploader_id),
    INDEX idx_project_id (project_id),
    INDEX idx_file_hash (file_hash),
    FOREIGN KEY (uploader_id) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
) COMMENT='文件存储表';

-- =====================================================
-- 5. 插入默认数据
-- =====================================================

-- 插入默认系统配置数据
INSERT INTO system_configs (config_key, config_value, config_type, config_category, description, is_public) VALUES
-- 基本设置
('basic.system_name', 'API测试平台', 'string', 'basic', '系统名称', 1),
('basic.system_version', '1.0.0', 'string', 'basic', '系统版本', 1),
('basic.system_logo', '', 'string', 'basic', '系统Logo URL', 1),
('basic.system_description', '专业的API接口自动化测试平台', 'string', 'basic', '系统描述', 1),
('basic.company_name', '', 'string', 'basic', '公司名称', 1),
('basic.contact_email', '', 'string', 'basic', '联系邮箱', 1),
('basic.copyright', '© 2024 Your Company. All rights reserved.', 'string', 'basic', '版权信息', 1),

-- 安全设置
('security.password_min_length', '8', 'number', 'security', '密码最小长度', 0),
('security.password_complexity', 'medium', 'string', 'security', '密码复杂度要求', 0),
('security.login_lock_enabled', 'true', 'boolean', 'security', '是否启用登录失败锁定', 0),
('security.max_login_attempts', '5', 'number', 'security', '最大登录失败次数', 0),
('security.session_timeout', '120', 'number', 'security', '会话超时时间(分钟)', 0),
('security.force_https', 'false', 'boolean', 'security', '是否强制HTTPS', 0),
('security.ip_whitelist', '', 'string', 'security', 'IP白名单', 0),

-- 功能设置
('feature.user_registration_enabled', 'true', 'boolean', 'feature', '是否允许用户注册', 0),
('feature.email_verification_enabled', 'true', 'boolean', 'feature', '是否启用邮箱验证', 0),
('feature.max_projects_per_user', '10', 'number', 'feature', '每个用户最大项目数', 0),
('feature.max_file_size', '10', 'number', 'feature', '最大文件上传大小(MB)', 0),
('feature.allowed_file_types', 'jpg,png,pdf,csv,json,xml', 'string', 'feature', '允许的文件类型', 0),

-- 存储设置
('storage.storage_type', 'local', 'string', 'storage', '存储类型', 0),
('storage.data_retention_days', '90', 'number', 'storage', '数据保留天数', 0),
('storage.local_storage_path', '/var/www/uploads', 'string', 'storage', '本地存储路径', 0),

-- 通知设置
('notification.email_enabled', 'false', 'boolean', 'notification', '是否启用邮件通知', 0),
('notification.webhook_enabled', 'false', 'boolean', 'notification', '是否启用Webhook通知', 0);

-- 插入默认管理员用户 (密码: admin123)
INSERT INTO users (username, email, password, real_name, role, status) VALUES
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', 'admin', 1);

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示创建的表
SHOW TABLES;

-- 脚本执行完成提示
SELECT 'Database schema created successfully!' as message;

-- 用户表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    real_name VARCHAR(100) COMMENT '真实姓名',
    avatar VARCHAR(500) COMMENT '头像URL',
    role VARCHAR(20) NOT NULL DEFAULT 'user' COMMENT '角色：admin,user',
    status INT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 系统配置表
CREATE TABLE system_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '配置类型：string,number,boolean,json',
    config_category VARCHAR(50) NOT NULL DEFAULT 'basic' COMMENT '配置分类',
    config_desc VARCHAR(500) COMMENT '配置描述',
    is_public INT NOT NULL DEFAULT 0 COMMENT '是否公开：0-否，1-是',
    is_readonly INT NOT NULL DEFAULT 0 COMMENT '是否只读：0-否，1-是',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_key (config_key),
    INDEX idx_config_category (config_category),
    INDEX idx_is_public (is_public),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 项目表
CREATE TABLE projects (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '项目ID',
    name VARCHAR(100) NOT NULL COMMENT '项目名称',
    description TEXT COMMENT '项目描述',
    owner_id BIGINT NOT NULL COMMENT '负责人ID',
    status INT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    INDEX idx_name (name),
    INDEX idx_owner_id (owner_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目表';

-- 接口分组表
CREATE TABLE api_groups (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '分组ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    name VARCHAR(100) NOT NULL COMMENT '分组名称',
    description TEXT COMMENT '分组描述',
    parent_id BIGINT COMMENT '父分组ID',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    INDEX idx_project_id (project_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES api_groups(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接口分组表';

-- 接口表
CREATE TABLE apis (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '接口ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    group_id BIGINT COMMENT '分组ID',
    name VARCHAR(200) NOT NULL COMMENT '接口名称',
    method VARCHAR(10) NOT NULL COMMENT '请求方法',
    path VARCHAR(500) NOT NULL COMMENT '接口路径',
    description TEXT COMMENT '接口描述',
    tags VARCHAR(200) COMMENT '标签',
    request_headers TEXT COMMENT '请求头',
    query_params TEXT COMMENT '查询参数',
    path_params TEXT COMMENT '路径参数',
    request_body TEXT COMMENT '请求体',
    response_definition TEXT COMMENT '响应定义',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    status INT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    INDEX idx_project_id (project_id),
    INDEX idx_group_id (group_id),
    INDEX idx_method (method),
    INDEX idx_creator_id (creator_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES api_groups(id) ON DELETE SET NULL,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接口表';

-- 环境配置表
CREATE TABLE environments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '环境ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    name VARCHAR(100) NOT NULL COMMENT '环境名称',
    base_url VARCHAR(500) NOT NULL COMMENT '基础URL',
    global_variables TEXT COMMENT '全局变量',
    global_headers TEXT COMMENT '全局请求头',
    auth_config TEXT COMMENT '认证配置',
    is_default INT NOT NULL DEFAULT 0 COMMENT '是否默认：0-否，1-是',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    INDEX idx_project_id (project_id),
    INDEX idx_is_default (is_default),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='环境配置表';

-- 测试用例表
CREATE TABLE test_cases (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用例ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    api_id BIGINT COMMENT '接口ID',
    name VARCHAR(200) NOT NULL COMMENT '用例名称',
    description TEXT COMMENT '用例描述',
    priority VARCHAR(20) NOT NULL DEFAULT 'medium' COMMENT '优先级：low,medium,high',
    status INT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    pre_operations TEXT COMMENT '前置操作',
    request_config TEXT COMMENT '请求配置',
    assertions TEXT COMMENT '断言配置',
    post_operations TEXT COMMENT '后置操作',
    data_driven_config TEXT COMMENT '数据驱动配置',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    last_execution_status VARCHAR(20) COMMENT '最后执行状态',
    last_execution_time DATETIME COMMENT '最后执行时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    INDEX idx_project_id (project_id),
    INDEX idx_api_id (api_id),
    INDEX idx_priority (priority),
    INDEX idx_status (status),
    INDEX idx_creator_id (creator_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (api_id) REFERENCES apis(id) ON DELETE SET NULL,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='测试用例表';

-- 测试计划表
CREATE TABLE test_plans (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '计划ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    name VARCHAR(200) NOT NULL COMMENT '计划名称',
    description TEXT COMMENT '计划描述',
    environment_id BIGINT NOT NULL COMMENT '环境ID',
    schedule_type VARCHAR(20) NOT NULL DEFAULT 'manual' COMMENT '调度类型：manual,cron,webhook',
    schedule_config TEXT COMMENT '调度配置',
    notification_config TEXT COMMENT '通知配置',
    retry_config TEXT COMMENT '重试配置',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    status INT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    last_execution_time DATETIME COMMENT '最后执行时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    INDEX idx_project_id (project_id),
    INDEX idx_environment_id (environment_id),
    INDEX idx_schedule_type (schedule_type),
    INDEX idx_creator_id (creator_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (environment_id) REFERENCES environments(id) ON DELETE RESTRICT,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='测试计划表';

-- Mock规则表
CREATE TABLE mock_rules (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '规则ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    name VARCHAR(200) NOT NULL COMMENT '规则名称',
    description TEXT COMMENT '规则描述',
    method VARCHAR(10) NOT NULL COMMENT '请求方法',
    path_pattern VARCHAR(500) NOT NULL COMMENT '路径模式',
    match_conditions TEXT COMMENT '匹配条件',
    response_status INT NOT NULL DEFAULT 200 COMMENT '响应状态码',
    response_headers TEXT COMMENT '响应头',
    response_body TEXT COMMENT '响应体',
    response_delay INT NOT NULL DEFAULT 0 COMMENT '响应延迟(毫秒)',
    is_enabled INT NOT NULL DEFAULT 1 COMMENT '是否启用：0-否，1-是',
    hit_count INT NOT NULL DEFAULT 0 COMMENT '命中次数',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    INDEX idx_project_id (project_id),
    INDEX idx_method (method),
    INDEX idx_is_enabled (is_enabled),
    INDEX idx_creator_id (creator_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Mock规则表';

-- 邮件配置表
CREATE TABLE email_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    name VARCHAR(100) NOT NULL COMMENT '配置名称',
    smtp_host VARCHAR(200) NOT NULL COMMENT 'SMTP服务器',
    smtp_port INT NOT NULL COMMENT 'SMTP端口',
    smtp_username VARCHAR(200) NOT NULL COMMENT 'SMTP用户名',
    smtp_password VARCHAR(200) NOT NULL COMMENT 'SMTP密码',
    from_email VARCHAR(200) NOT NULL COMMENT '发件人邮箱',
    from_name VARCHAR(100) COMMENT '发件人名称',
    use_ssl INT NOT NULL DEFAULT 0 COMMENT '使用SSL：0-否，1-是',
    use_tls INT NOT NULL DEFAULT 0 COMMENT '使用TLS：0-否，1-是',
    is_default INT NOT NULL DEFAULT 0 COMMENT '是否默认：0-否，1-是',
    is_enabled INT NOT NULL DEFAULT 1 COMMENT '是否启用：0-否，1-是',
    test_email VARCHAR(200) COMMENT '测试邮箱',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    INDEX idx_is_default (is_default),
    INDEX idx_is_enabled (is_enabled),
    INDEX idx_creator_id (creator_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件配置表';

-- 测试数据集表
CREATE TABLE test_datasets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '数据集ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    name VARCHAR(200) NOT NULL COMMENT '数据集名称',
    description TEXT COMMENT '数据集描述',
    data_type VARCHAR(20) NOT NULL COMMENT '数据类型：json,csv,database,api',
    data_source VARCHAR(500) COMMENT '数据源',
    data_content LONGTEXT COMMENT '数据内容',
    schema_definition TEXT COMMENT '数据结构定义',
    data_source_config TEXT COMMENT '数据源配置',
    refresh_interval INT COMMENT '刷新间隔(分钟)',
    last_refreshed_at DATETIME COMMENT '最后刷新时间',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    INDEX idx_project_id (project_id),
    INDEX idx_data_type (data_type),
    INDEX idx_creator_id (creator_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='测试数据集表';

-- 性能测试表
CREATE TABLE performance_tests (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '测试ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    name VARCHAR(200) NOT NULL COMMENT '测试名称',
    description TEXT COMMENT '测试描述',
    test_type VARCHAR(20) NOT NULL COMMENT '测试类型：load,stress,spike,volume',
    target_url VARCHAR(500) NOT NULL COMMENT '目标URL',
    concurrent_users INT NOT NULL COMMENT '并发用户数',
    duration INT NOT NULL COMMENT '持续时间(秒)',
    ramp_up_time INT COMMENT '启动时间(秒)',
    test_data TEXT COMMENT '测试数据',
    assertions TEXT COMMENT '断言配置',
    environment_id BIGINT NOT NULL COMMENT '环境ID',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    status INT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    last_execution_time DATETIME COMMENT '最后执行时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    INDEX idx_project_id (project_id),
    INDEX idx_test_type (test_type),
    INDEX idx_environment_id (environment_id),
    INDEX idx_creator_id (creator_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (environment_id) REFERENCES environments(id) ON DELETE RESTRICT,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='性能测试表';

-- CI/CD集成表
CREATE TABLE cicd_integrations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '集成ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    name VARCHAR(200) NOT NULL COMMENT '集成名称',
    description TEXT COMMENT '集成描述',
    platform_type VARCHAR(50) NOT NULL COMMENT '平台类型：jenkins,gitlab,github,azure',
    webhook_url VARCHAR(500) NOT NULL COMMENT 'Webhook URL',
    webhook_secret VARCHAR(200) COMMENT 'Webhook密钥',
    trigger_events VARCHAR(500) COMMENT '触发事件',
    test_plan_ids VARCHAR(500) COMMENT '测试计划ID列表',
    notification_config TEXT COMMENT '通知配置',
    is_enabled INT NOT NULL DEFAULT 1 COMMENT '是否启用：0-否，1-是',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    last_triggered_at DATETIME COMMENT '最后触发时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    INDEX idx_project_id (project_id),
    INDEX idx_platform_type (platform_type),
    INDEX idx_is_enabled (is_enabled),
    INDEX idx_creator_id (creator_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CI/CD集成表';

-- 文件存储表
CREATE TABLE file_storage (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '文件ID',
    original_name VARCHAR(500) NOT NULL COMMENT '原始文件名',
    stored_name VARCHAR(500) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(1000) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    mime_type VARCHAR(200) COMMENT '文件类型',
    file_hash VARCHAR(64) NOT NULL COMMENT '文件哈希',
    uploader_id BIGINT NOT NULL COMMENT '上传人ID',
    project_id BIGINT COMMENT '项目ID',
    usage_type VARCHAR(50) COMMENT '用途类型：avatar,attachment,dataset,report',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    deleted INT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    INDEX idx_file_hash (file_hash),
    INDEX idx_uploader_id (uploader_id),
    INDEX idx_project_id (project_id),
    INDEX idx_usage_type (usage_type),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (uploader_id) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件存储表';

-- 操作日志表
CREATE TABLE operation_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    user_id BIGINT COMMENT '用户ID',
    username VARCHAR(50) COMMENT '用户名',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_desc VARCHAR(500) COMMENT '操作描述',
    resource_type VARCHAR(50) COMMENT '资源类型',
    resource_id VARCHAR(100) COMMENT '资源ID',
    request_method VARCHAR(10) COMMENT '请求方法',
    request_url VARCHAR(1000) COMMENT '请求URL',
    request_params TEXT COMMENT '请求参数',
    response_status INT COMMENT '响应状态码',
    response_time BIGINT COMMENT '响应时间(毫秒)',
    client_ip VARCHAR(50) COMMENT '客户端IP',
    user_agent VARCHAR(1000) COMMENT '用户代理',
    project_id BIGINT COMMENT '项目ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_resource_type (resource_type),
    INDEX idx_project_id (project_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 插入初始数据

-- 插入默认管理员用户
INSERT INTO users (username, email, password, real_name, role, status) VALUES
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '系统管理员', 'admin', 1);

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, config_type, config_category, config_desc, is_public, sort_order) VALUES
('system.name', 'API测试平台', 'string', 'basic', '系统名称', 1, 1),
('system.version', '1.0.0', 'string', 'basic', '系统版本', 1, 2),
('system.description', '基于Spring Boot的API接口自动化测试平台', 'string', 'basic', '系统描述', 1, 3),
('system.logo', '/static/images/logo.png', 'string', 'basic', '系统Logo', 1, 4),
('system.copyright', '© 2024 API测试平台', 'string', 'basic', '版权信息', 1, 5),

('security.password_min_length', '6', 'number', 'security', '密码最小长度', 0, 10),
('security.password_max_length', '20', 'number', 'security', '密码最大长度', 0, 11),
('security.login_max_attempts', '5', 'number', 'security', '登录最大尝试次数', 0, 12),
('security.session_timeout', '86400', 'number', 'security', '会话超时时间(秒)', 0, 13),

('notification.email_enabled', 'true', 'boolean', 'notification', '邮件通知启用', 0, 20),
('notification.dingtalk_enabled', 'false', 'boolean', 'notification', '钉钉通知启用', 0, 21),
('notification.wechat_enabled', 'false', 'boolean', 'notification', '企业微信通知启用', 0, 22),

('test.default_timeout', '30000', 'number', 'test', '默认超时时间(毫秒)', 0, 30),
('test.max_retry_count', '3', 'number', 'test', '最大重试次数', 0, 31),
('test.parallel_execution', 'true', 'boolean', 'test', '并行执行', 0, 32),

('file.max_upload_size', '10485760', 'number', 'file', '最大上传文件大小(字节)', 0, 40),
('file.allowed_extensions', 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,txt,csv,json,xml', 'string', 'file', '允许的文件扩展名', 0, 41);

-- 插入示例项目
INSERT INTO projects (name, description, owner_id) VALUES
('示例项目', '这是一个示例项目，用于演示API测试平台的功能', 1);

-- 插入示例环境
INSERT INTO environments (project_id, name, base_url, is_default) VALUES
(1, '开发环境', 'http://localhost:8080', 1),
(1, '测试环境', 'http://test.example.com', 0),
(1, '生产环境', 'http://api.example.com', 0);

-- 插入示例接口分组
INSERT INTO api_groups (project_id, name, description, sort_order) VALUES
(1, '用户管理', '用户相关接口', 1),
(1, '项目管理', '项目相关接口', 2),
(1, '系统管理', '系统管理接口', 3);

-- 插入示例接口
INSERT INTO apis (project_id, group_id, name, method, path, description, creator_id) VALUES
(1, 1, '用户登录', 'POST', '/api/auth/login', '用户登录接口', 1),
(1, 1, '获取用户信息', 'GET', '/api/auth/me', '获取当前用户信息', 1),
(1, 2, '获取项目列表', 'GET', '/api/projects', '分页获取项目列表', 1),
(1, 2, '创建项目', 'POST', '/api/projects', '创建新项目', 1),
(1, 3, '获取系统配置', 'GET', '/api/admin/system-configs', '获取系统配置', 1);

COMMIT;
